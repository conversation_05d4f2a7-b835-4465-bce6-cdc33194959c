# Guia de Limpeza de Dados para Webhook

## Problema Identificado

Os dados JSON estão sendo codificados múltiplas vezes ao passar pelas diferentes camadas do sistema (Telegram → Banco → Webhook), resultando em escape characters desnecessários que impedem o parsing correto.

## Exemplos dos Problemas

### 1. Dados do Log (linha 32) - Formato Correto
```json
{
    "paciente": "<PERSON>",
    "data_nascimento": "20/09/1973",
    "idade": "51 anos",
    "sexo": "Masculino",
    "convenio": "Bradesco Saúde"
}
```

### 2. Dados do DBLog (linha 22) - Formato Problemático
```json
"{\"callback_data\":\"{\\n\\u00a0\\u00a0\\u00a0 \\\"paciente\\\": \\\"<PERSON>\\\",\\n\\u00a0\\u00a0\\u00a0 \\\"data_nascimento\\\": \\\"20\\\/09\\\/1973\\\"\\n}\"}"
```

### 3. Dados do Banco - Formato Problemático
```json
""{\"paciente\":\"Carlos Jose Gomes da Silva\",\"data_nascimento\":\"20\\\/09\\\/1973\"}"
```

## Solução Implementada

### Classe: `SendDocumentToWebhook`

#### Método Principal: `cleanJsonData($data)`
- Detecta automaticamente o formato dos dados
- Remove escape characters desnecessários
- Normaliza Unicode e caracteres especiais
- Valida o JSON resultante

#### Método Específico: `cleanDatabaseJsonData($data)`
- Otimizado para dados vindos do banco de dados
- Lida com escaping específico do banco
- Converte Unicode escapes corretamente

## Como Usar

### 1. Para Dados Gerais (Telegram/Callback)
```php
$sendWebhook = new SendDocumentToWebhook($telegram);
$sendWebhook->perform($dirtyJsonData);
```

### 2. Para Dados do Banco Especificamente
```php
$sendWebhook = new SendDocumentToWebhook($telegram);
$cleanData = $sendWebhook->cleanDatabaseJsonData($databaseJsonString);
$sendWebhook->perform($cleanData);
```

## Funcionalidades de Limpeza

### 1. **Remoção de Callback Data Structure**
```php
// Entrada: {"callback_data": "{\"paciente\": \"Nome\"}"}
// Saída: {"paciente": "Nome"}
```

### 2. **Correção de Escape Characters**
```php
// Entrada: "{\"paciente\":\"Nome\"}"
// Saída: {"paciente": "Nome"}
```

### 3. **Normalização de Unicode**
```php
// Entrada: "Bradesco Sa\\u00fade"
// Saída: "Bradesco Saúde"
```

### 4. **Remoção de Non-Breaking Spaces**
```php
// Entrada: "\\u00a0\\u00a0\\u00a0\"paciente\""
// Saída: "   \"paciente\""
```

### 5. **Correção de Barras Escapadas**
```php
// Entrada: "20\\\/09\\\/1973"
// Saída: "20/09/1973"
```

## Logs de Debug

O sistema agora inclui logs detalhados para debug:

1. **Dados Originais Recebidos**
2. **Dados Após Limpeza**
3. **Dados Finais Enviados**
4. **Resposta do Webhook**
5. **Erros de Parsing (se houver)**

## Estrutura de Dados Esperada

### Dados Médicos Completos
```json
{
    "paciente": "Carlos Jose Gomes da Silva",
    "data_nascimento": "20/09/1973",
    "idade": "51 anos",
    "sexo": "Masculino",
    "convenio": "Bradesco Saúde",
    "codigo_usuario": "775263019593002",
    "atendimento": "4519301",
    "prontuario": "301618",
    "data_entrada": "26/11/2024 07:45",
    "setor": "HP-Centro Cirúrgico",
    "cirurgia_realizada": "Herniorrafia Inguinal - Unilateral Por",
    "cirurgiao": "Rafael Moraes Tavares",
    "crm_cirurgiao": "5473",
    "anestesista": "Rafaela Nunes Dantas",
    "tipo_anestesia": "Geral",
    "inicio_cirurgia": "26/11/2024 08:30",
    "fim_cirurgia": "",
    "classificacao": "Limpa",
    "tipo_cirurgia": "Eletiva",
    "procedimentos": [
        {
            "codigo": "31009336",
            "descricao": "Herniorrafia Inguinal Unilateral por Videolaparoscopia"
        }
    ],
    "equipe_cirurgica": {
        "cirurgiao_principal": "Rafael Moraes Tavares",
        "primeiro_auxiliar": "Leandro Cavalcanti de Albuquerque Leite Barros"
    },
    "materiais": [
        {
            "item": "Materials: SF 500ml",
            "quantidade": "01"
        }
    ],
    "opme": [
        "Trocater 10mm descartável 01 unidade"
    ],
    "_token": "7438923:jzhdsifhosdihfasdadsf8jxzcoa"
}
```

## Tratamento de Erros

### 1. **JSON Inválido**
- Retorna objeto vazio `{}`
- Log de erro com detalhes

### 2. **Dados Vazios/Null**
- Retorna objeto vazio `{}`
- Log informativo

### 3. **Falha no Webhook**
- Log completo do erro
- Inclui dados originais e limpos
- Informações de linha e arquivo do erro

## Testes Implementados

### Arquivo: `tests/Unit/Services/Telegram/SendDocumentToWebhookTest.php`

Testes incluem:
- ✅ Limpeza de callback_data structure
- ✅ Correção de escape characters
- ✅ Remoção de non-breaking spaces
- ✅ Correção de barras duplas
- ✅ Estruturas aninhadas complexas
- ✅ Arrays de materiais
- ✅ Dados vazios/null
- ✅ Preservação de caracteres Unicode
- ✅ Dados reais do Telegram

## Monitoramento

Para monitorar o funcionamento:

1. **Verifique os logs do DBLog** para:
   - "SendDocumentToWebhook::perform - Raw data received"
   - "SendDocumentToWebhook::perform - Cleaned data"
   - "SendDocumentToWebhook::perform - Final data to send"
   - "SendDocumentToWebhook::perform - Webhook response"

2. **Em caso de erro**, verifique:
   - "cleanJsonData - JSON parsing failed"
   - "SendDocumentToWebhook::perform - Failed to send webhook"

## Próximos Passos

1. **Teste em Produção**: Monitore os logs para verificar se a limpeza está funcionando
2. **Ajustes Finos**: Adicione mais casos específicos se necessário
3. **Performance**: Otimize se houver muitos dados sendo processados
4. **Backup**: Mantenha logs dos dados originais para debug

## Exemplo de Uso Completo

```php
// No seu código onde você chama o webhook
$telegramData = $this->telegram->telegramChat->ocr_data; // Dados do banco
$sendWebhook = new SendDocumentToWebhook($this->telegram);

// O método perform() agora limpa automaticamente os dados
$sendWebhook->perform($telegramData);

// Ou para dados específicos do banco:
$cleanedData = $sendWebhook->cleanDatabaseJsonData($telegramData);
$sendWebhook->perform($cleanedData);
```
