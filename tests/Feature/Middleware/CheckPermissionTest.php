<?php

namespace Tests\Feature\Middleware;

use Tests\TestCase;
use App\Http\Middleware\CheckPermission;
use App\Models\User;
use App\Models\Organization;
use App\Models\Profile;
use App\Models\Permission;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Laravel\Sanctum\Sanctum;

class CheckPermissionTest extends TestCase
{
    use RefreshDatabase;

    private CheckPermission $middleware;
    private User $user;
    private Organization $organization;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->middleware = new CheckPermission();
        $this->organization = Organization::factory()->create();
    }

    public function test_super_admin_can_access_any_permission()
    {
        $profile = Profile::factory()->create([
            'organization_id' => $this->organization->id,
            'is_super_admin' => true,
            'is_admin' => false,
        ]);
        
        $this->user = User::factory()->create([
            'organization_id' => $this->organization->id,
            'profile_id' => $profile->id,
        ]);

        Sanctum::actingAs($this->user);

        $request = Request::create('/test', 'GET');
        $next = function ($request) {
            return new Response('Success');
        };

        $response = $this->middleware->handle($request, $next, 'any_permission');

        $this->assertEquals(200, $response->getStatusCode());
        $this->assertEquals('Success', $response->getContent());
    }

    public function test_admin_can_access_any_permission()
    {
        $profile = Profile::factory()->create([
            'organization_id' => $this->organization->id,
            'is_super_admin' => false,
            'is_admin' => true,
        ]);
        
        $this->user = User::factory()->create([
            'organization_id' => $this->organization->id,
            'profile_id' => $profile->id,
        ]);

        Sanctum::actingAs($this->user);

        $request = Request::create('/test', 'GET');
        $next = function ($request) {
            return new Response('Success');
        };

        $response = $this->middleware->handle($request, $next, 'any_permission');

        $this->assertEquals(200, $response->getStatusCode());
        $this->assertEquals('Success', $response->getContent());
    }

    public function test_user_with_permission_can_access()
    {
        $permission = Permission::factory()->create(['slug' => 'manage_users']);
        
        $profile = Profile::factory()->create([
            'organization_id' => $this->organization->id,
            'is_super_admin' => false,
            'is_admin' => false,
        ]);
        
        $profile->permissions()->attach($permission->id);
        
        $this->user = User::factory()->create([
            'organization_id' => $this->organization->id,
            'profile_id' => $profile->id,
        ]);

        Sanctum::actingAs($this->user);

        $request = Request::create('/test', 'GET');
        $next = function ($request) {
            return new Response('Success');
        };

        $response = $this->middleware->handle($request, $next, 'manage_users');

        $this->assertEquals(200, $response->getStatusCode());
        $this->assertEquals('Success', $response->getContent());
    }

    public function test_user_without_permission_cannot_access()
    {
        $profile = Profile::factory()->create([
            'organization_id' => $this->organization->id,
            'is_super_admin' => false,
            'is_admin' => false,
        ]);
        
        $this->user = User::factory()->create([
            'organization_id' => $this->organization->id,
            'profile_id' => $profile->id,
        ]);

        Sanctum::actingAs($this->user);

        $request = Request::create('/test', 'GET');
        $request->headers->set('Accept', 'application/json');
        
        $next = function ($request) {
            return new Response('Success');
        };

        $response = $this->middleware->handle($request, $next, 'manage_users');

        $this->assertEquals(403, $response->getStatusCode());
        
        $responseData = json_decode($response->getContent(), true);
        $this->assertEquals('Permission Denied', $responseData['error']);
        $this->assertEquals('manage_users', $responseData['required_permission']);
    }

    public function test_unauthenticated_user_cannot_access()
    {
        $request = Request::create('/test', 'GET');
        $request->headers->set('Accept', 'application/json');
        
        $next = function ($request) {
            return new Response('Success');
        };

        $response = $this->middleware->handle($request, $next, 'manage_users');

        $this->assertEquals(403, $response->getStatusCode());
        
        $responseData = json_decode($response->getContent(), true);
        $this->assertEquals('Permission Denied', $responseData['error']);
    }

    public function test_user_without_profile_cannot_access()
    {
        $this->user = User::factory()->create([
            'organization_id' => $this->organization->id,
            'profile_id' => null,
        ]);

        Sanctum::actingAs($this->user);

        $request = Request::create('/test', 'GET');
        $request->headers->set('Accept', 'application/json');
        
        $next = function ($request) {
            return new Response('Success');
        };

        $response = $this->middleware->handle($request, $next, 'manage_users');

        $this->assertEquals(403, $response->getStatusCode());
        
        $responseData = json_decode($response->getContent(), true);
        $this->assertEquals('Permission Denied', $responseData['error']);
    }

    public function test_middleware_adds_permission_info_to_request()
    {
        $permission = Permission::factory()->create(['slug' => 'view_reports']);
        
        $profile = Profile::factory()->create([
            'organization_id' => $this->organization->id,
            'is_super_admin' => false,
            'is_admin' => false,
        ]);
        
        $profile->permissions()->attach($permission->id);
        
        $this->user = User::factory()->create([
            'organization_id' => $this->organization->id,
            'profile_id' => $profile->id,
        ]);

        Sanctum::actingAs($this->user);

        $request = Request::create('/test', 'GET');
        $capturedRequest = null;
        
        $next = function ($request) use (&$capturedRequest) {
            $capturedRequest = $request;
            return new Response('Success');
        };

        $this->middleware->handle($request, $next, 'view_reports');

        $this->assertNotNull($capturedRequest);
        $this->assertNotNull($capturedRequest->get('user_permissions'));
        $this->assertNotNull($capturedRequest->get('user_profile'));
        $this->assertContains('view_reports', $capturedRequest->get('user_permissions'));
    }
}
