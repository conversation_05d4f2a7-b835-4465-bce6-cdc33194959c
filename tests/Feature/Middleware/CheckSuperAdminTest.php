<?php

namespace Tests\Feature\Middleware;

use Tests\TestCase;
use App\Models\User;
use App\Models\Organization;
use App\Models\Profile;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Laravel\Sanctum\Sanctum;
use Illuminate\Support\Facades\Route;

class CheckSuperAdminTest extends TestCase
{
    use RefreshDatabase;

    private User $superAdminUser;
    private User $adminUser;
    private User $regularUser;
    private Organization $organization;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->organization = Organization::factory()->create();
        
        // Create super admin user
        $superAdminProfile = Profile::factory()->create([
            'organization_id' => $this->organization->id,
            'is_super_admin' => true,
            'is_admin' => false,
        ]);
        
        $this->superAdminUser = User::factory()->create([
            'organization_id' => $this->organization->id,
            'profile_id' => $superAdminProfile->id,
        ]);

        // Create admin user
        $adminProfile = Profile::factory()->create([
            'organization_id' => $this->organization->id,
            'is_super_admin' => false,
            'is_admin' => true,
        ]);
        
        $this->adminUser = User::factory()->create([
            'organization_id' => $this->organization->id,
            'profile_id' => $adminProfile->id,
        ]);

        // Create regular user
        $regularProfile = Profile::factory()->create([
            'organization_id' => $this->organization->id,
            'is_super_admin' => false,
            'is_admin' => false,
        ]);
        
        $this->regularUser = User::factory()->create([
            'organization_id' => $this->organization->id,
            'profile_id' => $regularProfile->id,
        ]);

        // Create test route
        Route::get('/test-super-admin', function () {
            return response()->json(['message' => 'Super admin access granted']);
        })->middleware(['auth:sanctum', 'super_admin']);
    }

    public function test_super_admin_can_access_protected_route()
    {
        Sanctum::actingAs($this->superAdminUser);

        $response = $this->getJson('/test-super-admin');

        $response->assertStatus(200)
                ->assertJsonFragment([
                    'message' => 'Super admin access granted'
                ]);
    }

    public function test_admin_cannot_access_super_admin_route()
    {
        Sanctum::actingAs($this->adminUser);

        $response = $this->getJson('/test-super-admin');

        $response->assertStatus(403);
    }

    public function test_regular_user_cannot_access_super_admin_route()
    {
        Sanctum::actingAs($this->regularUser);

        $response = $this->getJson('/test-super-admin');

        $response->assertStatus(403);
    }

    public function test_user_without_profile_cannot_access_super_admin_route()
    {
        $userWithoutProfile = User::factory()->create([
            'organization_id' => $this->organization->id,
            'profile_id' => null,
        ]);

        Sanctum::actingAs($userWithoutProfile);

        $response = $this->getJson('/test-super-admin');

        $response->assertStatus(403);
    }

    public function test_unauthenticated_user_cannot_access_super_admin_route()
    {
        $response = $this->getJson('/test-super-admin');

        $response->assertStatus(401);
    }

    public function test_middleware_adds_profile_info_to_request()
    {
        Route::get('/test-super-admin-info', function (\Illuminate\Http\Request $request) {
            return response()->json([
                'profile_id' => $request->get('profile_id'),
                'is_super_admin' => $request->get('is_super_admin'),
                'is_admin' => $request->get('is_admin')
            ]);
        })->middleware(['auth:sanctum', 'super_admin']);

        Sanctum::actingAs($this->superAdminUser);

        $response = $this->getJson('/test-super-admin-info');

        $response->assertStatus(200)
                ->assertJsonFragment([
                    'profile_id' => $this->superAdminUser->profile_id,
                    'is_super_admin' => true,
                    'is_admin' => false
                ]);
    }
}
