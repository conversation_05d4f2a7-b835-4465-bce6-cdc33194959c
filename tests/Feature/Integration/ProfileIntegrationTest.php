<?php

namespace Tests\Feature\Integration;

use Tests\TestCase;
use App\Models\Profile;
use App\Models\Organization;
use App\Repositories\ProfileRepository;
use App\Factories\ProfileFactory;
use Illuminate\Foundation\Testing\RefreshDatabase;

class ProfileIntegrationTest extends TestCase
{
    use RefreshDatabase;

    private $profileRepository;
    private $profileFactory;

    protected function setUp(): void
    {
        parent::setUp();

        $this->profileFactory = app()->make(ProfileFactory::class);
        $this->profileRepository = app()->make(ProfileRepository::class);
    }

    public function test_can_create_profile_through_repository()
    {
        // Arrange
        $organization = Organization::factory()->create();

        $profileData = [
            'name' => 'Test Profile',
            'slug' => 'test_profile',
            'organization_id' => $organization->id,
            'is_admin' => false,
            'is_super_admin' => false
        ];

        $domain = $this->profileFactory->buildFromArray($profileData);

        // Act
        $result = $this->profileRepository->store($domain);

        // Assert
        $this->assertNotNull($result->id);
        $this->assertEquals('test_profile', $result->slug);
        $this->assertEquals('Test Profile', $result->name);
        $this->assertEquals($organization->id, $result->organization_id);
        $this->assertFalse($result->is_admin);
        $this->assertFalse($result->is_super_admin);

        // Verify in database
        $this->assertDatabaseHas('profiles', [
            'slug' => 'test_profile',
            'name' => 'Test Profile',
            'organization_id' => $organization->id
        ]);
    }

    public function test_can_fetch_all_profiles()
    {
        // Arrange
        $organization = Organization::factory()->create();
        Profile::factory()->count(2)->create(['organization_id' => $organization->id]);

        // Act
        $result = $this->profileRepository->fetchAll();

        // Assert
        $this->assertIsArray($result);
        $this->assertCount(2, $result);
        $this->assertInstanceOf(\App\Domains\Profile::class, $result[0]);
    }

    public function test_can_fetch_profile_by_id()
    {
        // Arrange
        $organization = Organization::factory()->create();
        $profile = Profile::factory()->create([
            'slug' => 'test_profile',
            'name' => 'Test Profile',
            'organization_id' => $organization->id
        ]);

        // Act
        $result = $this->profileRepository->fetchById($profile->id);

        // Assert
        $this->assertInstanceOf(\App\Domains\Profile::class, $result);
        $this->assertEquals($profile->id, $result->id);
        $this->assertEquals('test_profile', $result->slug);
        $this->assertEquals('Test Profile', $result->name);
    }

    public function test_can_update_profile()
    {
        // Arrange
        $organization = Organization::factory()->create();
        $profile = Profile::factory()->create([
            'slug' => 'original_profile',
            'name' => 'Original Profile',
            'organization_id' => $organization->id,
            'is_admin' => false
        ]);

        $updateData = [
            'slug' => 'updated_profile',
            'name' => 'Updated Profile',
            'is_admin' => true,
            'is_super_admin' => false,
            'organization_id' => $organization->id
        ];

        $domain = $this->profileFactory->buildFromArray($updateData, $profile->id);

        // Act
        $result = $this->profileRepository->update($domain);

        // Assert
        $this->assertEquals($profile->id, $result->id);
        $this->assertEquals('updated_profile', $result->slug);
        $this->assertEquals('Updated Profile', $result->name);
        $this->assertTrue($result->is_admin);

        // Verify in database
        $this->assertDatabaseHas('profiles', [
            'id' => $profile->id,
            'slug' => 'updated_profile',
            'name' => 'Updated Profile',
            'is_admin' => true
        ]);
    }

    public function test_can_delete_profile()
    {
        // Arrange
        $organization = Organization::factory()->create();
        $profile = Profile::factory()->create(['organization_id' => $organization->id]);
        $domain = $this->profileRepository->fetchById($profile->id);

        // Act
        $result = $this->profileRepository->delete($domain);

        // Assert
        $this->assertTrue($result);

        // Verify removed from database
        $this->assertDatabaseMissing('profiles', [
            'id' => $profile->id
        ]);
    }

    public function test_can_fetch_profiles_by_organization()
    {
        // Arrange
        $organization1 = Organization::factory()->create();
        $organization2 = Organization::factory()->create();

        Profile::factory()->count(2)->create(['organization_id' => $organization1->id]);
        Profile::factory()->create(['organization_id' => $organization2->id]);

        // Act
        $result = $this->profileRepository->fetchByOrganization($organization1->id);

        // Assert
        $this->assertIsArray($result);
        $this->assertCount(2, $result);
        $this->assertInstanceOf(\App\Domains\Profile::class, $result[0]);
        $this->assertEquals($organization1->id, $result[0]->organization_id);
    }

    public function test_factory_can_instantiate_with_app_make()
    {
        // Act & Assert
        $factory = app()->make(ProfileFactory::class);
        $this->assertInstanceOf(ProfileFactory::class, $factory);
    }

    public function test_repository_can_instantiate_with_app_make()
    {
        // Act & Assert
        $repository = app()->make(ProfileRepository::class);
        $this->assertInstanceOf(ProfileRepository::class, $repository);
    }
}
