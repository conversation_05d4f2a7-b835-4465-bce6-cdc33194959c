<?php

namespace Tests\Feature\Integration;

use Tests\TestCase;
use App\Models\Permission;
use App\Repositories\PermissionRepository;
use App\Factories\PermissionFactory;
use Illuminate\Foundation\Testing\RefreshDatabase;

class PermissionIntegrationTest extends TestCase
{
    use RefreshDatabase;

    private $permissionRepository;
    private $permissionFactory;

    protected function setUp(): void
    {
        parent::setUp();

        $this->permissionFactory = app()->make(PermissionFactory::class);
        $this->permissionRepository = app()->make(PermissionRepository::class);
    }

    public function test_can_create_permission_through_repository()
    {
        // Arrange
        $permissionData = [
            'slug' => 'test_permission',
            'name' => 'Test Permission',
            'description' => 'Test description'
        ];

        $domain = $this->permissionFactory->buildFromArray($permissionData);

        // Act
        $result = $this->permissionRepository->store($domain);

        // Assert
        $this->assertNotNull($result->id);
        $this->assertEquals('test_permission', $result->slug);
        $this->assertEquals('Test Permission', $result->name);
        $this->assertEquals('Test description', $result->description);

        // Verify in database
        $this->assertDatabaseHas('permissions', [
            'slug' => 'test_permission',
            'name' => 'Test Permission',
            'description' => 'Test description'
        ]);
    }

    public function test_can_fetch_all_permissions()
    {
        // Arrange
        Permission::factory()->count(3)->create();

        // Act
        $result = $this->permissionRepository->fetchAll();

        // Assert
        $this->assertIsArray($result);
        $this->assertCount(3, $result);
        $this->assertInstanceOf(\App\Domains\Permission::class, $result[0]);
    }

    public function test_can_fetch_permission_by_id()
    {
        // Arrange
        $permission = Permission::factory()->create([
            'slug' => 'test_permission',
            'name' => 'Test Permission'
        ]);

        // Act
        $result = $this->permissionRepository->fetchById($permission->id);

        // Assert
        $this->assertInstanceOf(\App\Domains\Permission::class, $result);
        $this->assertEquals($permission->id, $result->id);
        $this->assertEquals('test_permission', $result->slug);
        $this->assertEquals('Test Permission', $result->name);
    }

    public function test_can_update_permission()
    {
        // Arrange
        $permission = Permission::factory()->create([
            'slug' => 'original_permission',
            'name' => 'Original Permission'
        ]);

        $updateData = [
            'slug' => 'updated_permission',
            'name' => 'Updated Permission',
            'description' => 'Updated description'
        ];

        $domain = $this->permissionFactory->buildFromArray($updateData, $permission->id);

        // Act
        $result = $this->permissionRepository->update($domain);

        // Assert
        $this->assertEquals($permission->id, $result->id);
        $this->assertEquals('updated_permission', $result->slug);
        $this->assertEquals('Updated Permission', $result->name);
        $this->assertEquals('Updated description', $result->description);

        // Verify in database
        $this->assertDatabaseHas('permissions', [
            'id' => $permission->id,
            'slug' => 'updated_permission',
            'name' => 'Updated Permission'
        ]);
    }

    public function test_can_delete_permission()
    {
        // Arrange
        $permission = Permission::factory()->create();
        $domain = $this->permissionRepository->fetchById($permission->id);

        // Act
        $result = $this->permissionRepository->delete($domain);

        // Assert
        $this->assertTrue($result);

        // Verify removed from database
        $this->assertDatabaseMissing('permissions', [
            'id' => $permission->id
        ]);
    }

    public function test_factory_can_instantiate_with_app_make()
    {
        // Act & Assert
        $factory = app()->make(PermissionFactory::class);
        $this->assertInstanceOf(PermissionFactory::class, $factory);
    }

    public function test_repository_can_instantiate_with_app_make()
    {
        // Act & Assert
        $repository = app()->make(PermissionRepository::class);
        $this->assertInstanceOf(PermissionRepository::class, $repository);
    }
}
