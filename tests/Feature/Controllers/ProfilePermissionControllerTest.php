<?php

namespace Tests\Feature\Controllers;

use Tests\TestCase;
use App\Models\User;
use App\Models\Organization;
use App\Models\Profile;
use App\Models\Permission;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Laravel\Sanctum\Sanctum;

class ProfilePermissionControllerTest extends TestCase
{
    use RefreshDatabase;

    private User $superAdminUser;
    private User $adminUser;
    private User $regularUser;
    private Organization $organization;
    private Organization $otherOrganization;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->organization = Organization::factory()->create();
        $this->otherOrganization = Organization::factory()->create();
        
        // Create super admin user
        $superAdminProfile = Profile::factory()->create([
            'organization_id' => $this->organization->id,
            'is_super_admin' => true,
            'is_admin' => false,
        ]);
        
        $this->superAdminUser = User::factory()->create([
            'organization_id' => $this->organization->id,
            'profile_id' => $superAdminProfile->id,
        ]);

        // Create admin user
        $adminProfile = Profile::factory()->create([
            'organization_id' => $this->organization->id,
            'is_super_admin' => false,
            'is_admin' => true,
        ]);
        
        $this->adminUser = User::factory()->create([
            'organization_id' => $this->organization->id,
            'profile_id' => $adminProfile->id,
        ]);

        // Create regular user
        $regularProfile = Profile::factory()->create([
            'organization_id' => $this->organization->id,
            'is_super_admin' => false,
            'is_admin' => false,
        ]);
        
        $this->regularUser = User::factory()->create([
            'organization_id' => $this->organization->id,
            'profile_id' => $regularProfile->id,
        ]);
    }

    public function test_admin_can_add_permission_to_profile_in_same_organization()
    {
        Sanctum::actingAs($this->adminUser);

        $profile = Profile::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $permission = Permission::factory()->create([
            'slug' => 'manage_users'
        ]);

        $response = $this->postJson('/api/profile/permission/add', [
            'profile_id' => $profile->id,
            'permission_slug' => 'manage_users'
        ]);

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'message',
                    'profile_id',
                    'permission_slug'
                ])
                ->assertJsonFragment([
                    'success' => true,
                    'profile_id' => $profile->id,
                    'permission_slug' => 'manage_users'
                ]);

        // Verify permission was actually added
        $this->assertTrue($profile->permissions()->where('slug', 'manage_users')->exists());
    }

    public function test_super_admin_can_add_permission_to_any_profile()
    {
        Sanctum::actingAs($this->superAdminUser);

        $profile = Profile::factory()->create([
            'organization_id' => $this->otherOrganization->id
        ]);

        $permission = Permission::factory()->create([
            'slug' => 'view_reports'
        ]);

        $response = $this->postJson('/api/profile/permission/add', [
            'profile_id' => $profile->id,
            'permission_slug' => 'view_reports'
        ]);

        $response->assertStatus(200)
                ->assertJsonFragment([
                    'success' => true,
                    'permission_slug' => 'view_reports'
                ]);
    }

    public function test_admin_cannot_add_permission_to_profile_in_different_organization()
    {
        Sanctum::actingAs($this->adminUser);

        $profile = Profile::factory()->create([
            'organization_id' => $this->otherOrganization->id
        ]);

        $permission = Permission::factory()->create([
            'slug' => 'manage_users'
        ]);

        $response = $this->postJson('/api/profile/permission/add', [
            'profile_id' => $profile->id,
            'permission_slug' => 'manage_users'
        ]);

        $response->assertStatus(403);
    }

    public function test_regular_user_cannot_add_permissions()
    {
        Sanctum::actingAs($this->regularUser);

        $profile = Profile::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $permission = Permission::factory()->create([
            'slug' => 'manage_users'
        ]);

        $response = $this->postJson('/api/profile/permission/add', [
            'profile_id' => $profile->id,
            'permission_slug' => 'manage_users'
        ]);

        $response->assertStatus(403);
    }

    public function test_admin_can_remove_permission_from_profile()
    {
        Sanctum::actingAs($this->adminUser);

        $profile = Profile::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $permission = Permission::factory()->create([
            'slug' => 'manage_users'
        ]);

        // First add the permission
        $profile->permissions()->attach($permission->id);

        $response = $this->postJson('/api/profile/permission/remove', [
            'profile_id' => $profile->id,
            'permission_slug' => 'manage_users'
        ]);

        $response->assertStatus(200)
                ->assertJsonFragment([
                    'success' => true,
                    'permission_slug' => 'manage_users'
                ]);

        // Verify permission was actually removed
        $this->assertFalse($profile->permissions()->where('slug', 'manage_users')->exists());
    }

    public function test_admin_can_get_profile_permissions()
    {
        Sanctum::actingAs($this->adminUser);

        $profile = Profile::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $permission1 = Permission::factory()->create(['slug' => 'manage_users']);
        $permission2 = Permission::factory()->create(['slug' => 'view_reports']);

        $profile->permissions()->attach([$permission1->id, $permission2->id]);

        $response = $this->getJson("/api/profile/{$profile->id}/permissions");

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'data' => [
                        '*' => [
                            'id',
                            'slug',
                            'name',
                            'description'
                        ]
                    ]
                ]);

        $permissions = $response->json('data');
        $this->assertCount(2, $permissions);
        
        $slugs = array_column($permissions, 'slug');
        $this->assertContains('manage_users', $slugs);
        $this->assertContains('view_reports', $slugs);
    }

    public function test_add_permission_validates_required_fields()
    {
        Sanctum::actingAs($this->adminUser);

        $response = $this->postJson('/api/profile/permission/add', []);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['profile_id', 'permission_slug']);
    }

    public function test_remove_permission_validates_required_fields()
    {
        Sanctum::actingAs($this->adminUser);

        $response = $this->postJson('/api/profile/permission/remove', []);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['profile_id', 'permission_slug']);
    }

    public function test_cannot_add_duplicate_permission()
    {
        Sanctum::actingAs($this->adminUser);

        $profile = Profile::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $permission = Permission::factory()->create([
            'slug' => 'manage_users'
        ]);

        // First add the permission
        $profile->permissions()->attach($permission->id);

        // Try to add the same permission again
        $response = $this->postJson('/api/profile/permission/add', [
            'profile_id' => $profile->id,
            'permission_slug' => 'manage_users'
        ]);

        $response->assertStatus(400);
    }

    public function test_cannot_remove_non_existent_permission()
    {
        Sanctum::actingAs($this->adminUser);

        $profile = Profile::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        Permission::factory()->create([
            'slug' => 'manage_users'
        ]);

        $response = $this->postJson('/api/profile/permission/remove', [
            'profile_id' => $profile->id,
            'permission_slug' => 'manage_users'
        ]);

        $response->assertStatus(400);
    }

    public function test_unauthenticated_user_cannot_access_profile_permissions()
    {
        $profile = Profile::factory()->create();

        $response = $this->postJson('/api/profile/permission/add', [
            'profile_id' => $profile->id,
            'permission_slug' => 'manage_users'
        ]);

        $response->assertStatus(401);
    }
}
