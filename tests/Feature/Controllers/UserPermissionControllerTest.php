<?php

namespace Tests\Feature\Controllers;

use Tests\TestCase;
use App\Models\User;
use App\Models\Organization;
use App\Models\Profile;
use App\Models\Permission;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Laravel\Sanctum\Sanctum;

class UserPermissionControllerTest extends TestCase
{
    use RefreshDatabase;

    private User $superAdminUser;
    private User $adminUser;
    private User $regularUser;
    private Organization $organization;

    protected function setUp(): void
    {
        parent::setUp();

        $this->organization = Organization::factory()->create();

        // Create super admin user
        $superAdminProfile = Profile::factory()->create([
            'organization_id' => $this->organization->id,
            'is_super_admin' => true,
            'is_admin' => false,
        ]);

        $this->superAdminUser = User::factory()->create([
            'organization_id' => $this->organization->id,
            'profile_id' => $superAdminProfile->id,
        ]);

        // Create admin user
        $adminProfile = Profile::factory()->create([
            'organization_id' => $this->organization->id,
            'is_super_admin' => false,
            'is_admin' => true,
        ]);

        $this->adminUser = User::factory()->create([
            'organization_id' => $this->organization->id,
            'profile_id' => $adminProfile->id,
        ]);

        // Create regular user with permissions
        $regularProfile = Profile::factory()->create([
            'organization_id' => $this->organization->id,
            'is_super_admin' => false,
            'is_admin' => false,
        ]);

        $this->regularUser = User::factory()->create([
            'organization_id' => $this->organization->id,
            'profile_id' => $regularProfile->id,
        ]);

        // Add some permissions to regular user's profile
        $permission1 = Permission::factory()->create(['slug' => 'view_reports']);
        $permission2 = Permission::factory()->create(['slug' => 'manage_clients']);
        $regularProfile->permissions()->attach([$permission1->id, $permission2->id]);
    }

    public function test_user_can_get_own_permissions()
    {
        Sanctum::actingAs($this->regularUser);

        $response = $this->getJson('/api/user/permissions');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'data' => [
                        '*' => [
                            'id',
                            'slug',
                            'name',
                            'description'
                        ]
                    ]
                ]);

        $permissions = $response->json('data');
        $this->assertCount(2, $permissions);

        $slugs = array_column($permissions, 'slug');
        $this->assertContains('view_reports', $slugs);
        $this->assertContains('manage_clients', $slugs);
    }

    public function test_super_admin_gets_all_permissions()
    {
        Sanctum::actingAs($this->superAdminUser);

        // Create some permissions
        Permission::factory()->create(['slug' => 'permission1']);
        Permission::factory()->create(['slug' => 'permission2']);
        Permission::factory()->create(['slug' => 'permission3']);

        $response = $this->getJson('/api/user/permissions');

        $response->assertStatus(200);

        $permissions = $response->json('data');
        $this->assertGreaterThanOrEqual(3, count($permissions));
    }

    public function test_admin_gets_all_permissions()
    {
        Sanctum::actingAs($this->adminUser);

        // Create some permissions
        Permission::factory()->create(['slug' => 'permission1']);
        Permission::factory()->create(['slug' => 'permission2']);

        $response = $this->getJson('/api/user/permissions');

        $response->assertStatus(200);

        $permissions = $response->json('data');
        $this->assertGreaterThanOrEqual(2, count($permissions));
    }

    public function test_user_can_check_specific_permission()
    {
        Sanctum::actingAs($this->regularUser);

        $response = $this->getJson('/api/user/permissions/view_reports');

        $response->assertStatus(200)
                ->assertJsonFragment([
                    'has_permission' => true,
                    'permission_slug' => 'view_reports'
                ]);
    }

    public function test_user_cannot_check_permission_they_dont_have()
    {
        Sanctum::actingAs($this->regularUser);

        Permission::factory()->create(['slug' => 'delete_everything']);

        $response = $this->getJson('/api/user/permissions/delete_everything');

        $response->assertStatus(200)
                ->assertJsonFragment([
                    'has_permission' => false,
                    'permission_slug' => 'delete_everything'
                ]);
    }

    public function test_super_admin_has_all_permissions()
    {
        Sanctum::actingAs($this->superAdminUser);

        Permission::factory()->create(['slug' => 'any_permission']);

        $response = $this->getJson('/api/user/permissions/any_permission');

        $response->assertStatus(200)
                ->assertJsonFragment([
                    'has_permission' => true,
                    'permission_slug' => 'any_permission'
                ]);
    }

    public function test_admin_has_all_permissions()
    {
        Sanctum::actingAs($this->adminUser);

        Permission::factory()->create(['slug' => 'any_permission']);

        $response = $this->getJson('/api/user/permissions/any_permission');

        $response->assertStatus(200)
                ->assertJsonFragment([
                    'has_permission' => true,
                    'permission_slug' => 'any_permission'
                ]);
    }

    public function test_user_without_profile_has_no_permissions()
    {
        $userWithoutProfile = User::factory()->create([
            'organization_id' => $this->organization->id,
            'profile_id' => null,
        ]);

        Sanctum::actingAs($userWithoutProfile);

        $response = $this->getJson('/api/user/permissions');

        $response->assertStatus(200);

        $permissions = $response->json('data');
        $this->assertEmpty($permissions);
    }

    public function test_user_without_profile_cannot_check_specific_permission()
    {
        $userWithoutProfile = User::factory()->create([
            'organization_id' => $this->organization->id,
            'profile_id' => null,
        ]);

        Sanctum::actingAs($userWithoutProfile);

        Permission::factory()->create(['slug' => 'some_permission']);

        $response = $this->getJson('/api/user/permissions/some_permission');

        $response->assertStatus(200)
                ->assertJsonFragment([
                    'has_permission' => false,
                    'permission_slug' => 'some_permission'
                ]);
    }

    public function test_check_permission_for_non_existent_permission()
    {
        Sanctum::actingAs($this->regularUser);

        $response = $this->getJson('/api/user/permissions/non_existent_permission');

        $response->assertStatus(200)
                ->assertJsonFragment([
                    'has_permission' => false,
                    'permission_slug' => 'non_existent_permission'
                ]);
    }

    public function test_unauthenticated_user_cannot_access_permissions()
    {
        $response = $this->getJson('/api/user/permissions');

        $response->assertStatus(401);
    }

    public function test_unauthenticated_user_cannot_check_specific_permission()
    {
        $response = $this->getJson('/api/user/permissions/some_permission');

        $response->assertStatus(401);
    }

    public function test_permissions_response_structure()
    {
        Sanctum::actingAs($this->regularUser);

        $response = $this->getJson('/api/user/permissions');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'data' => [
                        '*' => [
                            'id',
                            'slug',
                            'name',
                            'description',
                            'created_at',
                            'updated_at'
                        ]
                    ]
                ]);
    }

    public function test_check_permission_response_structure()
    {
        Sanctum::actingAs($this->regularUser);

        $response = $this->getJson('/api/user/permissions/view_reports');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'has_permission',
                    'permission_slug',
                    'user_id',
                    'profile_id'
                ]);
    }

    public function test_user_can_fetch_own_permissions()
    {
        // Create permissions and attach to regular user profile
        $permission1 = Permission::factory()->create([
            'slug' => 'test-permission-1',
            'name' => 'Test Permission 1'
        ]);

        $permission2 = Permission::factory()->create([
            'slug' => 'test-permission-2',
            'name' => 'Test Permission 2'
        ]);

        // Clear existing permissions and add only the test ones
        $this->regularUser->profile->permissions()->detach();
        $this->regularUser->profile->permissions()->attach([$permission1->id, $permission2->id]);

        Sanctum::actingAs($this->regularUser);

        $response = $this->getJson('/api/user/permissions');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'status',
                    'message',
                    'data' => [
                        'user' => [
                            'id',
                            'name',
                            'email',
                            'organization_id'
                        ],
                        'profile' => [
                            'id',
                            'name',
                            'slug',
                            'description',
                            'is_admin',
                            'is_super_admin'
                        ],
                        'permissions' => [
                            '*' => [
                                'id',
                                'slug',
                                'name',
                                'description'
                            ]
                        ],
                        'can_access_all'
                    ]
                ]);

        $data = $response->json('data');
        $this->assertEquals($this->regularUser->id, $data['user']['id']);
        $this->assertCount(2, $data['permissions']);
        $this->assertFalse($data['can_access_all']);

        // Check permissions are present
        $permissionSlugs = array_column($data['permissions'], 'slug');
        $this->assertContains('test-permission-1', $permissionSlugs);
        $this->assertContains('test-permission-2', $permissionSlugs);
    }

    public function test_admin_user_gets_full_access_in_fetch_permissions()
    {
        Sanctum::actingAs($this->adminUser);

        $response = $this->getJson('/api/user/permissions');

        $response->assertStatus(200);

        $data = $response->json('data');
        $this->assertTrue($data['can_access_all']);
        $this->assertTrue($data['profile']['is_admin']);
    }

    public function test_user_can_check_specific_permission_they_have()
    {
        // Create permission and attach to regular user profile
        $permission = Permission::factory()->create([
            'slug' => 'test-permission',
            'name' => 'Test Permission'
        ]);

        $this->regularUser->profile->permissions()->attach($permission->id);

        Sanctum::actingAs($this->regularUser);

        $response = $this->getJson('/api/user/permission/test-permission/can');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'status',
                    'message',
                    'data' => [
                        'user' => [
                            'id',
                            'name',
                            'email'
                        ],
                        'profile' => [
                            'id',
                            'name',
                            'slug',
                            'is_admin',
                            'is_super_admin'
                        ],
                        'permission' => [
                            'id',
                            'slug',
                            'name',
                            'description',
                            'exists'
                        ],
                        'has_permission',
                        'reason'
                    ]
                ]);

        $data = $response->json('data');
        $this->assertTrue($data['has_permission']);
        $this->assertEquals('Permission explicitly granted to profile', $data['reason']);
        $this->assertEquals('test-permission', $data['permission']['slug']);
    }

    public function test_admin_can_check_any_permission()
    {
        // Create permission but don't attach to admin profile
        $permission = Permission::factory()->create([
            'slug' => 'test-permission',
            'name' => 'Test Permission'
        ]);

        Sanctum::actingAs($this->adminUser);

        $response = $this->getJson('/api/user/permission/test-permission/can');

        $response->assertStatus(200);

        $data = $response->json('data');
        $this->assertTrue($data['has_permission']);
        $this->assertEquals('Profile is admin', $data['reason']);
    }

    public function test_check_non_existent_permission()
    {
        Sanctum::actingAs($this->regularUser);

        $response = $this->getJson('/api/user/permission/non-existent-permission/can');

        $response->assertStatus(200);

        $data = $response->json('data');
        $this->assertFalse($data['has_permission']);
        $this->assertEquals('Permission does not exist', $data['reason']);
        $this->assertFalse($data['permission']['exists']);
    }

    public function test_unauthenticated_user_cannot_fetch_permissions()
    {
        $response = $this->getJson('/api/user/permissions');
        $response->assertStatus(401);
    }

    public function test_unauthenticated_user_cannot_check_specific_permission_with_can_endpoint()
    {
        $response = $this->getJson('/api/user/permission/test-permission/can');
        $response->assertStatus(401);
    }
}
