<?php

namespace Tests\Feature\Controllers;

use Tests\TestCase;
use App\Models\User;
use App\Models\Organization;
use App\Models\Profile;
use App\Models\Permission;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Laravel\Sanctum\Sanctum;

class PermissionControllerTest extends TestCase
{
    use RefreshDatabase;

    private User $superAdminUser;
    private User $adminUser;
    private User $regularUser;
    private Organization $organization;

    protected function setUp(): void
    {
        parent::setUp();

        $this->organization = Organization::factory()->create();

        // Create super admin user
        $superAdminProfile = Profile::factory()->create([
            'organization_id' => $this->organization->id,
            'is_super_admin' => true,
            'is_admin' => false,
        ]);

        $this->superAdminUser = User::factory()->create([
            'organization_id' => $this->organization->id,
            'profile_id' => $superAdminProfile->id,
        ]);

        // Create admin user
        $adminProfile = Profile::factory()->create([
            'organization_id' => $this->organization->id,
            'is_super_admin' => false,
            'is_admin' => true,
        ]);

        $this->adminUser = User::factory()->create([
            'organization_id' => $this->organization->id,
            'profile_id' => $adminProfile->id,
        ]);

        // Create regular user
        $regularProfile = Profile::factory()->create([
            'organization_id' => $this->organization->id,
            'is_super_admin' => false,
            'is_admin' => false,
        ]);

        $this->regularUser = User::factory()->create([
            'organization_id' => $this->organization->id,
            'profile_id' => $regularProfile->id,
        ]);
    }

    public function test_super_admin_can_list_permissions()
    {
        Sanctum::actingAs($this->superAdminUser);

        Permission::factory()->count(3)->create();

        $response = $this->getJson('/api/permissions');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'data' => [
                        '*' => [
                            'id',
                            'slug',
                            'name',
                            'description',
                            'created_at',
                            'updated_at'
                        ]
                    ]
                ]);
    }

    public function test_admin_cannot_list_permissions()
    {
        Sanctum::actingAs($this->adminUser);

        $response = $this->getJson('/api/permissions');

        $response->assertStatus(403);
    }

    public function test_regular_user_cannot_list_permissions()
    {
        Sanctum::actingAs($this->regularUser);

        $response = $this->getJson('/api/permissions');

        $response->assertStatus(403);
    }

    public function test_super_admin_can_create_permission()
    {
        Sanctum::actingAs($this->superAdminUser);

        $permissionData = [
            'slug' => 'manage_test',
            'name' => 'Manage Test',
            'description' => 'Permission to manage test resources'
        ];

        $response = $this->postJson('/api/permissions', $permissionData);

        $response->assertStatus(201)
                ->assertJsonStructure([
                    'data' => [
                        'id',
                        'slug',
                        'name',
                        'description',
                        'created_at',
                        'updated_at'
                    ]
                ])
                ->assertJsonFragment([
                    'slug' => 'manage_test',
                    'name' => 'Manage Test'
                ]);

        $this->assertDatabaseHas('permissions', [
            'slug' => 'manage_test',
            'name' => 'Manage Test'
        ]);
    }

    public function test_admin_cannot_create_permission()
    {
        Sanctum::actingAs($this->adminUser);

        $permissionData = [
            'slug' => 'manage_test',
            'name' => 'Manage Test'
        ];

        $response = $this->postJson('/api/permissions', $permissionData);

        $response->assertStatus(403);
    }

    public function test_super_admin_can_show_permission()
    {
        Sanctum::actingAs($this->superAdminUser);

        $permission = Permission::factory()->create();

        $response = $this->getJson("/api/permissions/{$permission->id}");

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'data' => [
                        'id',
                        'slug',
                        'name',
                        'description',
                        'created_at',
                        'updated_at'
                    ]
                ])
                ->assertJsonFragment([
                    'id' => $permission->id,
                    'slug' => $permission->slug
                ]);
    }

    public function test_super_admin_can_update_permission()
    {
        Sanctum::actingAs($this->superAdminUser);

        $permission = Permission::factory()->create([
            'slug' => 'old_slug',
            'name' => 'Old Name'
        ]);

        $updateData = [
            'slug' => 'new_slug',
            'name' => 'New Name',
            'description' => 'Updated description'
        ];

        $response = $this->putJson("/api/permissions/{$permission->id}", $updateData);

        $response->assertStatus(200)
                ->assertJsonFragment([
                    'slug' => 'new_slug',
                    'name' => 'New Name'
                ]);

        $this->assertDatabaseHas('permissions', [
            'id' => $permission->id,
            'slug' => 'new_slug',
            'name' => 'New Name'
        ]);
    }

    public function test_super_admin_can_delete_permission()
    {
        Sanctum::actingAs($this->superAdminUser);

        $permission = Permission::factory()->create();

        $response = $this->deleteJson("/api/permissions/{$permission->id}");

        $response->assertStatus(200)
                ->assertJsonFragment([
                    'message' => 'Permission deleted successfully'
                ]);

        $this->assertDatabaseMissing('permissions', [
            'id' => $permission->id
        ]);
    }

    public function test_create_permission_validates_required_fields()
    {
        Sanctum::actingAs($this->superAdminUser);

        $response = $this->postJson('/api/permissions', []);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['slug', 'name']);
    }

    public function test_create_permission_validates_unique_slug()
    {
        Sanctum::actingAs($this->superAdminUser);

        $existingPermission = Permission::factory()->create(['slug' => 'existing_slug']);

        $response = $this->postJson('/api/permissions', [
            'slug' => 'existing_slug',
            'name' => 'Test Permission'
        ]);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['slug']);
    }

    public function test_unauthenticated_user_cannot_access_permissions()
    {
        $response = $this->getJson('/api/permissions');

        $response->assertStatus(401);
    }
}
