<?php

namespace Tests\Feature\Controllers;

use Tests\TestCase;
use App\Models\User;
use App\Models\Organization;
use App\Models\Profile;
use App\Models\Permission;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Laravel\Sanctum\Sanctum;

class ProfileControllerTest extends TestCase
{
    use RefreshDatabase;

    private User $superAdminUser;
    private User $adminUser;
    private User $regularUser;
    private Organization $organization;
    private Organization $otherOrganization;

    protected function setUp(): void
    {
        parent::setUp();

        $this->organization = Organization::factory()->create();
        $this->otherOrganization = Organization::factory()->create();

        // Create super admin user
        $superAdminProfile = Profile::factory()->create([
            'organization_id' => $this->organization->id,
            'is_super_admin' => true,
            'is_admin' => false,
        ]);

        $this->superAdminUser = User::factory()->create([
            'organization_id' => $this->organization->id,
            'profile_id' => $superAdminProfile->id,
        ]);

        // Create admin user
        $adminProfile = Profile::factory()->create([
            'organization_id' => $this->organization->id,
            'is_super_admin' => false,
            'is_admin' => true,
        ]);

        $this->adminUser = User::factory()->create([
            'organization_id' => $this->organization->id,
            'profile_id' => $adminProfile->id,
        ]);

        // Create regular user
        $regularProfile = Profile::factory()->create([
            'organization_id' => $this->organization->id,
            'is_super_admin' => false,
            'is_admin' => false,
        ]);

        $this->regularUser = User::factory()->create([
            'organization_id' => $this->organization->id,
            'profile_id' => $regularProfile->id,
        ]);
    }

    public function test_admin_can_list_profiles_from_own_organization()
    {
        Sanctum::actingAs($this->adminUser);

        // Create profiles in same organization
        Profile::factory()->count(2)->create([
            'organization_id' => $this->organization->id
        ]);

        // Create profile in different organization (should not appear)
        Profile::factory()->create([
            'organization_id' => $this->otherOrganization->id
        ]);

        $response = $this->getJson('/api/profiles');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'data' => [
                        'data' => [
                            '*' => [
                                'id',
                                'organization_id',
                                'name',
                                'slug',
                                'description',
                                'is_admin',
                                'is_super_admin',
                                'created_at',
                                'updated_at',
                                'permissions'
                            ]
                        ],
                        'count',
                        'total',
                        'currentPage',
                        'lastPage'
                    ]
                ]);

        // Should only see profiles from own organization
        $responseData = $response->json('data.data');
        foreach ($responseData as $profile) {
            $this->assertEquals($this->organization->id, $profile['organization_id']);
        }
    }

    public function test_super_admin_can_list_all_profiles()
    {
        Sanctum::actingAs($this->superAdminUser);

        Profile::factory()->create(['organization_id' => $this->organization->id]);
        Profile::factory()->create(['organization_id' => $this->otherOrganization->id]);

        $response = $this->getJson('/api/profiles');

        $response->assertStatus(200);

        // Super admin should see profiles from all organizations
        $responseData = $response->json('data');
        $organizationIds = array_unique(array_column($responseData, 'organization_id'));
        $this->assertContains($this->organization->id, $organizationIds);
        $this->assertContains($this->otherOrganization->id, $organizationIds);
    }

    public function test_regular_user_cannot_list_profiles()
    {
        Sanctum::actingAs($this->regularUser);

        $response = $this->getJson('/api/profiles');

        $response->assertStatus(403);
    }

    public function test_admin_can_create_profile_in_own_organization()
    {
        Sanctum::actingAs($this->adminUser);

        $profileData = [
            'organization_id' => $this->organization->id,
            'name' => 'Test Profile',
            'slug' => 'test_profile',
            'description' => 'Test profile description',
            'is_admin' => false,
            'is_super_admin' => false
        ];

        $response = $this->postJson('/api/profiles', $profileData);

        $response->assertStatus(201)
                ->assertJsonStructure([
                    'data' => [
                        'id',
                        'organization_id',
                        'name',
                        'slug',
                        'description',
                        'is_admin',
                        'is_super_admin',
                        'created_at',
                        'updated_at'
                    ]
                ])
                ->assertJsonFragment([
                    'name' => 'Test Profile',
                    'slug' => 'test_profile',
                    'organization_id' => $this->organization->id
                ]);

        $this->assertDatabaseHas('profiles', [
            'name' => 'Test Profile',
            'slug' => 'test_profile',
            'organization_id' => $this->organization->id
        ]);
    }

    public function test_admin_cannot_create_profile_in_other_organization()
    {
        Sanctum::actingAs($this->adminUser);

        $profileData = [
            'organization_id' => $this->otherOrganization->id,
            'name' => 'Test Profile',
            'slug' => 'test_profile',
            'description' => 'Test profile description',
            'is_admin' => false,
            'is_super_admin' => false
        ];

        $response = $this->postJson('/api/profiles', $profileData);

        $response->assertStatus(403);
    }

    public function test_admin_can_show_profile_from_own_organization()
    {
        Sanctum::actingAs($this->adminUser);

        $profile = Profile::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $response = $this->getJson("/api/profiles/{$profile->id}");

        $response->assertStatus(200)
                ->assertJsonFragment([
                    'id' => $profile->id,
                    'organization_id' => $this->organization->id
                ]);
    }

    public function test_admin_cannot_show_profile_from_other_organization()
    {
        Sanctum::actingAs($this->adminUser);

        $profile = Profile::factory()->create([
            'organization_id' => $this->otherOrganization->id
        ]);

        $response = $this->getJson("/api/profiles/{$profile->id}");

        $response->assertStatus(404);
    }

    public function test_admin_can_update_profile_in_own_organization()
    {
        Sanctum::actingAs($this->adminUser);

        $profile = Profile::factory()->create([
            'organization_id' => $this->organization->id,
            'name' => 'Old Name'
        ]);

        $updateData = [
            'organization_id' => $this->organization->id,
            'name' => 'New Name',
            'slug' => 'new_slug',
            'description' => 'Updated description',
            'is_admin' => false,
            'is_super_admin' => false
        ];

        $response = $this->putJson("/api/profiles/{$profile->id}", $updateData);

        $response->assertStatus(200)
                ->assertJsonFragment([
                    'name' => 'New Name',
                    'slug' => 'new_slug'
                ]);

        $this->assertDatabaseHas('profiles', [
            'id' => $profile->id,
            'name' => 'New Name'
        ]);
    }

    public function test_admin_can_delete_profile_from_own_organization()
    {
        Sanctum::actingAs($this->adminUser);

        $profile = Profile::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $response = $this->deleteJson("/api/profiles/{$profile->id}");

        $response->assertStatus(200)
                ->assertJsonFragment([
                    'message' => 'Profile deleted successfully'
                ]);

        $this->assertDatabaseMissing('profiles', [
            'id' => $profile->id
        ]);
    }

    public function test_create_profile_validates_required_fields()
    {
        Sanctum::actingAs($this->adminUser);

        $response = $this->postJson('/api/profiles', []);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['organization_id', 'name', 'slug']);
    }

    public function test_unauthenticated_user_cannot_access_profiles()
    {
        $response = $this->getJson('/api/profiles');

        $response->assertStatus(401);
    }
}
