<?php

namespace Tests\Unit\UseCases\Profile;

use Tests\TestCase;
use App\UseCases\Profile\RemovePermission;
use App\Http\Requests\Profile\RemovePermissionRequest;
use App\Repositories\ProfileRepository;
use App\Repositories\PermissionRepository;
use App\Models\User;
use App\Models\Profile as ProfileModel;
use App\Models\Permission as PermissionModel;
use App\Models\Organization;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Mockery;

class RemovePermissionTest extends TestCase
{
    use RefreshDatabase;

    private RemovePermission $useCase;
    private $profileRepository;
    private $permissionRepository;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->profileRepository = Mockery::mock(ProfileRepository::class);
        $this->permissionRepository = Mockery::mock(PermissionRepository::class);
        
        $this->useCase = new RemovePermission(
            $this->profileRepository,
            $this->permissionRepository
        );
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    public function test_perform_removes_permission_successfully_for_super_admin()
    {
        // Create organization and users
        $organization = Organization::factory()->create();
        
        $superAdminProfile = ProfileModel::factory()->create([
            'organization_id' => $organization->id,
            'is_super_admin' => true,
            'is_admin' => false,
        ]);
        
        $superAdminUser = User::factory()->create([
            'organization_id' => $organization->id,
            'profile_id' => $superAdminProfile->id,
        ]);

        $targetProfile = ProfileModel::factory()->create([
            'organization_id' => $organization->id,
        ]);

        $permission = PermissionModel::factory()->create([
            'slug' => 'manage_users'
        ]);

        // Add permission to profile first
        $targetProfile->permissions()->attach($permission->id);

        // Mock request
        $request = Mockery::mock(RemovePermissionRequest::class);
        $request->shouldReceive('get')->with('profile_id')->andReturn($targetProfile->id);
        $request->shouldReceive('get')->with('permission_slug')->andReturn('manage_users');

        // Mock auth
        $this->actingAs($superAdminUser);

        // Mock repositories
        $this->profileRepository->shouldReceive('findById')
            ->with($targetProfile->id)
            ->andReturn($targetProfile);

        $this->permissionRepository->shouldReceive('findBySlug')
            ->with('manage_users')
            ->andReturn($permission);

        $this->profileRepository->shouldReceive('removePermission')
            ->with($targetProfile, $permission)
            ->andReturn(true);

        $result = $this->useCase->perform($request);

        $this->assertIsArray($result);
        $this->assertEquals($targetProfile->id, $result['profile_id']);
        $this->assertEquals('manage_users', $result['permission_slug']);
        $this->assertTrue($result['success']);
    }

    public function test_perform_throws_exception_for_non_admin()
    {
        $organization = Organization::factory()->create();
        
        $regularProfile = ProfileModel::factory()->create([
            'organization_id' => $organization->id,
            'is_super_admin' => false,
            'is_admin' => false,
        ]);
        
        $regularUser = User::factory()->create([
            'organization_id' => $organization->id,
            'profile_id' => $regularProfile->id,
        ]);

        $request = Mockery::mock(RemovePermissionRequest::class);

        $this->actingAs($regularUser);

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('You need admin privileges to remove permissions from profiles.');

        $this->useCase->perform($request);
    }

    public function test_perform_throws_exception_for_different_organization()
    {
        $organization1 = Organization::factory()->create();
        $organization2 = Organization::factory()->create();
        
        $adminProfile = ProfileModel::factory()->create([
            'organization_id' => $organization1->id,
            'is_super_admin' => false,
            'is_admin' => true,
        ]);
        
        $adminUser = User::factory()->create([
            'organization_id' => $organization1->id,
            'profile_id' => $adminProfile->id,
        ]);

        $targetProfile = ProfileModel::factory()->create([
            'organization_id' => $organization2->id,
        ]);

        $request = Mockery::mock(RemovePermissionRequest::class);
        $request->shouldReceive('get')->with('profile_id')->andReturn($targetProfile->id);

        $this->actingAs($adminUser);

        $this->profileRepository->shouldReceive('findById')
            ->with($targetProfile->id)
            ->andReturn($targetProfile);

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('You can only manage profiles from your organization.');

        $this->useCase->perform($request);
    }

    public function test_perform_throws_exception_for_permission_not_in_profile()
    {
        $organization = Organization::factory()->create();
        
        $adminProfile = ProfileModel::factory()->create([
            'organization_id' => $organization->id,
            'is_super_admin' => false,
            'is_admin' => true,
        ]);
        
        $adminUser = User::factory()->create([
            'organization_id' => $organization->id,
            'profile_id' => $adminProfile->id,
        ]);

        $targetProfile = ProfileModel::factory()->create([
            'organization_id' => $organization->id,
        ]);

        $permission = PermissionModel::factory()->create([
            'slug' => 'manage_users'
        ]);

        // Don't add permission to profile

        $request = Mockery::mock(RemovePermissionRequest::class);
        $request->shouldReceive('get')->with('profile_id')->andReturn($targetProfile->id);
        $request->shouldReceive('get')->with('permission_slug')->andReturn('manage_users');

        $this->actingAs($adminUser);

        $this->profileRepository->shouldReceive('findById')
            ->with($targetProfile->id)
            ->andReturn($targetProfile);

        $this->permissionRepository->shouldReceive('findBySlug')
            ->with('manage_users')
            ->andReturn($permission);

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Permission not found in this profile.');

        $this->useCase->perform($request);
    }
}
