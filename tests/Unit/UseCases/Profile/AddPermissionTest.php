<?php

namespace Tests\Unit\UseCases\Profile;

use Tests\TestCase;
use App\UseCases\Profile\AddPermission;
use App\Repositories\ProfileRepository;
use App\Repositories\PermissionRepository;
use App\Http\Requests\Profile\AddPermissionRequest;
use App\Models\User;
use App\Models\Profile as ProfileModel;
use App\Models\Permission as PermissionModel;
use Mockery;
use Exception;
use Illuminate\Support\Facades\DB;

class AddPermissionTest extends TestCase
{
    private $profileRepository;
    private $permissionRepository;
    private AddPermission $useCase;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->profileRepository = Mockery::mock(ProfileRepository::class);
        $this->permissionRepository = Mockery::mock(PermissionRepository::class);
        
        $this->useCase = new AddPermission(
            $this->profileRepository,
            $this->permissionRepository
        );
    }

    public function test_perform_adds_permission_successfully_for_super_admin()
    {
        // Arrange
        DB::shouldReceive('beginTransaction')->once();
        DB::shouldReceive('commit')->once();

        $user = new User();
        $user->organization_id = 1;
        $profile = new ProfileModel();
        $profile->is_super_admin = true;
        $profile->is_admin = false;
        $user->setRelation('profile', $profile);
        
        $this->actingAs($user);

        $request = new AddPermissionRequest();
        $request->merge([
            'profile_id' => 2,
            'permission_slug' => 'manage_users'
        ]);

        $targetProfile = new ProfileModel();
        $targetProfile->id = 2;
        $targetProfile->organization_id = 1;

        $permission = new PermissionModel();
        $permission->id = 1;
        $permission->slug = 'manage_users';

        $permissionsRelation = Mockery::mock();
        $permissionsRelation->shouldReceive('where')
            ->with('permission_id', 1)
            ->once()
            ->andReturnSelf();
        $permissionsRelation->shouldReceive('exists')
            ->once()
            ->andReturn(false);
        $permissionsRelation->shouldReceive('attach')
            ->with(1, Mockery::type('array'))
            ->once();

        $targetProfile->shouldReceive('permissions')
            ->twice()
            ->andReturn($permissionsRelation);

        // Mock static methods
        ProfileModel::shouldReceive('findOrFail')
            ->with(2)
            ->once()
            ->andReturn($targetProfile);

        PermissionModel::shouldReceive('where')
            ->with('slug', 'manage_users')
            ->once()
            ->andReturnSelf();
        PermissionModel::shouldReceive('firstOrFail')
            ->once()
            ->andReturn($permission);

        // Act
        $result = $this->useCase->perform($request);

        // Assert
        $this->assertIsArray($result);
        $this->assertTrue($result['success']);
        $this->assertStringContainsString('manage_users', $result['message']);
        $this->assertEquals(2, $result['profile_id']);
        $this->assertEquals('manage_users', $result['permission_slug']);
    }

    public function test_perform_throws_exception_for_non_admin()
    {
        // Arrange
        $user = new User();
        $profile = new ProfileModel();
        $profile->is_super_admin = false;
        $profile->is_admin = false;
        $user->setRelation('profile', $profile);
        
        $this->actingAs($user);

        $request = new AddPermissionRequest();
        $request->merge([
            'profile_id' => 2,
            'permission_slug' => 'manage_users'
        ]);

        // Act & Assert
        $this->expectException(Exception::class);
        $this->expectExceptionMessage('You need admin or super admin privileges to manage profile permissions.');
        $this->expectExceptionCode(403);

        $this->useCase->perform($request);
    }

    public function test_perform_throws_exception_for_different_organization()
    {
        // Arrange
        DB::shouldReceive('beginTransaction')->once();
        DB::shouldReceive('rollBack')->once();

        $user = new User();
        $user->organization_id = 1;
        $profile = new ProfileModel();
        $profile->is_super_admin = false;
        $profile->is_admin = true;
        $user->setRelation('profile', $profile);
        
        $this->actingAs($user);

        $request = new AddPermissionRequest();
        $request->merge([
            'profile_id' => 2,
            'permission_slug' => 'manage_users'
        ]);

        $targetProfile = new ProfileModel();
        $targetProfile->id = 2;
        $targetProfile->organization_id = 2; // Different organization

        $permission = new PermissionModel();
        $permission->id = 1;
        $permission->slug = 'manage_users';

        ProfileModel::shouldReceive('findOrFail')
            ->with(2)
            ->once()
            ->andReturn($targetProfile);

        PermissionModel::shouldReceive('where')
            ->with('slug', 'manage_users')
            ->once()
            ->andReturnSelf();
        PermissionModel::shouldReceive('firstOrFail')
            ->once()
            ->andReturn($permission);

        // Act & Assert
        $this->expectException(Exception::class);
        $this->expectExceptionMessage('You can only manage permissions for profiles in your organization.');
        $this->expectExceptionCode(403);

        $this->useCase->perform($request);
    }

    public function test_perform_throws_exception_for_duplicate_permission()
    {
        // Arrange
        DB::shouldReceive('beginTransaction')->once();
        DB::shouldReceive('rollBack')->once();

        $user = new User();
        $user->organization_id = 1;
        $profile = new ProfileModel();
        $profile->is_super_admin = true;
        $user->setRelation('profile', $profile);
        
        $this->actingAs($user);

        $request = new AddPermissionRequest();
        $request->merge([
            'profile_id' => 2,
            'permission_slug' => 'manage_users'
        ]);

        $targetProfile = new ProfileModel();
        $targetProfile->id = 2;
        $targetProfile->organization_id = 1;

        $permission = new PermissionModel();
        $permission->id = 1;
        $permission->slug = 'manage_users';

        $permissionsRelation = Mockery::mock();
        $permissionsRelation->shouldReceive('where')
            ->with('permission_id', 1)
            ->once()
            ->andReturnSelf();
        $permissionsRelation->shouldReceive('exists')
            ->once()
            ->andReturn(true); // Permission already exists

        $targetProfile->shouldReceive('permissions')
            ->once()
            ->andReturn($permissionsRelation);

        ProfileModel::shouldReceive('findOrFail')
            ->with(2)
            ->once()
            ->andReturn($targetProfile);

        PermissionModel::shouldReceive('where')
            ->with('slug', 'manage_users')
            ->once()
            ->andReturnSelf();
        PermissionModel::shouldReceive('firstOrFail')
            ->once()
            ->andReturn($permission);

        // Act & Assert
        $this->expectException(Exception::class);
        $this->expectExceptionMessage("Permission 'manage_users' is already assigned to this profile.");
        $this->expectExceptionCode(400);

        $this->useCase->perform($request);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }
}
