<?php

namespace Tests\Unit\UseCases\User;

use App\UseCases\User\CheckSpecificUserPermission;
use App\Repositories\UserRepository;
use App\Repositories\ProfileRepository;
use App\Repositories\PermissionRepository;
use App\Models\User as UserModel;
use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Laravel\Sanctum\Sanctum;

class CheckSpecificUserPermissionTest extends TestCase
{
    use RefreshDatabase;

    private CheckSpecificUserPermission $useCase;
    private UserRepository $userRepository;
    private ProfileRepository $profileRepository;
    private PermissionRepository $permissionRepository;

    protected function setUp(): void
    {
        parent::setUp();

        $this->userRepository = app()->make(UserRepository::class);
        $this->profileRepository = app()->make(ProfileRepository::class);
        $this->permissionRepository = app()->make(PermissionRepository::class);
        $this->useCase = new CheckSpecificUserPermission(
            $this->userRepository,
            $this->profileRepository,
            $this->permissionRepository
        );
    }

    public function test_throws_exception_when_user_not_authenticated()
    {
        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('User not authenticated');

        $this->useCase->perform('test-permission');
    }

    public function test_returns_false_when_permission_does_not_exist()
    {
        $user = UserModel::factory()->create();
        Sanctum::actingAs($user);

        $result = $this->useCase->perform('non-existent-permission');

        $this->assertIsArray($result);
        $this->assertArrayHasKey('user', $result);
        $this->assertArrayHasKey('permission', $result);
        $this->assertArrayHasKey('has_permission', $result);
        $this->assertArrayHasKey('reason', $result);

        $this->assertFalse($result['permission']['exists']);
        $this->assertFalse($result['has_permission']);
        $this->assertEquals('Permission does not exist', $result['reason']);
    }



    public function test_returns_false_when_user_has_no_profile()
    {
        // Create permission
        $permission = \App\Models\Permission::factory()->create([
            'slug' => 'test-permission',
            'name' => 'Test Permission'
        ]);

        // Create user without profile
        $user = UserModel::factory()->create([
            'profile_id' => null
        ]);

        Sanctum::actingAs($user);

        $result = $this->useCase->perform('test-permission');

        $this->assertIsArray($result);
        $this->assertFalse($result['has_permission']);
        $this->assertEquals('User has no profile assigned', $result['reason']);
        $this->assertTrue($result['permission']['exists']);
    }

    public function test_returns_true_for_super_admin_profile()
    {
        // Create organization
        $organization = \App\Models\Organization::factory()->create();

        // Create permission
        $permission = \App\Models\Permission::factory()->create([
            'slug' => 'test-permission',
            'name' => 'Test Permission'
        ]);

        // Create super admin profile
        $profile = \App\Models\Profile::factory()->create([
            'organization_id' => $organization->id,
            'is_admin' => false,
            'is_super_admin' => true
        ]);

        // Create user with super admin profile
        $user = UserModel::factory()->create([
            'organization_id' => $organization->id,
            'profile_id' => $profile->id
        ]);

        Sanctum::actingAs($user);

        $result = $this->useCase->perform('test-permission');

        $this->assertIsArray($result);
        $this->assertTrue($result['has_permission']);
        $this->assertEquals('Profile is super admin', $result['reason']);
        $this->assertTrue($result['profile']['is_super_admin']);
    }

    public function test_returns_true_for_admin_profile()
    {
        // Create organization
        $organization = \App\Models\Organization::factory()->create();

        // Create permission
        $permission = \App\Models\Permission::factory()->create([
            'slug' => 'test-permission',
            'name' => 'Test Permission'
        ]);

        // Create admin profile
        $profile = \App\Models\Profile::factory()->create([
            'organization_id' => $organization->id,
            'is_admin' => true,
            'is_super_admin' => false
        ]);

        // Create user with admin profile
        $user = UserModel::factory()->create([
            'organization_id' => $organization->id,
            'profile_id' => $profile->id
        ]);

        Sanctum::actingAs($user);

        $result = $this->useCase->perform('test-permission');

        $this->assertIsArray($result);
        $this->assertTrue($result['has_permission']);
        $this->assertEquals('Profile is admin', $result['reason']);
        $this->assertTrue($result['profile']['is_admin']);
    }

    public function test_returns_true_when_profile_has_specific_permission()
    {
        // Create organization
        $organization = \App\Models\Organization::factory()->create();

        // Create permission
        $permission = \App\Models\Permission::factory()->create([
            'slug' => 'test-permission',
            'name' => 'Test Permission'
        ]);

        // Create regular profile with specific permission
        $profile = \App\Models\Profile::factory()->create([
            'organization_id' => $organization->id,
            'is_admin' => false,
            'is_super_admin' => false
        ]);

        $profile->permissions()->attach($permission->id);

        // Create user with profile
        $user = UserModel::factory()->create([
            'organization_id' => $organization->id,
            'profile_id' => $profile->id
        ]);

        Sanctum::actingAs($user);

        $result = $this->useCase->perform('test-permission');

        $this->assertIsArray($result);
        $this->assertTrue($result['has_permission']);
        $this->assertEquals('Permission explicitly granted to profile', $result['reason']);
        $this->assertFalse($result['profile']['is_admin']);
        $this->assertFalse($result['profile']['is_super_admin']);
    }

    public function test_returns_false_when_profile_does_not_have_permission()
    {
        // Create organization
        $organization = \App\Models\Organization::factory()->create();

        // Create permission
        $permission = \App\Models\Permission::factory()->create([
            'slug' => 'test-permission',
            'name' => 'Test Permission'
        ]);

        // Create regular profile without permission
        $profile = \App\Models\Profile::factory()->create([
            'organization_id' => $organization->id,
            'is_admin' => false,
            'is_super_admin' => false
        ]);

        // Create user with profile
        $user = UserModel::factory()->create([
            'organization_id' => $organization->id,
            'profile_id' => $profile->id
        ]);

        Sanctum::actingAs($user);

        $result = $this->useCase->perform('test-permission');

        $this->assertIsArray($result);
        $this->assertFalse($result['has_permission']);
        $this->assertEquals('Permission not granted to profile', $result['reason']);
        $this->assertFalse($result['profile']['is_admin']);
        $this->assertFalse($result['profile']['is_super_admin']);
    }
}
