<?php

namespace Tests\Unit\UseCases\User;

use App\UseCases\User\FetchUserPermissions;
use App\Repositories\UserRepository;
use App\Repositories\ProfileRepository;
use App\Domains\User;
use App\Domains\Profile;
use App\Domains\Permission;
use App\Models\User as UserModel;
use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Laravel\Sanctum\Sanctum;

class FetchUserPermissionsTest extends TestCase
{
    use RefreshDatabase;

    private FetchUserPermissions $useCase;
    private UserRepository $userRepository;
    private ProfileRepository $profileRepository;

    protected function setUp(): void
    {
        parent::setUp();

        $this->userRepository = app()->make(UserRepository::class);
        $this->profileRepository = app()->make(ProfileRepository::class);
        $this->useCase = new FetchUserPermissions(
            $this->userRepository,
            $this->profileRepository
        );
    }

    public function test_throws_exception_when_user_not_authenticated()
    {
        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('User not authenticated');

        $this->useCase->perform();
    }

    public function test_returns_empty_permissions_when_user_has_no_profile()
    {
        // Create user without profile
        $user = UserModel::factory()->create([
            'profile_id' => null
        ]);

        Sanctum::actingAs($user);

        $result = $this->useCase->perform();

        $this->assertIsArray($result);
        $this->assertArrayHasKey('user', $result);
        $this->assertArrayHasKey('profile', $result);
        $this->assertArrayHasKey('permissions', $result);
        $this->assertArrayHasKey('can_access_all', $result);

        $this->assertEquals($user->id, $result['user']['id']);
        $this->assertNull($result['profile']);
        $this->assertEmpty($result['permissions']);
        $this->assertFalse($result['can_access_all']);
    }



    public function test_returns_user_permissions_with_profile()
    {
        // Create organization
        $organization = \App\Models\Organization::factory()->create();

        // Create permissions
        $permission1 = \App\Models\Permission::factory()->create([
            'slug' => 'test-permission-1',
            'name' => 'Test Permission 1'
        ]);

        $permission2 = \App\Models\Permission::factory()->create([
            'slug' => 'test-permission-2',
            'name' => 'Test Permission 2'
        ]);

        // Create profile with permissions
        $profile = \App\Models\Profile::factory()->create([
            'organization_id' => $organization->id,
            'name' => 'Test Profile',
            'is_admin' => false,
            'is_super_admin' => false
        ]);

        $profile->permissions()->attach([$permission1->id, $permission2->id]);

        // Create user with profile
        $user = UserModel::factory()->create([
            'organization_id' => $organization->id,
            'profile_id' => $profile->id
        ]);

        Sanctum::actingAs($user);

        $result = $this->useCase->perform();

        $this->assertIsArray($result);
        $this->assertArrayHasKey('user', $result);
        $this->assertArrayHasKey('profile', $result);
        $this->assertArrayHasKey('permissions', $result);
        $this->assertArrayHasKey('can_access_all', $result);

        $this->assertEquals($user->id, $result['user']['id']);
        $this->assertEquals($profile->id, $result['profile']['id']);
        $this->assertEquals($profile->name, $result['profile']['name']);
        $this->assertCount(2, $result['permissions']);
        $this->assertFalse($result['can_access_all']);

        // Check permissions structure
        $permissionSlugs = array_column($result['permissions'], 'slug');
        $this->assertContains('test-permission-1', $permissionSlugs);
        $this->assertContains('test-permission-2', $permissionSlugs);
    }

    public function test_returns_admin_access_for_admin_profile()
    {
        // Create organization
        $organization = \App\Models\Organization::factory()->create();

        // Create admin profile
        $profile = \App\Models\Profile::factory()->create([
            'organization_id' => $organization->id,
            'name' => 'Admin Profile',
            'is_admin' => true,
            'is_super_admin' => false
        ]);

        // Create user with admin profile
        $user = UserModel::factory()->create([
            'organization_id' => $organization->id,
            'profile_id' => $profile->id
        ]);

        Sanctum::actingAs($user);

        $result = $this->useCase->perform();

        $this->assertIsArray($result);
        $this->assertTrue($result['can_access_all']);
        $this->assertTrue($result['profile']['is_admin']);
        $this->assertFalse($result['profile']['is_super_admin']);
    }

    public function test_returns_super_admin_access_for_super_admin_profile()
    {
        // Create organization
        $organization = \App\Models\Organization::factory()->create();

        // Create super admin profile
        $profile = \App\Models\Profile::factory()->create([
            'organization_id' => $organization->id,
            'name' => 'Super Admin Profile',
            'is_admin' => false,
            'is_super_admin' => true
        ]);

        // Create user with super admin profile
        $user = UserModel::factory()->create([
            'organization_id' => $organization->id,
            'profile_id' => $profile->id
        ]);

        Sanctum::actingAs($user);

        $result = $this->useCase->perform();

        $this->assertIsArray($result);
        $this->assertTrue($result['can_access_all']);
        $this->assertFalse($result['profile']['is_admin']);
        $this->assertTrue($result['profile']['is_super_admin']);
    }
}
