<?php

namespace Tests\Unit\UseCases\Permission;

use Tests\TestCase;
use App\UseCases\Permission\Store;
use App\Repositories\PermissionRepository;
use App\Factories\PermissionFactory;
use App\Domains\Permission;
use App\Http\Requests\Permission\CreatePermissionRequest;
use App\Models\User;
use App\Models\Profile;
use Mockery;
use Exception;

class StoreTest extends TestCase
{
    private $permissionRepository;
    private $permissionFactory;
    private Store $useCase;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->permissionRepository = Mockery::mock(PermissionRepository::class);
        $this->permissionFactory = Mockery::mock(PermissionFactory::class);
        
        $this->useCase = new Store(
            $this->permissionRepository,
            $this->permissionFactory
        );
    }

    public function test_perform_creates_permission_successfully_for_super_admin()
    {
        // Arrange
        $user = new User();
        $profile = new Profile();
        $profile->is_super_admin = true;
        $user->setRelation('profile', $profile);
        
        $this->actingAs($user);

        $request = new CreatePermissionRequest();
        $request->merge([
            'slug' => 'manage_users',
            'name' => 'Manage Users',
            'description' => 'Permission to manage users'
        ]);

        $permissionDomain = new Permission(
            null,
            'manage_users',
            'Manage Users',
            'Permission to manage users',
            now(),
            now()
        );

        $storedPermission = new Permission(
            1,
            'manage_users',
            'Manage Users',
            'Permission to manage users',
            now(),
            now()
        );

        $this->permissionFactory
            ->shouldReceive('buildFromStoreRequest')
            ->with($request)
            ->once()
            ->andReturn($permissionDomain);

        $this->permissionRepository
            ->shouldReceive('store')
            ->with($permissionDomain)
            ->once()
            ->andReturn($storedPermission);

        // Act
        $result = $this->useCase->perform($request);

        // Assert
        $this->assertInstanceOf(Permission::class, $result);
        $this->assertEquals(1, $result->id);
        $this->assertEquals('manage_users', $result->slug);
        $this->assertEquals('Manage Users', $result->name);
    }

    public function test_perform_throws_exception_for_non_super_admin()
    {
        // Arrange
        $user = new User();
        $profile = new Profile();
        $profile->is_super_admin = false;
        $profile->is_admin = true; // Even admin should not be able to create permissions
        $user->setRelation('profile', $profile);
        
        $this->actingAs($user);

        $request = new CreatePermissionRequest();
        $request->merge([
            'slug' => 'manage_users',
            'name' => 'Manage Users'
        ]);

        // Act & Assert
        $this->expectException(Exception::class);
        $this->expectExceptionMessage('You need super admin privileges to create permissions.');
        $this->expectExceptionCode(403);

        $this->useCase->perform($request);
    }

    public function test_perform_throws_exception_for_user_without_profile()
    {
        // Arrange
        $user = new User();
        $user->setRelation('profile', null);
        
        $this->actingAs($user);

        $request = new CreatePermissionRequest();

        // Act & Assert
        $this->expectException(Exception::class);
        $this->expectExceptionMessage('You need super admin privileges to create permissions.');
        $this->expectExceptionCode(403);

        $this->useCase->perform($request);
    }

    public function test_perform_throws_exception_for_unauthenticated_user()
    {
        // Arrange
        $request = new CreatePermissionRequest();

        // Act & Assert
        $this->expectException(Exception::class);
        $this->expectExceptionMessage('You need super admin privileges to create permissions.');
        $this->expectExceptionCode(403);

        $this->useCase->perform($request);
    }

    public function test_perform_handles_repository_exception()
    {
        // Arrange
        $user = new User();
        $profile = new Profile();
        $profile->is_super_admin = true;
        $user->setRelation('profile', $profile);
        
        $this->actingAs($user);

        $request = new CreatePermissionRequest();
        $request->merge([
            'slug' => 'manage_users',
            'name' => 'Manage Users'
        ]);

        $permissionDomain = new Permission(
            null,
            'manage_users',
            'Manage Users',
            null,
            now(),
            now()
        );

        $this->permissionFactory
            ->shouldReceive('buildFromStoreRequest')
            ->with($request)
            ->once()
            ->andReturn($permissionDomain);

        $this->permissionRepository
            ->shouldReceive('store')
            ->with($permissionDomain)
            ->once()
            ->andThrow(new Exception('Database error'));

        // Act & Assert
        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Database error');

        $this->useCase->perform($request);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }
}
