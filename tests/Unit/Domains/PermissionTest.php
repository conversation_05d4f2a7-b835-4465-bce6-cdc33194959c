<?php

namespace Tests\Unit\Domains;

use Tests\TestCase;
use App\Domains\Permission;
use Carbon\Carbon;

class PermissionTest extends TestCase
{
    public function test_permission_domain_can_be_created_with_all_properties()
    {
        $now = Carbon::now();
        
        $permission = new Permission(
            1,
            'manage_users',
            'Manage Users',
            'Permission to manage users in the system',
            $now,
            $now
        );

        $this->assertEquals(1, $permission->id);
        $this->assertEquals('manage_users', $permission->slug);
        $this->assertEquals('Manage Users', $permission->name);
        $this->assertEquals('Permission to manage users in the system', $permission->description);
        $this->assertEquals($now, $permission->created_at);
        $this->assertEquals($now, $permission->updated_at);
    }

    public function test_permission_domain_can_be_created_without_id()
    {
        $now = Carbon::now();
        
        $permission = new Permission(
            null,
            'manage_products',
            'Manage Products',
            'Permission to manage products',
            $now,
            $now
        );

        $this->assertNull($permission->id);
        $this->assertEquals('manage_products', $permission->slug);
        $this->assertEquals('Manage Products', $permission->name);
    }

    public function test_to_array_returns_correct_structure()
    {
        $now = Carbon::now();
        
        $permission = new Permission(
            1,
            'view_reports',
            'View Reports',
            'Permission to view reports',
            $now,
            $now
        );

        $array = $permission->toArray();

        $this->assertIsArray($array);
        $this->assertEquals(1, $array['id']);
        $this->assertEquals('view_reports', $array['slug']);
        $this->assertEquals('View Reports', $array['name']);
        $this->assertEquals('Permission to view reports', $array['description']);
        $this->assertEquals($now, $array['created_at']);
        $this->assertEquals($now, $array['updated_at']);
    }

    public function test_to_store_array_excludes_id_and_timestamps()
    {
        $now = Carbon::now();
        
        $permission = new Permission(
            1,
            'delete_users',
            'Delete Users',
            'Permission to delete users',
            $now,
            $now
        );

        $storeArray = $permission->toStoreArray();

        $this->assertIsArray($storeArray);
        $this->assertArrayNotHasKey('id', $storeArray);
        $this->assertArrayNotHasKey('created_at', $storeArray);
        $this->assertArrayNotHasKey('updated_at', $storeArray);
        $this->assertEquals('delete_users', $storeArray['slug']);
        $this->assertEquals('Delete Users', $storeArray['name']);
        $this->assertEquals('Permission to delete users', $storeArray['description']);
    }

    public function test_to_update_array_excludes_id_and_timestamps()
    {
        $now = Carbon::now();
        
        $permission = new Permission(
            1,
            'edit_profiles',
            'Edit Profiles',
            'Permission to edit user profiles',
            $now,
            $now
        );

        $updateArray = $permission->toUpdateArray();

        $this->assertIsArray($updateArray);
        $this->assertArrayNotHasKey('id', $updateArray);
        $this->assertArrayNotHasKey('created_at', $updateArray);
        $this->assertArrayNotHasKey('updated_at', $updateArray);
        $this->assertEquals('edit_profiles', $updateArray['slug']);
        $this->assertEquals('Edit Profiles', $updateArray['name']);
        $this->assertEquals('Permission to edit user profiles', $updateArray['description']);
    }

    public function test_permission_domain_handles_null_description()
    {
        $now = Carbon::now();
        
        $permission = new Permission(
            1,
            'basic_access',
            'Basic Access',
            null,
            $now,
            $now
        );

        $this->assertNull($permission->description);
        $this->assertNull($permission->toArray()['description']);
    }
}
