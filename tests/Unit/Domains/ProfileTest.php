<?php

namespace Tests\Unit\Domains;

use App\Domains\Profile;
use Tests\Unit\Domains\BaseDomainTest;
use Carbon\Carbon;

class ProfileTest extends BaseDomainTest
{
    public function test_domain_instantiation()
    {
        $domain = $this->createDomainInstance();

        $this->assertInstanceOf(Profile::class, $domain);
        $this->assertEquals(1, $domain->id);
        $this->assertEquals(1, $domain->organization_id);
        $this->assertEquals('Administrator', $domain->name);
        $this->assertEquals('admin', $domain->slug);
        $this->assertTrue($domain->is_admin);
        $this->assertFalse($domain->is_super_admin);
    }

    public function test_to_array_method()
    {
        $domain = $this->createDomainInstance();
        $array = $domain->toArray();

        $this->assertIsArray($array);
        $this->assertArrayStructure($this->getExpectedArrayKeys(), $array);
    }

    public function test_is_super_admin_method()
    {
        $superAdmin = new Profile(1, 1, 'Super Admin', 'super_admin', 'Super admin', false, true, Carbon::now(), Carbon::now(), []);
        $admin = new Profile(2, 1, 'Admin', 'admin', 'Admin', true, false, Carbon::now(), Carbon::now(), []);
        $user = new Profile(3, 1, 'User', 'user', 'Regular user', false, false, Carbon::now(), Carbon::now(), []);

        $this->assertTrue($superAdmin->isSuperAdmin());
        $this->assertFalse($admin->isSuperAdmin());
        $this->assertFalse($user->isSuperAdmin());
    }

    public function test_is_admin_method()
    {
        $superAdmin = new Profile(1, 1, 'Super Admin', 'super_admin', 'Super admin', false, true, Carbon::now(), Carbon::now(), []);
        $admin = new Profile(2, 1, 'Admin', 'admin', 'Admin', true, false, Carbon::now(), Carbon::now(), []);
        $user = new Profile(3, 1, 'User', 'user', 'Regular user', false, false, Carbon::now(), Carbon::now(), []);

        $this->assertFalse($superAdmin->isAdmin());
        $this->assertTrue($admin->isAdmin());
        $this->assertFalse($user->isAdmin());
    }

    public function test_can_method_for_super_admin()
    {
        $superAdmin = new Profile(1, 1, 'Super Admin', 'super_admin', 'Super admin', false, true, Carbon::now(), Carbon::now(), []);

        $this->assertTrue($superAdmin->can('any_permission'));
        $this->assertTrue($superAdmin->can('manage_users'));
    }

    public function test_can_method_for_admin()
    {
        $admin = new Profile(2, 1, 'Admin', 'admin', 'Admin', true, false, Carbon::now(), Carbon::now(), []);

        $this->assertTrue($admin->can('any_permission'));
        $this->assertTrue($admin->can('manage_users'));
    }

    public function test_can_method_for_regular_user()
    {
        $user = new Profile(3, 1, 'User', 'user', 'Regular user', false, false, Carbon::now(), Carbon::now(), ['view_reports', 'manage_clients']);

        $this->assertTrue($user->can('view_reports'));
        $this->assertTrue($user->can('manage_clients'));
        $this->assertFalse($user->can('manage_users'));
    }

    public function test_can_method_handles_null_permissions()
    {
        $user = new Profile(3, 1, 'User', 'user', 'Regular user', false, false, Carbon::now(), Carbon::now(), null);

        $this->assertFalse($user->can('any_permission'));
    }

    protected function createDomainInstance()
    {
        return new Profile(
            1,
            1,
            'Administrator',
            'admin',
            'Admin profile with full access',
            true,
            false,
            Carbon::now(),
            Carbon::now(),
            ['manage_users', 'view_reports']
        );
    }

    protected function getExpectedArrayKeys(): array
    {
        return [
            'id',
            'organization_id',
            'name',
            'slug',
            'description',
            'is_admin',
            'is_super_admin',
            'created_at',
            'updated_at',
            'permissions'
        ];
    }
}
