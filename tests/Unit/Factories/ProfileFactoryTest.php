<?php

namespace Tests\Unit\Factories;

use App\Factories\ProfileFactory;
use App\Domains\Profile;
use App\Models\Profile as ProfileModel;
use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Carbon\Carbon;

class ProfileFactoryTest extends TestCase
{
    use RefreshDatabase;

    public function test_build_from_model()
    {
        $model = $this->createModelInstance();
        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromModel($model);

        $this->assertInstanceOf($this->getDomainClass(), $domain);
        $this->assertEquals(1, $domain->id);
        $this->assertEquals(1, $domain->organization_id);
        $this->assertEquals('Administrator', $domain->name);
        $this->assertEquals('admin', $domain->slug);
        $this->assertTrue($domain->is_admin);
        $this->assertFalse($domain->is_super_admin);
    }

    public function test_build_from_model_handles_permissions()
    {
        $model = $this->createModelInstance();
        $model->permissions = ['manage_users', 'view_reports'];

        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromModel($model);

        $this->assertEquals(['manage_users', 'view_reports'], $domain->permissions);
    }

    public function test_build_from_model_handles_null_permissions()
    {
        $model = $this->createModelInstance();
        $model->permissions = null;

        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromModel($model);

        $this->assertEquals([], $domain->permissions); // Factory returns empty array, not null
    }

    protected function createFactoryInstance()
    {
        return app()->make(ProfileFactory::class);
    }

    protected function getDomainClass(): string
    {
        return Profile::class;
    }

    protected function createModelInstance()
    {
        $model = new ProfileModel();
        $model->id = 1;
        $model->organization_id = 1;
        $model->name = 'Administrator';
        $model->slug = 'admin';
        $model->description = 'Admin profile';
        $model->is_admin = true;
        $model->is_super_admin = false;
        $model->permissions = [];
        $model->created_at = Carbon::now();
        $model->updated_at = Carbon::now();

        return $model;
    }
}
