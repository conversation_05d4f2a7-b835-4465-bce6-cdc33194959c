<?php

namespace Tests\Unit\Factories;

use Tests\TestCase;
use App\Factories\PermissionFactory;
use App\Domains\Permission;
use App\Models\Permission as PermissionModel;
use App\Http\Requests\Permission\CreatePermissionRequest;
use App\Http\Requests\Permission\UpdatePermissionRequest;
use Carbon\Carbon;

class PermissionFactoryTest extends TestCase
{
    private PermissionFactory $factory;

    protected function setUp(): void
    {
        parent::setUp();
        $this->factory = app()->make(PermissionFactory::class);
    }

    public function test_factory_can_be_instantiated()
    {
        $this->assertInstanceOf(PermissionFactory::class, $this->factory);
    }

    public function test_build_from_model_creates_domain_correctly()
    {
        $now = Carbon::now();

        $model = new PermissionModel();
        $model->id = 1;
        $model->slug = 'manage_users';
        $model->name = 'Manage Users';
        $model->description = 'Permission to manage users';
        $model->created_at = $now;
        $model->updated_at = $now;

        $domain = $this->factory->buildFromModel($model);

        $this->assertInstanceOf(Permission::class, $domain);
        $this->assertEquals(1, $domain->id);
        $this->assertEquals('manage_users', $domain->slug);
        $this->assertEquals('Manage Users', $domain->name);
        $this->assertEquals('Permission to manage users', $domain->description);
        $this->assertInstanceOf(Carbon::class, $domain->created_at);
        $this->assertInstanceOf(Carbon::class, $domain->updated_at);
    }

    public function test_build_from_model_handles_null_values()
    {
        $model = new PermissionModel();
        $model->id = null;
        $model->slug = 'test_permission';
        $model->name = 'Test Permission';
        $model->description = null;
        $model->created_at = null;
        $model->updated_at = null;

        $domain = $this->factory->buildFromModel($model);

        $this->assertInstanceOf(Permission::class, $domain);
        $this->assertNull($domain->id);
        $this->assertEquals('test_permission', $domain->slug);
        $this->assertEquals('Test Permission', $domain->name);
        $this->assertNull($domain->description);
        $this->assertNull($domain->created_at);
        $this->assertNull($domain->updated_at);
    }

    public function test_build_from_store_request_creates_domain_correctly()
    {
        $requestData = [
            'slug' => 'create_reports',
            'name' => 'Create Reports',
            'description' => 'Permission to create reports'
        ];

        $request = new CreatePermissionRequest();
        $request->merge($requestData);

        $domain = $this->factory->buildFromStoreRequest($request);

        $this->assertInstanceOf(Permission::class, $domain);
        $this->assertNull($domain->id); // Should be null for new records
        $this->assertEquals('create_reports', $domain->slug);
        $this->assertEquals('Create Reports', $domain->name);
        $this->assertEquals('Permission to create reports', $domain->description);
        $this->assertNull($domain->created_at); // Factory doesn't set timestamps for store requests
        $this->assertNull($domain->updated_at);
    }

    public function test_build_from_update_request_creates_domain_correctly()
    {
        $requestData = [
            'slug' => 'edit_reports',
            'name' => 'Edit Reports',
            'description' => 'Permission to edit reports'
        ];

        $request = new UpdatePermissionRequest();
        $request->merge($requestData);

        $domain = $this->factory->buildFromUpdateRequest($request);

        $this->assertInstanceOf(Permission::class, $domain);
        $this->assertNull($domain->id); // Should be null for updates (ID comes from route)
        $this->assertEquals('edit_reports', $domain->slug);
        $this->assertEquals('Edit Reports', $domain->name);
        $this->assertEquals('Permission to edit reports', $domain->description);
        $this->assertNull($domain->created_at); // Should be null for updates
        $this->assertNull($domain->updated_at); // Factory doesn't set timestamps for update requests
    }

    public function test_build_from_store_request_handles_missing_description()
    {
        $requestData = [
            'slug' => 'basic_access',
            'name' => 'Basic Access'
            // description is missing
        ];

        $request = new CreatePermissionRequest();
        $request->merge($requestData);

        $domain = $this->factory->buildFromStoreRequest($request);

        $this->assertInstanceOf(Permission::class, $domain);
        $this->assertEquals('basic_access', $domain->slug);
        $this->assertEquals('Basic Access', $domain->name);
        $this->assertNull($domain->description);
    }

    public function test_build_from_update_request_handles_missing_description()
    {
        $requestData = [
            'slug' => 'updated_access',
            'name' => 'Updated Access'
            // description is missing
        ];

        $request = new UpdatePermissionRequest();
        $request->merge($requestData);

        $domain = $this->factory->buildFromUpdateRequest($request);

        $this->assertInstanceOf(Permission::class, $domain);
        $this->assertEquals('updated_access', $domain->slug);
        $this->assertEquals('Updated Access', $domain->name);
        $this->assertNull($domain->description);
    }

    public function test_factory_creates_different_instances()
    {
        $model1 = new PermissionModel();
        $model1->id = 1;
        $model1->slug = 'permission1';
        $model1->name = 'Permission 1';

        $model2 = new PermissionModel();
        $model2->id = 2;
        $model2->slug = 'permission2';
        $model2->name = 'Permission 2';

        $domain1 = $this->factory->buildFromModel($model1);
        $domain2 = $this->factory->buildFromModel($model2);

        $this->assertNotSame($domain1, $domain2);
        $this->assertEquals(1, $domain1->id);
        $this->assertEquals(2, $domain2->id);
        $this->assertEquals('permission1', $domain1->slug);
        $this->assertEquals('permission2', $domain2->slug);
    }
}
