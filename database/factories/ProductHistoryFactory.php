<?php

namespace Database\Factories;

use App\Models\Product;
use App\Models\ProductHistory;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\ProductHistory>
 */
class ProductHistoryFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = ProductHistory::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $fields = ['name', 'price', 'description', 'barcode', 'unity'];
        $field = fake()->randomElement($fields);
        
        return [
            'user_id' => User::factory(),
            'product_id' => Product::factory(),
            'field' => $field,
            'alias' => ucfirst($field),
            'old' => fake()->word(),
            'new' => fake()->word(),
        ];
    }

    /**
     * Indicate that the history is for price changes.
     */
    public function priceChange(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'field' => 'price',
                'alias' => 'Preço',
                'old' => fake()->randomFloat(2, 10, 100),
                'new' => fake()->randomFloat(2, 10, 100),
            ];
        });
    }

    /**
     * Indicate that the history is for name changes.
     */
    public function nameChange(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'field' => 'name',
                'alias' => 'Nome',
                'old' => fake()->words(2, true),
                'new' => fake()->words(2, true),
            ];
        });
    }

    /**
     * Indicate that the history is for description changes.
     */
    public function descriptionChange(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'field' => 'description',
                'alias' => 'Descrição',
                'old' => fake()->sentence(),
                'new' => fake()->sentence(),
            ];
        });
    }

    /**
     * Indicate that the history is for barcode changes.
     */
    public function barcodeChange(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'field' => 'barcode',
                'alias' => 'Código de Barras',
                'old' => fake()->ean13(),
                'new' => fake()->ean13(),
            ];
        });
    }
}
