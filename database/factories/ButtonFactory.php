<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Button>
 */
class ButtonFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $type = fake()->randomElement(['QUICK_REPLY', 'URL', 'PHONE_NUMBER']);
        $internal_data = match ($type) {
            'URL' => fake()->url(),
            'PHONE_NUMBER' => fake()->phoneNumber(),
            default => null,
        };

        return [
            'organization_id' => \App\Models\Organization::factory(),
            'text' => fake()->words(2, true),
            'type' => $type,
            'internal_type' => fake()->randomElement(['action', 'condition', 'navigation']),
            'internal_data' => $internal_data,
            'callback_data' => fake()->word(),
            'json' => '{}',
            'commands_to_run' => fake()->randomElement([null, ['command1', 'command2']]),
            'step_to_go' => fake()->randomElement([null, 'next_step', 'another_step']),
            'value' => fake()->randomElement([null, 'P', 'M', 'G', 'option1']),
        ];
    }

    /**
     * Indicate that the button is a quick reply.
     */
    public function quickReply(): static
    {
        return $this->state(fn (array $attributes) => [
            'type' => 'QUICK_REPLY',
            'internal_type' => 'action',
        ]);
    }

    /**
     * Indicate that the button is a URL button.
     */
    public function url(): static
    {
        return $this->state(fn (array $attributes) => [
            'type' => 'URL',
            'internal_data' => fake()->url(),
            'callback_data' => fake()->url(),
        ]);
    }

    /**
     * Indicate that the button is a phone number button.
     */
    public function phoneNumber(): static
    {
        return $this->state(fn (array $attributes) => [
            'type' => 'PHONE_NUMBER',
            'internal_data' => fake()->phoneNumber(),
            'callback_data' => fake()->phoneNumber(),
        ]);
    }

    /**
     * Indicate that the button is a condition button.
     */
    public function condition(): static
    {
        return $this->state(fn (array $attributes) => [
            'internal_type' => 'condition',
        ]);
    }
}
