<?php

namespace Database\Factories;

use App\Models\Organization;
use App\Services\ASAAS\Models\AsaasOrganizationCustomer;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Services\ASAAS\Models\AsaasOrganizationCustomer>
 */
class AsaasOrganizationCustomerFactory extends Factory
{
    protected $model = AsaasOrganizationCustomer::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'organization_id' => Organization::factory(),
            'asaas_customer_id' => 'cus_' . $this->faker->unique()->numerify('##########'),
            'asaas_synced_at' => $this->faker->optional(0.7)->dateTimeBetween('-1 month', 'now'),
            'asaas_sync_errors' => null,
            'sync_status' => $this->faker->randomElement(['pending', 'synced', 'error']),
            'name' => $this->faker->company(),
            'email' => $this->faker->unique()->companyEmail(),
            'phone' => $this->faker->phoneNumber(),
            'mobile_phone' => $this->faker->phoneNumber(),
            'address' => $this->faker->streetAddress(),
            'address_number' => $this->faker->buildingNumber(),
            'complement' => $this->faker->optional(0.3)->secondaryAddress(),
            'province' => $this->faker->citySuffix(),
            'city_name' => $this->faker->city(),
            'state' => $this->faker->stateAbbr(),
            'country' => 'Brasil',
            'postal_code' => $this->faker->postcode(),
            'cpf_cnpj' => $this->faker->numerify('##############'),
            'person_type' => $this->faker->randomElement(['FISICA', 'JURIDICA']),
            'external_reference' => 'org_' . $this->faker->unique()->numerify('####'),
            'notification_disabled' => $this->faker->boolean(20), // 20% chance of being true
            'additional_emails' => $this->faker->optional(0.3)->email(),
            'observations' => $this->faker->optional(0.5)->sentence(),
            'foreign_customer' => $this->faker->boolean(5), // 5% chance of being true
            'deleted' => false,
        ];
    }

    /**
     * Indicate that the customer is pending sync.
     */
    public function pending(): static
    {
        return $this->state(fn (array $attributes) => [
            'sync_status' => 'pending',
            'asaas_synced_at' => null,
            'asaas_sync_errors' => null,
        ]);
    }

    /**
     * Indicate that the customer is synced.
     */
    public function synced(): static
    {
        return $this->state(fn (array $attributes) => [
            'sync_status' => 'synced',
            'asaas_synced_at' => $this->faker->dateTimeBetween('-1 week', 'now'),
            'asaas_sync_errors' => null,
        ]);
    }

    /**
     * Indicate that the customer has sync errors.
     */
    public function withErrors(): static
    {
        return $this->state(fn (array $attributes) => [
            'sync_status' => 'error',
            'asaas_sync_errors' => [
                'error' => 'API Error',
                'message' => 'Failed to sync customer data',
                'timestamp' => now()->toISOString(),
            ],
        ]);
    }

    /**
     * Indicate that the customer needs sync (old sync date).
     */
    public function needsSync(): static
    {
        return $this->state(fn (array $attributes) => [
            'asaas_synced_at' => $this->faker->dateTimeBetween('-1 month', '-2 hours'),
        ]);
    }

    /**
     * Indicate that the customer is a physical person.
     */
    public function fisica(): static
    {
        return $this->state(fn (array $attributes) => [
            'person_type' => 'FISICA',
            'name' => $this->faker->name(),
            'cpf_cnpj' => $this->faker->numerify('###########'),
        ]);
    }

    /**
     * Indicate that the customer is a legal entity.
     */
    public function juridica(): static
    {
        return $this->state(fn (array $attributes) => [
            'person_type' => 'JURIDICA',
            'name' => $this->faker->company(),
            'cpf_cnpj' => $this->faker->numerify('##############'),
        ]);
    }

    /**
     * Indicate that the customer is a foreign customer.
     */
    public function foreign(): static
    {
        return $this->state(fn (array $attributes) => [
            'foreign_customer' => true,
            'country' => $this->faker->country(),
        ]);
    }

    /**
     * Indicate that the customer has notifications disabled.
     */
    public function notificationsDisabled(): static
    {
        return $this->state(fn (array $attributes) => [
            'notification_disabled' => true,
        ]);
    }

    /**
     * Indicate that the customer is deleted.
     */
    public function deleted(): static
    {
        return $this->state(fn (array $attributes) => [
            'deleted' => true,
        ]);
    }

    /**
     * Create customer with specific organization.
     */
    public function forOrganization(Organization $organization): static
    {
        return $this->state(fn (array $attributes) => [
            'organization_id' => $organization->id,
            'external_reference' => "org_{$organization->id}",
            'name' => $organization->name,
            'observations' => $organization->description,
        ]);
    }

    /**
     * Create customer with complete data.
     */
    public function complete(): static
    {
        return $this->state(fn (array $attributes) => [
            'name' => $this->faker->company(),
            'email' => $this->faker->companyEmail(),
            'phone' => $this->faker->phoneNumber(),
            'mobile_phone' => $this->faker->phoneNumber(),
            'address' => $this->faker->streetAddress(),
            'address_number' => $this->faker->buildingNumber(),
            'complement' => $this->faker->secondaryAddress(),
            'province' => $this->faker->citySuffix(),
            'city_name' => $this->faker->city(),
            'state' => $this->faker->stateAbbr(),
            'country' => 'Brasil',
            'postal_code' => $this->faker->postcode(),
            'cpf_cnpj' => $this->faker->numerify('##############'),
            'person_type' => 'JURIDICA',
            'additional_emails' => $this->faker->email(),
            'observations' => $this->faker->sentence(),
        ]);
    }

    /**
     * Create customer with minimal data.
     */
    public function minimal(): static
    {
        return $this->state(fn (array $attributes) => [
            'name' => $this->faker->company(),
            'email' => $this->faker->companyEmail(),
            'cpf_cnpj' => $this->faker->numerify('##############'),
            'phone' => null,
            'mobile_phone' => null,
            'address' => null,
            'address_number' => null,
            'complement' => null,
            'province' => null,
            'city_name' => null,
            'state' => null,
            'postal_code' => null,
            'additional_emails' => null,
            'observations' => null,
        ]);
    }
}
