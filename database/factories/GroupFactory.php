<?php

namespace Database\Factories;

use App\Models\Group;
use App\Models\Organization;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Group>
 */
class GroupFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Group::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'organization_id' => Organization::factory(),
            'name' => fake()->words(2, true),
            'description' => fake()->paragraph(),
        ];
    }

    /**
     * Indicate that the group is for electronics.
     */
    public function electronics(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'name' => 'Electronics',
                'description' => 'Electronic products and components',
            ];
        });
    }

    /**
     * Indicate that the group is for clothing.
     */
    public function clothing(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'name' => 'Clothing',
                'description' => 'Apparel and fashion items',
            ];
        });
    }

    /**
     * Indicate that the group is for books.
     */
    public function books(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'name' => 'Books',
                'description' => 'Books and educational materials',
            ];
        });
    }

    /**
     * Indicate that the group is for office supplies.
     */
    public function officeSupplies(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'name' => 'Office Supplies',
                'description' => 'Office equipment and supplies',
            ];
        });
    }
}
