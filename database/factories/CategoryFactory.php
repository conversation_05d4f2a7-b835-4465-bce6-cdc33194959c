<?php

namespace Database\Factories;

use App\Models\Category;
use App\Models\Organization;
use Illuminate\Database\Eloquent\Factories\Factory;
use Carbon\Carbon;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Category>
 */
class CategoryFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Category::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'organization_id' => Organization::factory(),
            'name' => $this->faker->words(2, true),
            'description' => $this->faker->sentence(),
            'color' => $this->faker->hexColor(),
            'type' => $this->faker->randomElement(['campaign', 'template', 'client']),
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now(),
        ];
    }

    /**
     * Indicate that the category is for campaigns.
     */
    public function forCampaigns(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'type' => 'campaign',
                'name' => $this->faker->randomElement([
                    'Marketing',
                    'Sales',
                    'Support',
                    'Promotional',
                    'Newsletter'
                ]),
            ];
        });
    }

    /**
     * Indicate that the category is for templates.
     */
    public function forTemplates(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'type' => 'template',
                'name' => $this->faker->randomElement([
                    'Welcome',
                    'Confirmation',
                    'Reminder',
                    'Follow-up',
                    'Thank you'
                ]),
            ];
        });
    }
}
