<?php

namespace Database\Factories;

use App\Models\CampaignTagAssignment;
use App\Models\Campaign;
use App\Models\Tag;
use Illuminate\Database\Eloquent\Factories\Factory;
use Carbon\Carbon;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\CampaignTagAssignment>
 */
class CampaignTagAssignmentFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = CampaignTagAssignment::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'campaign_id' => Campaign::factory(),
            'tag_id' => Tag::factory(),
            'assigned_at' => Carbon::now(),
        ];
    }
}
