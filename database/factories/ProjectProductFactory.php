<?php

namespace Database\Factories;

use App\Models\Product;
use App\Models\Project;
use App\Models\ProjectProduct;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\ProjectProduct>
 */
class ProjectProductFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = ProjectProduct::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'project_id' => Project::factory(),
            'product_id' => Product::factory(),
            'quantity' => fake()->numberBetween(1, 100),
            'value' => fake()->randomFloat(2, 10, 1000),
            'description' => fake()->sentence(),
        ];
    }

    /**
     * Indicate that the project product has high quantity.
     */
    public function highQuantity(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'quantity' => fake()->numberBetween(100, 1000),
                'value' => fake()->randomFloat(2, 1000, 10000),
                'description' => 'High quantity project product - ' . fake()->sentence(),
            ];
        });
    }

    /**
     * Indicate that the project product has low quantity.
     */
    public function lowQuantity(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'quantity' => fake()->numberBetween(1, 5),
                'value' => fake()->randomFloat(2, 10, 100),
                'description' => 'Low quantity project product - ' . fake()->sentence(),
            ];
        });
    }

    /**
     * Indicate that the project product is expensive.
     */
    public function expensive(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'value' => fake()->randomFloat(2, 1000, 10000),
                'description' => 'Premium project product - ' . fake()->sentence(),
            ];
        });
    }

    /**
     * Indicate that the project product is for a specific project.
     */
    public function forProject($projectId = null): static
    {
        return $this->state(function (array $attributes) use ($projectId) {
            return [
                'project_id' => $projectId ?? Project::factory(),
            ];
        });
    }

    /**
     * Indicate that the project product is for a specific product.
     */
    public function forProduct($productId = null): static
    {
        return $this->state(function (array $attributes) use ($productId) {
            return [
                'product_id' => $productId ?? Product::factory(),
            ];
        });
    }
}
