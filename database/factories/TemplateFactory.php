<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Template>
 */
class TemplateFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'organization_id' => \App\Models\Organization::factory(),
            'phone_number_id' => null,
            'user_id' => null,
            'client_id' => null,
            'name' => fake()->slug(2),
            'category' => fake()->randomElement(['UTILITY', 'MARKETING', 'AUTHENTICATION']),
            'parameter_format' => fake()->randomElement(['NAMED', 'POSITIONAL']),
            'language' => fake()->randomElement(['en_US', 'es_ES', 'pt_BR', 'en', 'es']),
            'library_template_name' => fake()->optional()->slug(3),
            'id_external' => fake()->optional()->uuid(),
            'status' => fake()->randomElement(['PENDING', 'APPROVED', 'REJECTED', 'DISABLED']),
        ];
    }

    /**
     * Indicate that the template is approved.
     */
    public function approved(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'APPROVED',
        ]);
    }

    /**
     * Indicate that the template is pending.
     */
    public function pending(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'PENDING',
        ]);
    }

    /**
     * Indicate that the template is rejected.
     */
    public function rejected(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'REJECTED',
        ]);
    }
}
