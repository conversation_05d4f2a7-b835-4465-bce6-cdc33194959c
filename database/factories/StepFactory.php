<?php

namespace Database\Factories;

use App\Enums\StepType;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Step>
 */
class StepFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'organization_id' => \App\Models\Organization::factory(),
            'flow_id' => \App\Models\Flow::factory(),
            'step' => fake()->numberBetween(1, 10),
            'type' => fake()->randomElement(['text', 'image', 'video', 'document', 'input']),
            'step_type' => fake()->randomElement(StepType::cases()),
            'position' => fake()->numberBetween(1, 10),
            'next_step' => null,
            'earlier_step' => null,
            'is_initial_step' => false,
            'is_ending_step' => false,
            'configuration' => ['text' => fake()->sentence()],
            'navigation_rules' => null,
            'timeout_seconds' => null,
            'input' => null,
            'json' => '{}',
            'is_input_required' => fake()->boolean(),
            'is_button_click_required' => fake()->boolean(),
            'is_moving_automatic' => fake()->boolean(),
        ];
    }

    /**
     * Create a first step.
     */
    public function first(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_initial_step' => true,
            'step' => 1,
            'position' => 1,
            'earlier_step' => null,
        ]);
    }

    /**
     * Create a last step.
     */
    public function last(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_ending_step' => true,
            'next_step' => null,
        ]);
    }

    /**
     * Create an input step.
     */
    public function input(): static
    {
        return $this->state(fn (array $attributes) => [
            'step_type' => StepType::INPUT,
            'type' => 'input',
            'configuration' => ['input_field' => fake()->randomElement(['client.name', 'client.email', 'client.phone', 'client.address'])],
            'input' => fake()->randomElement(['client.name', 'client.email', 'client.phone', 'client.address']),
        ]);
    }

    /**
     * Create an interactive step.
     */
    public function interactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'step_type' => StepType::INTERACTIVE,
            'type' => 'interactive',
            'configuration' => ['buttons' => [['text' => 'Yes', 'id' => 'yes'], ['text' => 'No', 'id' => 'no']]],
        ]);
    }

    /**
     * Create a message step.
     */
    public function message(): static
    {
        return $this->state(fn (array $attributes) => [
            'step_type' => StepType::MESSAGE,
            'type' => 'message',
            'configuration' => ['text' => fake()->sentence()],
        ]);
    }

    /**
     * Create a condition step.
     */
    public function condition(): static
    {
        return $this->state(fn (array $attributes) => [
            'step_type' => StepType::CONDITION,
            'type' => 'condition',
            'configuration' => ['conditions' => []],
            'navigation_rules' => [
                ['condition_type' => 'default', 'condition_value' => '', 'target_step_id' => 2, 'priority' => 0]
            ],
        ]);
    }

    /**
     * Create a step with buttons.
     */
    public function withButtons(): static
    {
        return $this->state(fn (array $attributes) => [
            'step_type' => StepType::INTERACTIVE,
            'type' => 'interactive',
            'configuration' => ['buttons' => [['text' => 'Option 1', 'id' => 'opt1'], ['text' => 'Option 2', 'id' => 'opt2']]],
        ]);
    }
}
