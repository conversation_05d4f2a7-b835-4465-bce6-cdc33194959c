<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Flow>
 */
class FlowFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'organization_id' => \App\Models\Organization::factory(),
            'name' => fake()->words(3, true),
            'description' => fake()->sentence(),
            'steps_count' => fake()->numberBetween(1, 10),
            'json' => json_encode([
                'flow' => 'data',
                'version' => '1.0'
            ]),
            'is_default_flow' => false,
            'inactivity_minutes' => fake()->numberBetween(30, 120),
            'ending_conversation_message' => fake()->sentence(),
            'version' => '1.0',
            'status' => fake()->randomElement(['draft', 'active', 'archived']),
            'variables' => json_encode([
                'client' => ['name', 'email', 'phone'],
                'flow' => ['current_step', 'started_at']
            ]),
        ];
    }

    /**
     * Indicate that this is a default flow.
     */
    public function default(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_default_flow' => true,
            'name' => 'Default Flow',
            'status' => 'active',
        ]);
    }

    /**
     * Create a simple flow with minimal steps.
     */
    public function simple(): static
    {
        return $this->state(fn (array $attributes) => [
            'steps_count' => fake()->numberBetween(1, 3),
            'name' => 'Simple Flow',
            'inactivity_minutes' => 30,
            'variables' => json_encode(['client' => ['name']]),
        ]);
    }

    /**
     * Create a complex flow with many steps.
     */
    public function complex(): static
    {
        return $this->state(fn (array $attributes) => [
            'steps_count' => fake()->numberBetween(10, 20),
            'name' => 'Complex Flow',
            'inactivity_minutes' => 120,
            'variables' => json_encode([
                'client' => ['name', 'email', 'phone', 'company'],
                'flow' => ['current_step', 'started_at', 'last_interaction'],
                'custom' => ['preference', 'category']
            ]),
        ]);
    }
}
