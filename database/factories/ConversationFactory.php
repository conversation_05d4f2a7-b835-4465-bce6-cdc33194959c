<?php

namespace Database\Factories;

use App\Models\Conversation;
use App\Models\Organization;
use App\Models\User;
use App\Models\Client;
use App\Models\Flow;
use App\Models\PhoneNumber;
use App\Models\Step;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Conversation>
 */
class ConversationFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Conversation::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'organization_id' => Organization::factory(),
            'user_id' => User::factory(),
            'client_id' => Client::factory(),
            'flow_id' => Flow::factory(),
            'phone_number_id' => PhoneNumber::factory(),
            'current_step_id' => null,
            'json' => json_encode([
                'variables' => [
                    'client_name' => $this->faker->name(),
                    'current_step' => 1,
                ],
                'history' => []
            ]),
            'is_finished' => $this->faker->boolean(30),
            'raw_data' => [
                'pizza' => [
                    'size' => $this->faker->randomElement(['P', 'M', 'G']),
                    'flavor' => $this->faker->randomElement(['margherita', 'pepperoni', 'four_cheese']),
                    'price' => $this->faker->randomFloat(2, 20, 50)
                ],
                'client' => [
                    'name' => $this->faker->name(),
                    'phone' => $this->faker->phoneNumber()
                ]
            ],
        ];
    }

    /**
     * Indicate that the conversation is finished.
     */
    public function finished(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_finished' => true,
        ]);
    }

    /**
     * Indicate that the conversation is active.
     */
    public function active(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_finished' => false,
        ]);
    }

    /**
     * Indicate that the conversation has a current step.
     */
    public function withCurrentStep(): static
    {
        return $this->state(fn (array $attributes) => [
            'current_step_id' => Step::factory(),
        ]);
    }

    /**
     * Indicate that the conversation has no phone number.
     */
    public function withoutPhoneNumber(): static
    {
        return $this->state(fn (array $attributes) => [
            'phone_number_id' => null,
        ]);
    }

    /**
     * Indicate that the conversation has no flow.
     */
    public function withoutFlow(): static
    {
        return $this->state(fn (array $attributes) => [
            'flow_id' => null,
        ]);
    }
}
