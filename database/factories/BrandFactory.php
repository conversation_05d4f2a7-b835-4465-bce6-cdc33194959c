<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Brand>
 */
class BrandFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'organization_id' => \App\Models\Organization::factory(),
            'name' => fake()->company(),
            'description' => fake()->sentence(),
        ];
    }

    /**
     * Create a brand with a specific name.
     */
    public function withName(string $name): static
    {
        return $this->state(fn (array $attributes) => [
            'name' => $name,
        ]);
    }

    /**
     * Create a technology brand.
     */
    public function technology(): static
    {
        return $this->state(fn (array $attributes) => [
            'name' => fake()->randomElement(['Apple', 'Samsung', 'Sony', 'Microsoft', 'Google']),
            'description' => 'Technology brand specializing in electronic devices',
        ]);
    }

    /**
     * Create a fashion brand.
     */
    public function fashion(): static
    {
        return $this->state(fn (array $attributes) => [
            'name' => fake()->randomElement(['Nike', 'Adidas', 'Zara', 'H&M', 'Gucci']),
            'description' => 'Fashion brand specializing in clothing and accessories',
        ]);
    }
}
