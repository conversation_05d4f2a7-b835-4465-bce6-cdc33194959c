<?php

namespace Database\Factories;

use App\Models\Department;
use App\Models\Organization;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Department>
 */
class DepartmentFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Department::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'organization_id' => Organization::factory(),
            'name' => fake()->words(2, true),
            'is_active' => fake()->boolean(80), // 80% chance of being active
        ];
    }

    /**
     * Indicate that the department is active.
     */
    public function active(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'is_active' => true,
                'name' => 'Active ' . fake()->words(2, true),
            ];
        });
    }

    /**
     * Indicate that the department is inactive.
     */
    public function inactive(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'is_active' => false,
                'name' => 'Inactive ' . fake()->words(2, true),
            ];
        });
    }

    /**
     * Indicate that the department is for IT.
     */
    public function it(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'name' => 'IT Department',
                'is_active' => true,
            ];
        });
    }

    /**
     * Indicate that the department is for HR.
     */
    public function hr(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'name' => 'Human Resources',
                'is_active' => true,
            ];
        });
    }
}
