<?php

namespace Database\Factories;

use App\Models\Batch;
use App\Models\Brand;
use App\Models\Client;
use App\Models\Organization;
use App\Models\Product;
use App\Models\Project;
use App\Models\Shop;
use App\Models\StockExit;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\StockExit>
 */
class StockExitFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = StockExit::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'organization_id' => Organization::factory(),
            'shop_id' => Shop::factory(),
            'user_id' => User::factory(),
            'brand_id' => Brand::factory(),
            'product_id' => Product::factory(),
            'batch_id' => Batch::factory(),
            'client_id' => Client::factory(),
            'project_id' => Project::factory(),
            'quantity' => fake()->numberBetween(1, 1000),
            'value' => fake()->randomFloat(2, 10, 10000),
            'description' => fake()->sentence(),
        ];
    }

    /**
     * Indicate that the stock exit has high quantity.
     */
    public function highQuantity(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'quantity' => fake()->numberBetween(1000, 10000),
                'value' => fake()->randomFloat(2, 10000, 100000),
                'description' => 'High quantity stock exit - ' . fake()->sentence(),
            ];
        });
    }

    /**
     * Indicate that the stock exit has low quantity.
     */
    public function lowQuantity(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'quantity' => fake()->numberBetween(1, 10),
                'value' => fake()->randomFloat(2, 10, 100),
                'description' => 'Low quantity stock exit - ' . fake()->sentence(),
            ];
        });
    }

    /**
     * Indicate that the stock exit is expensive.
     */
    public function expensive(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'value' => fake()->randomFloat(2, 10000, 100000),
                'description' => 'Premium stock exit - ' . fake()->sentence(),
            ];
        });
    }

    /**
     * Indicate that the stock exit is for a specific project.
     */
    public function forProject($projectId = null): static
    {
        return $this->state(function (array $attributes) use ($projectId) {
            return [
                'project_id' => $projectId ?? Project::factory(),
            ];
        });
    }

    /**
     * Indicate that the stock exit is for a specific client.
     */
    public function forClient($clientId = null): static
    {
        return $this->state(function (array $attributes) use ($clientId) {
            return [
                'client_id' => $clientId ?? Client::factory(),
            ];
        });
    }
}
