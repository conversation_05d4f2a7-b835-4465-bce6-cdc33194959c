<?php

namespace Database\Factories;

use App\Models\Department;
use App\Models\DepartmentUser;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\DepartmentUser>
 */
class DepartmentUserFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = DepartmentUser::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'department_id' => Department::factory(),
            'user_id' => User::factory(),
        ];
    }

    /**
     * Indicate that the assignment is for IT department.
     */
    public function itDepartment(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'department_id' => Department::factory()->it(),
            ];
        });
    }

    /**
     * Indicate that the assignment is for HR department.
     */
    public function hrDepartment(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'department_id' => Department::factory()->hr(),
            ];
        });
    }

    /**
     * Indicate that the assignment is for an active department.
     */
    public function activeDepartment(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'department_id' => Department::factory()->active(),
            ];
        });
    }

    /**
     * Indicate that the assignment is for an inactive department.
     */
    public function inactiveDepartment(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'department_id' => Department::factory()->inactive(),
            ];
        });
    }
}
