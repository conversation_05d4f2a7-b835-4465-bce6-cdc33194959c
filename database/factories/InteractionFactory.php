<?php

namespace Database\Factories;

use App\Models\Interaction;
use App\Models\Organization;
use App\Models\User;
use App\Models\Client;
use App\Models\Flow;
use App\Models\Step;
use App\Models\Conversation;
use App\Models\Button;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Interaction>
 */
class InteractionFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Interaction::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'organization_id' => Organization::factory(),
            'user_id' => User::factory(),
            'client_id' => Client::factory(),
            'flow_id' => Flow::factory(),
            'step_id' => Step::factory(),
            'conversation_id' => Conversation::factory(),
            'message' => $this->faker->sentence(),
            'answer' => $this->faker->sentence(),
            'result' => json_encode([
                'status' => 'success',
                'processed_at' => now()->toISOString()
            ]),
            'json' => json_encode([
                'metadata' => [
                    'source' => 'whatsapp',
                    'type' => 'text'
                ]
            ]),
            'is_button_click' => $this->faker->boolean(30),
            'button_click_id' => null,
            'is_raw_message' => $this->faker->boolean(70),
            'raw_data' => json_encode([
                'input_type' => 'text',
                'timestamp' => now()->toISOString(),
                'user_input' => $this->faker->sentence()
            ]),
        ];
    }

    /**
     * Indicate that the interaction is a button click.
     */
    public function buttonClick(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_button_click' => true,
            'button_click_id' => Button::factory(),
            'is_raw_message' => false,
            'message' => 'Button clicked',
            'raw_data' => json_encode([
                'input_type' => 'button_click',
                'button_id' => $this->faker->numberBetween(1, 100),
                'timestamp' => now()->toISOString()
            ]),
        ]);
    }

    /**
     * Indicate that the interaction is a raw text message.
     */
    public function rawMessage(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_button_click' => false,
            'button_click_id' => null,
            'is_raw_message' => true,
            'raw_data' => json_encode([
                'input_type' => 'text',
                'message' => $this->faker->sentence(),
                'timestamp' => now()->toISOString()
            ]),
        ]);
    }

    /**
     * Indicate that the interaction has no button click.
     */
    public function withoutButtonClick(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_button_click' => false,
            'button_click_id' => null,
        ]);
    }

    /**
     * Indicate that the interaction has specific raw data.
     */
    public function withRawData(array $data): static
    {
        return $this->state(fn (array $attributes) => [
            'raw_data' => json_encode($data),
        ]);
    }
}
