<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\PhoneNumber>
 */
class PhoneNumberFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'organization_id' => \App\Models\Organization::factory(),
            'user_id' => null,
            'client_id' => null,
            'flow_id' => null,
            'phone_number' => fake()->phoneNumber(),
            'name' => fake()->words(2, true),
            'description' => fake()->sentence(),
            'is_active' => true,
            'is_chatbot_activated' => true,
            'whatsapp_phone_number_id' => fake()->uuid(),
            'whatsapp_business_id' => fake()->uuid(),
            'whatsapp_access_token' => fake()->uuid(),
        ];
    }

    /**
     * Create a phone number with a specific format.
     */
    public function withFormat(string $format): static
    {
        return $this->state(fn (array $attributes) => [
            'phone_number' => fake()->numerify($format),
        ]);
    }

    /**
     * Create a WhatsApp Business phone number.
     */
    public function whatsappBusiness(): static
    {
        return $this->state(fn (array $attributes) => [
            'name' => 'WhatsApp Business - ' . fake()->company(),
            'phone_number' => '+' . fake()->numerify('############'),
        ]);
    }
}
