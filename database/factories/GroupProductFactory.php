<?php

namespace Database\Factories;

use App\Models\Group;
use App\Models\GroupProduct;
use App\Models\Product;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\GroupProduct>
 */
class GroupProductFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = GroupProduct::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'group_id' => Group::factory(),
            'product_id' => Product::factory(),
        ];
    }

    /**
     * Indicate that the assignment is for electronics group.
     */
    public function electronicsGroup(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'group_id' => Group::factory()->electronics(),
            ];
        });
    }

    /**
     * Indicate that the assignment is for clothing group.
     */
    public function clothingGroup(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'group_id' => Group::factory()->clothing(),
            ];
        });
    }

    /**
     * Indicate that the assignment is for books group.
     */
    public function booksGroup(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'group_id' => Group::factory()->books(),
            ];
        });
    }

    /**
     * Indicate that the assignment is for office supplies group.
     */
    public function officeSuppliesGroup(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'group_id' => Group::factory()->officeSupplies(),
            ];
        });
    }
}
