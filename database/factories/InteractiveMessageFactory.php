<?php

namespace Database\Factories;

use App\Models\InteractiveMessage;
use App\Models\Organization;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\InteractiveMessage>
 */
class InteractiveMessageFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = InteractiveMessage::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'organization_id' => Organization::factory(),
            'header' => $this->faker->optional()->sentence(3),
            'body' => $this->faker->sentence(),
            'footer' => $this->faker->optional()->sentence(2),
            'type' => $this->faker->randomElement(['button', 'list']),
            'button_text' => $this->faker->optional()->words(2, true),
        ];
    }

    /**
     * Indicate that the interactive message is a button type.
     */
    public function button(): static
    {
        return $this->state(fn (array $attributes) => [
            'type' => 'button',
            'button_text' => null,
        ]);
    }

    /**
     * Indicate that the interactive message is a list type.
     */
    public function list(): static
    {
        return $this->state(fn (array $attributes) => [
            'type' => 'list',
            'button_text' => $this->faker->words(2, true),
        ]);
    }
}
