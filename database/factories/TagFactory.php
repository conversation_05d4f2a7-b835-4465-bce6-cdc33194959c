<?php

namespace Database\Factories;

use App\Models\Tag;
use App\Models\Organization;
use Illuminate\Database\Eloquent\Factories\Factory;
use Carbon\Carbon;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Tag>
 */
class TagFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Tag::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'organization_id' => Organization::factory(),
            'name' => $this->faker->word(),
            'usage_count' => $this->faker->numberBetween(0, 100),
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now(),
        ];
    }

    /**
     * Indicate that the tag is popular (high usage).
     */
    public function popular(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'usage_count' => $this->faker->numberBetween(50, 200),
                'name' => $this->faker->randomElement([
                    'urgent',
                    'important',
                    'follow-up',
                    'vip',
                    'priority'
                ]),
            ];
        });
    }

    /**
     * Indicate that the tag is rarely used.
     */
    public function rare(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'usage_count' => $this->faker->numberBetween(0, 5),
            ];
        });
    }

    /**
     * Indicate that the tag is for marketing campaigns.
     */
    public function marketing(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'name' => $this->faker->randomElement([
                    'marketing',
                    'promotion',
                    'sale',
                    'discount',
                    'newsletter'
                ]),
            ];
        });
    }
}
