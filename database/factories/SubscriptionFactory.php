<?php

namespace Database\Factories;

use App\Models\Organization;
use App\Models\Subscription;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Subscription>
 */
class SubscriptionFactory extends Factory
{
    protected $model = Subscription::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $isCourtesy = $this->faker->boolean(30); // 30% chance of being courtesy
        $isTrial = !$isCourtesy && $this->faker->boolean(20); // 20% chance of being trial if not courtesy

        $startedAt = $this->faker->dateTimeBetween('-6 months', 'now');

        return [
            'organization_id' => Organization::factory(),
            'status' => $this->faker->randomElement(['ACTIVE', 'INACTIVE', 'SUSPENDED', 'CANCELLED', 'EXPIRED']),
            'billing_type' => $this->faker->randomElement(['BOLETO', 'CREDIT_CARD', 'PIX', 'UNDEFINED']),
            'cycle' => $this->faker->randomElement(['MONTHLY', 'QUARTERLY', 'SEMIANNUAL', 'YEARLY']),
            'value' => $isCourtesy || $isTrial ? 0.00 : $this->faker->randomFloat(2, 29.90, 299.90),
            'started_at' => $startedAt,
            'expires_at' => $this->faker->optional(0.8)->dateTimeBetween($startedAt, '+1 year'),
            'next_due_date' => $this->faker->optional(0.7)->dateTimeBetween('now', '+1 month'),
            'end_date' => $this->faker->optional(0.3)->dateTimeBetween('+6 months', '+2 years'),
            'is_courtesy' => $isCourtesy,
            'courtesy_expires_at' => $isCourtesy ? $this->faker->dateTimeBetween('now', '+3 months') : null,
            'courtesy_reason' => $isCourtesy ? $this->faker->randomElement([
                'Cliente especial',
                'Parceria comercial',
                'Teste de produto',
                'Compensação por problemas',
                'Promoção especial'
            ]) : null,
            'is_trial' => $isTrial,
            'trial_expires_at' => $isTrial ? $this->faker->dateTimeBetween('now', '+1 month') : null,
            'trial_days' => $isTrial ? $this->faker->randomElement([7, 15, 30]) : null,
            'description' => $this->faker->optional(0.7)->sentence(),
            'max_payments' => $this->faker->optional(0.3)->numberBetween(1, 24),
            'external_reference' => $this->faker->optional(0.5)->numerify('SUB######'),
            'discount_value' => $this->faker->optional(0.2)->randomFloat(2, 5.00, 50.00),
            'discount_type' => $this->faker->optional(0.2)->randomElement(['FIXED', 'PERCENTAGE']),
            'discount_due_date_limit_days' => $this->faker->optional(0.2)->numberBetween(0, 10),
            'fine_value' => $this->faker->optional(0.3)->randomFloat(2, 1.00, 10.00),
            'interest_value' => $this->faker->optional(0.3)->randomFloat(2, 0.5, 5.00),
            'deleted' => false,
        ];
    }

    /**
     * Indicate that the subscription is active.
     */
    public function active(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'ACTIVE',
            'expires_at' => $this->faker->dateTimeBetween('+1 week', '+1 year'),
        ]);
    }

    /**
     * Indicate that the subscription is inactive.
     */
    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'INACTIVE',
        ]);
    }

    /**
     * Indicate that the subscription is suspended.
     */
    public function suspended(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'SUSPENDED',
        ]);
    }

    /**
     * Indicate that the subscription is cancelled.
     */
    public function cancelled(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'CANCELLED',
        ]);
    }

    /**
     * Indicate that the subscription is expired.
     */
    public function expired(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'EXPIRED',
            'expires_at' => $this->faker->dateTimeBetween('-6 months', '-1 day'),
        ]);
    }

    /**
     * Indicate that the subscription is a courtesy.
     */
    public function courtesy(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_courtesy' => true,
            'courtesy_expires_at' => $this->faker->dateTimeBetween('+1 week', '+3 months'),
            'courtesy_reason' => 'Cortesia de teste',
            'value' => 0.00,
            'billing_type' => 'UNDEFINED',
            'is_trial' => false,
            'trial_expires_at' => null,
            'trial_days' => null,
        ]);
    }

    /**
     * Indicate that the subscription is a trial.
     */
    public function trial(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_trial' => true,
            'trial_expires_at' => $this->faker->dateTimeBetween('+1 week', '+1 month'),
            'trial_days' => $this->faker->randomElement([7, 15, 30]),
            'value' => 0.00,
            'billing_type' => 'UNDEFINED',
            'is_courtesy' => false,
            'courtesy_expires_at' => null,
            'courtesy_reason' => null,
        ]);
    }

    /**
     * Indicate that the subscription is paid (not courtesy or trial).
     */
    public function paid(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_courtesy' => false,
            'courtesy_expires_at' => null,
            'courtesy_reason' => null,
            'is_trial' => false,
            'trial_expires_at' => null,
            'trial_days' => null,
            'value' => $this->faker->randomFloat(2, 29.90, 299.90),
            'billing_type' => $this->faker->randomElement(['BOLETO', 'CREDIT_CARD', 'PIX']),
        ]);
    }

    /**
     * Indicate that the subscription is expiring soon.
     */
    public function expiringSoon(): static
    {
        return $this->state(fn (array $attributes) => [
            'expires_at' => $this->faker->dateTimeBetween('+1 day', '+7 days'),
        ]);
    }

    /**
     * Indicate that the subscription is monthly.
     */
    public function monthly(): static
    {
        return $this->state(fn (array $attributes) => [
            'cycle' => 'MONTHLY',
            'next_due_date' => $this->faker->dateTimeBetween('now', '+1 month'),
        ]);
    }

    /**
     * Indicate that the subscription is yearly.
     */
    public function yearly(): static
    {
        return $this->state(fn (array $attributes) => [
            'cycle' => 'YEARLY',
            'next_due_date' => $this->faker->dateTimeBetween('now', '+1 year'),
        ]);
    }

    /**
     * Indicate that the subscription uses boleto.
     */
    public function boleto(): static
    {
        return $this->state(fn (array $attributes) => [
            'billing_type' => 'BOLETO',
        ]);
    }

    /**
     * Indicate that the subscription uses credit card.
     */
    public function creditCard(): static
    {
        return $this->state(fn (array $attributes) => [
            'billing_type' => 'CREDIT_CARD',
        ]);
    }

    /**
     * Indicate that the subscription uses PIX.
     */
    public function pix(): static
    {
        return $this->state(fn (array $attributes) => [
            'billing_type' => 'PIX',
        ]);
    }

    /**
     * Indicate that the subscription has a discount.
     */
    public function withDiscount(): static
    {
        return $this->state(fn (array $attributes) => [
            'discount_value' => $this->faker->randomFloat(2, 5.00, 50.00),
            'discount_type' => $this->faker->randomElement(['FIXED', 'PERCENTAGE']),
            'discount_due_date_limit_days' => $this->faker->numberBetween(0, 10),
        ]);
    }

    /**
     * Indicate that the subscription has fine and interest.
     */
    public function withFineAndInterest(): static
    {
        return $this->state(fn (array $attributes) => [
            'fine_value' => $this->faker->randomFloat(2, 1.00, 10.00),
            'interest_value' => $this->faker->randomFloat(2, 0.5, 5.00),
        ]);
    }

    /**
     * Create subscription for specific organization.
     */
    public function forOrganization(Organization $organization): static
    {
        return $this->state(fn (array $attributes) => [
            'organization_id' => $organization->id,
            'external_reference' => "sub_org_{$organization->id}",
            'description' => "Assinatura para {$organization->name}",
        ]);
    }

    /**
     * Create subscription with high value.
     */
    public function premium(): static
    {
        return $this->state(fn (array $attributes) => [
            'value' => $this->faker->randomFloat(2, 199.90, 999.90),
            'cycle' => 'YEARLY',
            'billing_type' => 'CREDIT_CARD',
            'description' => 'Plano Premium',
        ]);
    }

    /**
     * Create subscription with basic value.
     */
    public function basic(): static
    {
        return $this->state(fn (array $attributes) => [
            'value' => $this->faker->randomFloat(2, 29.90, 99.90),
            'cycle' => 'MONTHLY',
            'billing_type' => 'BOLETO',
            'description' => 'Plano Básico',
        ]);
    }
}
