<?php

namespace Database\Factories;

use App\Models\WhatsAppSyncLog;
use App\Enums\SyncType;
use App\Enums\SyncStatus;
use Illuminate\Database\Eloquent\Factories\Factory;
use Carbon\Carbon;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\WhatsAppSyncLog>
 */
class WhatsAppSyncLogFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = WhatsAppSyncLog::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'sync_type' => $this->faker->randomElement(SyncType::cases()),
            'entity_id' => $this->faker->numberBetween(1, 1000),
            'status' => $this->faker->randomElement(SyncStatus::cases()),
            'response_data_json' => [
                'status' => 'success',
                'timestamp' => Carbon::now()->toISOString(),
                'data' => [
                    'processed' => $this->faker->numberBetween(1, 100),
                    'total' => $this->faker->numberBetween(1, 100),
                ],
            ],
            'error_message' => null,
            'messages_synced' => $this->faker->numberBetween(0, 100),
            'messages_updated' => $this->faker->numberBetween(0, 50),
            'synced_at' => Carbon::now(),
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now(),
        ];
    }

    /**
     * Indicate that the sync was successful.
     */
    public function successful(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'status' => SyncStatus::SUCCESS,
                'error_message' => null,
                'response_data_json' => [
                    'status' => 'success',
                    'timestamp' => Carbon::now()->toISOString(),
                    'messages_processed' => $this->faker->numberBetween(10, 100),
                    'sync_duration' => $this->faker->randomFloat(2, 1, 30),
                ],
                'messages_synced' => $this->faker->numberBetween(10, 100),
                'messages_updated' => $this->faker->numberBetween(5, 50),
            ];
        });
    }

    /**
     * Indicate that the sync failed.
     */
    public function failed(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'status' => SyncStatus::FAILED,
                'error_message' => $this->faker->randomElement([
                    'WhatsApp API rate limit exceeded',
                    'Invalid authentication token',
                    'Network connection timeout',
                    'Invalid webhook payload',
                    'Database connection error'
                ]),
                'response_data_json' => [
                    'status' => 'error',
                    'timestamp' => Carbon::now()->toISOString(),
                    'error_code' => $this->faker->numberBetween(400, 500),
                ],
                'messages_synced' => 0,
                'messages_updated' => 0,
            ];
        });
    }

    /**
     * Indicate that the sync is for messages.
     */
    public function forMessages(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'sync_type' => SyncType::MESSAGE,
                'messages_synced' => $this->faker->numberBetween(10, 200),
                'messages_updated' => $this->faker->numberBetween(5, 100),
            ];
        });
    }

    /**
     * Indicate that the sync is for campaigns.
     */
    public function forCampaigns(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'sync_type' => SyncType::CAMPAIGN,
                'messages_synced' => $this->faker->numberBetween(50, 500),
                'messages_updated' => $this->faker->numberBetween(25, 250),
                'response_data_json' => [
                    'status' => 'success',
                    'timestamp' => Carbon::now()->toISOString(),
                    'campaign_processed' => true,
                ],
            ];
        });
    }
}
