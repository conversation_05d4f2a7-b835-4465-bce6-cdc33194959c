<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Component>
 */
class ComponentFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'organization_id' => \App\Models\Organization::factory(),
            'step_id' => null,
            'template_id' => null,
            'name' => fake()->words(2, true),
            'type' => fake()->randomElement(['HEADER', 'BODY', 'FOOTER', 'BUTTONS']),
            'text' => fake()->sentence(),
            'format' => fake()->randomElement(['TEXT', 'IMAGE', 'VIDEO', 'DOCUMENT']),
            'json' => '{}',
        ];
    }

    /**
     * Create a header component.
     */
    public function header(): static
    {
        return $this->state(fn (array $attributes) => [
            'type' => 'HEADER',
            'text' => fake()->words(3, true),
        ]);
    }

    /**
     * Create a body component.
     */
    public function body(): static
    {
        return $this->state(fn (array $attributes) => [
            'type' => 'BODY',
            'text' => fake()->paragraph(),
        ]);
    }

    /**
     * Create a footer component.
     */
    public function footer(): static
    {
        return $this->state(fn (array $attributes) => [
            'type' => 'FOOTER',
            'text' => fake()->sentence(),
        ]);
    }

    /**
     * Create a buttons component.
     */
    public function buttons(): static
    {
        return $this->state(fn (array $attributes) => [
            'type' => 'BUTTONS',
            'text' => 'Button Component',
        ]);
    }
}
