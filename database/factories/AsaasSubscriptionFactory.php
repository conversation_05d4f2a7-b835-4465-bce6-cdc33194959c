<?php

namespace Database\Factories;

use App\Models\Subscription;
use App\Services\ASAAS\Models\AsaasSubscription;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Services\ASAAS\Models\AsaasSubscription>
 */
class AsaasSubscriptionFactory extends Factory
{
    protected $model = AsaasSubscription::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $status = $this->faker->randomElement(['ACTIVE', 'INACTIVE', 'EXPIRED', 'CANCELLED']);
        $syncStatus = $this->faker->randomElement(['pending', 'synced', 'error']);

        return [
            'subscription_id' => Subscription::factory(),
            'asaas_customer_id' => 'cus_' . $this->faker->regexify('[A-Za-z0-9]{12}'),
            'asaas_subscription_id' => 'sub_' . $this->faker->regexify('[A-Za-z0-9]{12}'),
            'asaas_date_created' => $this->faker->dateTimeBetween('-1 year', 'now'),
            'asaas_synced_at' => $syncStatus === 'synced' ? $this->faker->dateTimeBetween('-1 week', 'now') : null,
            'asaas_sync_errors' => $syncStatus === 'error' ? ['error' => 'Sample error message'] : null,
            'sync_status' => $syncStatus,
            'billing_type' => $this->faker->randomElement(['BOLETO', 'CREDIT_CARD', 'PIX', 'UNDEFINED']),
            'cycle' => $this->faker->randomElement(['MONTHLY', 'QUARTERLY', 'SEMIANNUAL', 'YEARLY']),
            'value' => $this->faker->randomFloat(2, 29.90, 299.90),
            'next_due_date' => $this->faker->optional(0.8)->dateTimeBetween('now', '+3 months'),
            'end_date' => $this->faker->optional(0.3)->dateTimeBetween('+6 months', '+2 years'),
            'description' => $this->faker->optional(0.7)->sentence(),
            'status' => $status,
            'max_payments' => $this->faker->optional(0.3)->numberBetween(1, 24),
            'external_reference' => $this->faker->optional(0.5)->numerify('EXT######'),
            'payment_link' => $this->faker->optional(0.4)->url(),
            'checkout_session' => $this->faker->optional(0.3)->uuid(),
            'discount_value' => $this->faker->optional(0.2)->randomFloat(2, 5.00, 50.00),
            'discount_type' => $this->faker->optional(0.2)->randomElement(['FIXED', 'PERCENTAGE']),
            'discount_due_date_limit_days' => $this->faker->optional(0.2)->numberBetween(0, 10),
            'fine_value' => $this->faker->optional(0.3)->randomFloat(2, 1.00, 10.00),
            'fine_type' => $this->faker->optional(0.3)->randomElement(['FIXED', 'PERCENTAGE']),
            'interest_value' => $this->faker->optional(0.3)->randomFloat(2, 0.5, 5.00),
            'credit_card_number' => $this->faker->optional(0.3)->numerify('####'),
            'credit_card_brand' => $this->faker->optional(0.3)->randomElement(['VISA', 'MASTERCARD', 'ELO', 'AMEX']),
            'credit_card_token' => $this->faker->optional(0.3)->uuid(),
            'split_data' => $this->faker->optional(0.1)->randomElement([
                [['walletId' => $this->faker->uuid(), 'fixedValue' => 10.00]],
                [['walletId' => $this->faker->uuid(), 'percentualValue' => 15.0]]
            ]),
            'deleted' => false,
        ];
    }

    /**
     * Indicate that the subscription is active.
     */
    public function active(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'ACTIVE',
            'sync_status' => 'synced',
            'asaas_synced_at' => $this->faker->dateTimeBetween('-1 week', 'now'),
        ]);
    }

    /**
     * Indicate that the subscription is inactive.
     */
    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'INACTIVE',
        ]);
    }

    /**
     * Indicate that the subscription is expired.
     */
    public function expired(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'EXPIRED',
            'sync_status' => 'synced', // Explicitly set to avoid random errors
            'end_date' => $this->faker->dateTimeBetween('-6 months', '-1 day'),
        ]);
    }

    /**
     * Indicate that the subscription is cancelled.
     */
    public function cancelled(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'CANCELLED',
            'sync_status' => 'synced', // Explicitly set to avoid random errors
            'end_date' => $this->faker->dateTimeBetween('-3 months', 'now'),
        ]);
    }

    /**
     * Indicate that the subscription is synced.
     */
    public function synced(): static
    {
        return $this->state(fn (array $attributes) => [
            'sync_status' => 'synced',
            'asaas_synced_at' => $this->faker->dateTimeBetween('-30 minutes', 'now'), // Ensure it's recent (within 1 hour)
            'asaas_sync_errors' => null,
        ]);
    }

    /**
     * Indicate that the subscription is pending sync.
     */
    public function pending(): static
    {
        return $this->state(fn (array $attributes) => [
            'sync_status' => 'pending',
            'asaas_synced_at' => null,
            'asaas_sync_errors' => null,
        ]);
    }

    /**
     * Indicate that the subscription has sync errors.
     */
    public function withErrors(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'INACTIVE', // Set to INACTIVE since it has errors
            'sync_status' => 'error',
            'asaas_sync_errors' => [
                'error' => 'API Error',
                'message' => 'Failed to sync with ASAAS',
                'code' => 500,
                'timestamp' => now()->toISOString(),
            ],
        ]);
    }

    /**
     * Indicate that the subscription needs sync.
     */
    public function needsSync(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'ACTIVE', // Explicitly set status to avoid random values
            'asaas_synced_at' => $this->faker->dateTimeBetween('-3 hours', '-2 hours'), // Ensure it's older than 1 hour
            'sync_status' => 'pending', // Always use 'pending' for consistent results
        ]);
    }

    /**
     * Indicate that the subscription uses boleto.
     */
    public function boleto(): static
    {
        return $this->state(fn (array $attributes) => [
            'billing_type' => 'BOLETO',
            'credit_card_number' => null,
            'credit_card_brand' => null,
            'credit_card_token' => null,
        ]);
    }

    /**
     * Indicate that the subscription uses credit card.
     */
    public function creditCard(): static
    {
        return $this->state(fn (array $attributes) => [
            'billing_type' => 'CREDIT_CARD',
            'credit_card_number' => $this->faker->numerify('####'),
            'credit_card_brand' => $this->faker->randomElement(['VISA', 'MASTERCARD']),
            'credit_card_token' => $this->faker->uuid(),
        ]);
    }

    /**
     * Indicate that the subscription uses PIX.
     */
    public function pix(): static
    {
        return $this->state(fn (array $attributes) => [
            'billing_type' => 'PIX',
            'credit_card_number' => null,
            'credit_card_brand' => null,
            'credit_card_token' => null,
        ]);
    }

    /**
     * Indicate that the subscription is monthly.
     */
    public function monthly(): static
    {
        return $this->state(fn (array $attributes) => [
            'cycle' => 'MONTHLY',
            'next_due_date' => $this->faker->dateTimeBetween('now', '+1 month'),
        ]);
    }

    /**
     * Indicate that the subscription is yearly.
     */
    public function yearly(): static
    {
        return $this->state(fn (array $attributes) => [
            'cycle' => 'YEARLY',
            'next_due_date' => $this->faker->dateTimeBetween('now', '+1 year'),
        ]);
    }

    /**
     * Indicate that the subscription has a discount.
     */
    public function withDiscount(): static
    {
        return $this->state(fn (array $attributes) => [
            'discount_value' => $this->faker->randomFloat(2, 5.00, 50.00),
            'discount_type' => $this->faker->randomElement(['FIXED', 'PERCENTAGE']),
            'discount_due_date_limit_days' => $this->faker->numberBetween(0, 10),
        ]);
    }

    /**
     * Indicate that the subscription has fine and interest.
     */
    public function withFineAndInterest(): static
    {
        return $this->state(fn (array $attributes) => [
            'fine_value' => $this->faker->randomFloat(2, 1.00, 10.00),
            'fine_type' => $this->faker->randomElement(['FIXED', 'PERCENTAGE']),
            'interest_value' => $this->faker->randomFloat(2, 0.5, 5.00),
        ]);
    }

    /**
     * Indicate that the subscription has split configuration.
     */
    public function withSplit(): static
    {
        return $this->state(fn (array $attributes) => [
            'split_data' => [
                [
                    'walletId' => $this->faker->uuid(),
                    'fixedValue' => $this->faker->randomFloat(2, 5.00, 20.00),
                    'percentualValue' => null,
                    'externalReference' => null,
                    'description' => 'Split payment',
                    'status' => 'ACTIVE',
                ]
            ],
        ]);
    }

    /**
     * Create subscription for specific subscription.
     */
    public function forSubscription(Subscription $subscription): static
    {
        return $this->state(fn (array $attributes) => [
            'subscription_id' => $subscription->id,
            'external_reference' => "asaas_sub_{$subscription->id}",
            'description' => "ASAAS subscription for subscription {$subscription->id}",
        ]);
    }

    /**
     * Create subscription with specific ASAAS customer ID.
     */
    public function forAsaasCustomer(string $asaasCustomerId): static
    {
        return $this->state(fn (array $attributes) => [
            'asaas_customer_id' => $asaasCustomerId,
        ]);
    }

    /**
     * Create subscription with high value.
     */
    public function premium(): static
    {
        return $this->state(fn (array $attributes) => [
            'value' => $this->faker->randomFloat(2, 199.90, 999.90),
            'cycle' => 'YEARLY',
            'billing_type' => 'CREDIT_CARD',
            'description' => 'Premium ASAAS Subscription',
        ]);
    }

    /**
     * Create subscription with basic value.
     */
    public function basic(): static
    {
        return $this->state(fn (array $attributes) => [
            'value' => $this->faker->randomFloat(2, 29.90, 99.90),
            'cycle' => 'MONTHLY',
            'billing_type' => 'BOLETO',
            'description' => 'Basic ASAAS Subscription',
        ]);
    }
}
