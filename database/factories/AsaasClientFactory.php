<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use App\Services\ASAAS\Models\AsaasClient;
use App\Models\Client;
use App\Models\Organization;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Services\ASAAS\Models\AsaasClient>
 */
class AsaasClientFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = AsaasClient::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $isFisica = $this->faker->boolean(70);
        $cpfCnpj = $isFisica ? $this->faker->numerify('###########') : $this->faker->numerify('##############');

        return [
            'client_id' => Client::factory(),
            'organization_id' => Organization::factory(),
            'asaas_customer_id' => 'cus_' . $this->faker->uuid(),
            'asaas_synced_at' => $this->faker->optional(0.8)->dateTimeBetween('-7 days', 'now'),
            'asaas_sync_errors' => null,
            'sync_status' => $this->faker->randomElement(['pending', 'synced', 'error']),
            'name' => $this->faker->name(),
            'email' => $this->faker->unique()->safeEmail(),
            'phone' => $this->faker->optional()->phoneNumber(),
            'mobile_phone' => $this->faker->optional()->phoneNumber(),
            'address' => $this->faker->optional()->streetAddress(),
            'address_number' => $this->faker->optional()->buildingNumber(),
            'complement' => $this->faker->optional()->secondaryAddress(),
            'province' => $this->faker->optional()->citySuffix(),
            'city_name' => $this->faker->optional()->city(),
            'state' => $this->faker->optional()->stateAbbr(),
            'country' => 'Brasil',
            'postal_code' => $this->faker->optional()->postcode(),
            'cpf_cnpj' => $cpfCnpj,
            'person_type' => $isFisica ? 'FISICA' : 'JURIDICA',
            'external_reference' => $this->faker->optional()->uuid(),
            'notification_disabled' => $this->faker->boolean(20),
            'additional_emails' => $this->faker->optional()->email(),
            'observations' => $this->faker->optional()->sentence(),
            'foreign_customer' => $this->faker->boolean(10),
            'deleted' => false,
        ];
    }

    /**
     * Indicate that the client has sync errors
     */
    public function withSyncErrors(): static
    {
        return $this->state(fn (array $attributes) => [
            'asaas_sync_errors' => [
                'error' => $this->faker->randomElement([
                    'Invalid email format',
                    'Missing required field: cpfCnpj',
                    'Customer already exists',
                    'Invalid document format',
                    'API rate limit exceeded'
                ]),
                'timestamp' => now()->toISOString(),
                'attempt_count' => $this->faker->numberBetween(1, 5),
                'last_response' => [
                    'status' => $this->faker->randomElement([400, 422, 429, 500]),
                    'message' => $this->faker->sentence(),
                ]
            ],
            'asaas_synced_at' => null,
        ]);
    }

    /**
     * Indicate that the client needs sync (not synced recently)
     */
    public function needsSync(): static
    {
        return $this->state(fn (array $attributes) => [
            'asaas_synced_at' => $this->faker->dateTimeBetween('-30 days', '-25 hours'),
        ]);
    }

    /**
     * Indicate that the client was recently synced
     */
    public function recentlySynced(): static
    {
        return $this->state(fn (array $attributes) => [
            'asaas_synced_at' => $this->faker->dateTimeBetween('-1 hour', 'now'),
            'asaas_sync_errors' => null,
        ]);
    }

    /**
     * Indicate that the client has never been synced
     */
    public function neverSynced(): static
    {
        return $this->state(fn (array $attributes) => [
            'asaas_customer_id' => null,
            'asaas_synced_at' => null,
            'asaas_sync_errors' => null,
        ]);
    }

    /**
     * Indicate that the client sync failed multiple times
     */
    public function multipleFailures(): static
    {
        return $this->state(fn (array $attributes) => [
            'asaas_sync_errors' => [
                'error' => 'Multiple sync failures',
                'timestamp' => now()->toISOString(),
                'attempt_count' => $this->faker->numberBetween(3, 10),
                'last_response' => [
                    'status' => 422,
                    'message' => 'Validation failed',
                ],
                'history' => [
                    [
                        'attempt' => 1,
                        'timestamp' => now()->subHours(3)->toISOString(),
                        'error' => 'Invalid email format',
                    ],
                    [
                        'attempt' => 2,
                        'timestamp' => now()->subHours(2)->toISOString(),
                        'error' => 'Missing required field',
                    ],
                    [
                        'attempt' => 3,
                        'timestamp' => now()->subHour()->toISOString(),
                        'error' => 'API rate limit exceeded',
                    ],
                ]
            ],
            'asaas_synced_at' => null,
        ]);
    }

    /**
     * Indicate that the client belongs to a specific organization
     */
    public function forOrganization(Organization $organization): static
    {
        return $this->state(fn (array $attributes) => [
            'organization_id' => $organization->id,
        ]);
    }

    /**
     * Indicate that the client is linked to a specific client
     */
    public function forClient(Client $client): static
    {
        return $this->state(fn (array $attributes) => [
            'client_id' => $client->id,
            'organization_id' => $client->organization_id,
        ]);
    }

    /**
     * Create with specific ASAAS customer ID
     */
    public function withCustomerId(string $customerId): static
    {
        return $this->state(fn (array $attributes) => [
            'asaas_customer_id' => $customerId,
        ]);
    }

    /**
     * Indicate that the client sync is pending
     */
    public function syncPending(): static
    {
        return $this->state(fn (array $attributes) => [
            'asaas_customer_id' => null,
            'asaas_synced_at' => null,
            'asaas_sync_errors' => [
                'status' => 'pending',
                'message' => 'Sync scheduled but not yet executed',
                'timestamp' => now()->toISOString(),
                'attempt_count' => 0,
            ],
        ]);
    }

    /**
     * Indicate that the client has temporary sync issues
     */
    public function temporaryIssues(): static
    {
        return $this->state(fn (array $attributes) => [
            'asaas_sync_errors' => [
                'error' => 'Temporary API unavailability',
                'timestamp' => now()->subMinutes(30)->toISOString(),
                'attempt_count' => 1,
                'retry_after' => now()->addMinutes(30)->toISOString(),
                'last_response' => [
                    'status' => 503,
                    'message' => 'Service temporarily unavailable',
                ]
            ],
        ]);
    }

    /**
     * Indicate that the client sync was successful after errors
     */
    public function recoveredFromErrors(): static
    {
        return $this->state(fn (array $attributes) => [
            'asaas_synced_at' => now(),
            'asaas_sync_errors' => null,
        ]);
    }
}
