<?php

namespace Database\Factories;

use App\Models\Budget;
use App\Models\Client;
use App\Models\Organization;
use App\Models\Project;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Project>
 */
class ProjectFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Project::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'organization_id' => Organization::factory(),
            'client_id' => Client::factory(),
            'budget_id' => Budget::factory(),
            'name' => fake()->words(3, true),
            'description' => fake()->paragraph(),
            'value' => fake()->randomFloat(2, 1000, 50000),
            'cost' => fake()->randomFloat(2, 500, 25000),
        ];
    }

    /**
     * Indicate that the project has high value.
     */
    public function highValue(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'value' => fake()->randomFloat(2, 50000, 500000),
                'cost' => fake()->randomFloat(2, 25000, 250000),
                'name' => 'High Value ' . fake()->words(2, true),
            ];
        });
    }

    /**
     * Indicate that the project has low value.
     */
    public function lowValue(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'value' => fake()->randomFloat(2, 100, 5000),
                'cost' => fake()->randomFloat(2, 50, 2500),
                'name' => 'Low Value ' . fake()->words(2, true),
            ];
        });
    }

    /**
     * Indicate that the project is completed.
     */
    public function completed(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'name' => 'Completed ' . fake()->words(2, true),
                'description' => 'This project has been completed successfully.',
            ];
        });
    }

    /**
     * Indicate that the project is in progress.
     */
    public function inProgress(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'name' => 'In Progress ' . fake()->words(2, true),
                'description' => 'This project is currently in progress.',
            ];
        });
    }

    /**
     * Indicate that the project is for a specific client.
     */
    public function forClient($clientId = null): static
    {
        return $this->state(function (array $attributes) use ($clientId) {
            return [
                'client_id' => $clientId ?? Client::factory(),
            ];
        });
    }
}
