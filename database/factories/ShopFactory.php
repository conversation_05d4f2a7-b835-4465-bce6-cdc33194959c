<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Shop>
 */
class ShopFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'organization_id' => \App\Models\Organization::factory(),
            'name' => fake()->company() . ' Store',
            'description' => fake()->sentence(),
            'is_active' => true,
        ];
    }

    /**
     * Create an inactive shop.
     */
    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => false,
        ]);
    }

    /**
     * Create a main store.
     */
    public function mainStore(): static
    {
        return $this->state(fn (array $attributes) => [
            'name' => 'Main Store',
            'description' => 'Primary store location',
            'is_active' => true,
        ]);
    }

    /**
     * Create a warehouse.
     */
    public function warehouse(): static
    {
        return $this->state(fn (array $attributes) => [
            'name' => 'Warehouse ' . fake()->numberBetween(1, 10),
            'description' => 'Storage and distribution center',
            'is_active' => true,
        ]);
    }

    /**
     * Create an online store.
     */
    public function online(): static
    {
        return $this->state(fn (array $attributes) => [
            'name' => 'Online Store',
            'description' => 'E-commerce platform',
            'is_active' => true,
        ]);
    }
}
