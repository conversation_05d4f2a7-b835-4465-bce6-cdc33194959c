<?php

namespace Database\Factories\Services\ASAAS\Models;

use App\Services\ASAAS\Models\AsaasLog;
use App\Models\Organization;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Services\ASAAS\Models\AsaasLog>
 */
class AsaasLogFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = AsaasLog::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $isError = $this->faker->boolean(20); // 20% chance of error
        $method = $this->faker->randomElement(['GET', 'POST', 'PUT', 'DELETE']);
        $endpoint = $this->faker->randomElement([
            '/v3/customers',
            '/v3/payments',
            '/v3/subscriptions',
            '/v3/accounts',
            '/v3/webhooks'
        ]);

        return [
            'organization_id' => Organization::factory(),
            'user_id' => User::factory(),
            'method' => $method,
            'endpoint' => $endpoint,
            'request_data' => $this->generateRequestData($method),
            'response_data' => $isError ? $this->generateErrorResponse() : $this->generateSuccessResponse(),
            'response_status' => $isError ? $this->faker->randomElement([400, 401, 403, 404, 500]) : 200,
            'execution_time' => $this->faker->randomFloat(3, 0.1, 10.0),
            'is_error' => $isError,
            'error_message' => $isError ? $this->faker->sentence() : null,
            'asaas_error_code' => $isError ? $this->faker->randomElement(['invalid_action', 'invalid_value', 'required_field']) : null,
            'environment' => $this->faker->randomElement(['sandbox', 'production']),
        ];
    }

    /**
     * Generate sample request data based on method
     */
    private function generateRequestData(string $method): array
    {
        if ($method === 'GET') {
            return [];
        }

        return [
            'name' => $this->faker->name(),
            'email' => $this->faker->email(),
            'cpfCnpj' => $this->faker->numerify('###.###.###-##'),
            'value' => $this->faker->randomFloat(2, 10, 1000),
        ];
    }

    /**
     * Generate sample success response
     */
    private function generateSuccessResponse(): array
    {
        return [
            'object' => 'customer',
            'id' => 'cus_' . $this->faker->regexify('[A-Za-z0-9]{12}'),
            'name' => $this->faker->name(),
            'email' => $this->faker->email(),
            'dateCreated' => $this->faker->dateTime()->format('Y-m-d'),
        ];
    }

    /**
     * Generate sample error response
     */
    private function generateErrorResponse(): array
    {
        return [
            'errors' => [
                [
                    'code' => 'invalid_action',
                    'description' => $this->faker->sentence(),
                ]
            ]
        ];
    }

    /**
     * Indicate that the log is an error
     */
    public function error(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_error' => true,
            'response_status' => $this->faker->randomElement([400, 401, 403, 404, 500]),
            'error_message' => $this->faker->sentence(),
            'asaas_error_code' => $this->faker->randomElement(['invalid_action', 'invalid_value', 'required_field']),
            'response_data' => $this->generateErrorResponse(),
        ]);
    }

    /**
     * Indicate that the log is successful
     */
    public function successful(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_error' => false,
            'response_status' => 200,
            'error_message' => null,
            'asaas_error_code' => null,
            'response_data' => $this->generateSuccessResponse(),
        ]);
    }

    /**
     * Indicate that the log is from sandbox environment
     */
    public function sandbox(): static
    {
        return $this->state(fn (array $attributes) => [
            'environment' => 'sandbox',
        ]);
    }

    /**
     * Indicate that the log is from production environment
     */
    public function production(): static
    {
        return $this->state(fn (array $attributes) => [
            'environment' => 'production',
        ]);
    }

    /**
     * Indicate that the log is a slow request
     */
    public function slow(): static
    {
        return $this->state(fn (array $attributes) => [
            'execution_time' => $this->faker->randomFloat(3, 5.0, 30.0),
        ]);
    }

    /**
     * Indicate that the log is a fast request
     */
    public function fast(): static
    {
        return $this->state(fn (array $attributes) => [
            'execution_time' => $this->faker->randomFloat(3, 0.1, 1.0),
        ]);
    }

    /**
     * Indicate that the log is recent
     */
    public function recent(): static
    {
        return $this->state(fn (array $attributes) => [
            'created_at' => $this->faker->dateTimeBetween('-1 hour', 'now'),
        ]);
    }

    /**
     * Indicate that the log is old
     */
    public function old(): static
    {
        return $this->state(fn (array $attributes) => [
            'created_at' => $this->faker->dateTimeBetween('-30 days', '-2 days'),
        ]);
    }
}
