<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use App\Services\ASAAS\Models\AsaasOrganization;
use App\Models\Organization;
use App\Enums\SubscriptionStatus;
use App\Enums\AsaasEnvironment;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Services\ASAAS\Models\AsaasOrganization>
 */
class AsaasOrganizationFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = AsaasOrganization::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'organization_id' => Organization::factory(),
            'asaas_account_id' => 'acc_' . $this->faker->uuid(),
            'asaas_api_key' => '$aact_YTU5YTE0M2M2N2I4MTliNzk0YTI5N2U5MzQzODNhNGM6OjAwMDAwMDAwMDAwMDAwNzI5NzY6OiRhYWNoXzRlNTgzYTQ4LWFhYjctNGVlYS04ZmNjLTZmNTcxZmY4NjNkYQ==',
            'asaas_wallet_id' => 'wal_' . $this->faker->uuid(),
            'asaas_environment' => $this->faker->randomElement([AsaasEnvironment::SANDBOX, AsaasEnvironment::PRODUCTION]),
            'name' => $this->faker->company(),
            'email' => $this->faker->unique()->safeEmail(),
            'phone' => $this->faker->phoneNumber(),
            'mobile_phone' => $this->faker->phoneNumber(),
            'address' => $this->faker->streetAddress(),
            'address_number' => $this->faker->buildingNumber(),
            'complement' => $this->faker->optional()->secondaryAddress(),
            'province' => $this->faker->citySuffix(),
            'postal_code' => $this->faker->postcode(),
            'cpf_cnpj' => $this->faker->numerify('##############'),
            'company_type' => $this->faker->randomElement(['MEI', 'LTDA', 'SA', 'EIRELI']),
            'income_value' => $this->faker->randomFloat(2, 1000, 50000),
            'site' => $this->faker->optional()->url(),
            'is_active' => true,
            'last_sync_at' => $this->faker->optional()->dateTimeBetween('-30 days', 'now'),
        ];
    }

    /**
     * Indicate that the organization is active
     */
    public function active(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => true,
            'last_sync_at' => now(),
        ]);
    }

    /**
     * Indicate that the organization is inactive
     */
    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => false,
            'last_sync_at' => null,
        ]);
    }

    /**
     * Indicate that the organization has sync errors
     */
    public function withSyncErrors(): static
    {
        return $this->state(fn (array $attributes) => [
            'sync_errors' => ['error' => 'Sample sync error'],
            'last_sync_at' => $this->faker->dateTimeBetween('-7 days', 'now'),
            'courtesy_reason' => $this->faker->randomElement([
                'Problema técnico',
                'Negociação comercial',
                'Cliente especial',
                'Período de teste',
                'Migração de sistema'
            ]),
        ]);
    }

    /**
     * Indicate that the organization is in expired courtesy
     */
    public function expiredCourtesy(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_courtesy' => true,
            'courtesy_expires_at' => $this->faker->dateTimeBetween('-30 days', '-1 day'),
            'courtesy_reason' => 'Cortesia expirada',
        ]);
    }

    /**
     * Indicate that the organization is in sandbox environment
     */
    public function sandbox(): static
    {
        return $this->state(fn (array $attributes) => [
            'asaas_environment' => AsaasEnvironment::SANDBOX,
        ]);
    }

    /**
     * Indicate that the organization is in production environment
     */
    public function production(): static
    {
        return $this->state(fn (array $attributes) => [
            'asaas_environment' => AsaasEnvironment::PRODUCTION,
        ]);
    }

    /**
     * Indicate that the organization has no ASAAS integration
     */
    public function withoutIntegration(): static
    {
        return $this->state(fn (array $attributes) => [
            'asaas_account_id' => null,
            'asaas_api_key' => null,
            'asaas_wallet_id' => null,
            'asaas_subscription_id' => null,
            'subscription_status' => SubscriptionStatus::INACTIVE,
            'subscription_value' => null,
            'subscription_due_date' => null,
            'subscription_started_at' => null,
            'subscription_expires_at' => null,
        ]);
    }

    /**
     * Indicate that the organization has basic plan
     */
    public function basicPlan(): static
    {
        return $this->state(fn (array $attributes) => [
            'subscription_value' => 29.90,
            'subscription_status' => SubscriptionStatus::ACTIVE,
        ]);
    }

    /**
     * Indicate that the organization has professional plan
     */
    public function professionalPlan(): static
    {
        return $this->state(fn (array $attributes) => [
            'subscription_value' => 59.90,
            'subscription_status' => SubscriptionStatus::ACTIVE,
        ]);
    }

    /**
     * Indicate that the organization has enterprise plan
     */
    public function enterprisePlan(): static
    {
        return $this->state(fn (array $attributes) => [
            'subscription_value' => 99.90,
            'subscription_status' => SubscriptionStatus::ACTIVE,
        ]);
    }

    /**
     * Indicate that the organization has custom plan
     */
    public function customPlan(float $value): static
    {
        return $this->state(fn (array $attributes) => [
            'subscription_value' => $value,
            'subscription_status' => SubscriptionStatus::ACTIVE,
        ]);
    }
}
