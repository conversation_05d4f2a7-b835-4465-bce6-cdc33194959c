<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use App\Models\Sale;
use App\Models\Organization;
use App\Models\Client;
use App\Models\User;
use App\Models\Shop;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Sale>
 */
class SaleFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Sale::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'organization_id' => Organization::factory(),
            'user_id' => User::factory(),
            'shop_id' => Shop::factory(),
            'client_id' => Client::factory(),
            'total_value' => $this->faker->randomFloat(2, 10, 1000),
            'description' => $this->faker->optional()->sentence(),
            'billing_type' => $this->faker->optional()->randomElement(['BOLETO', 'CREDIT_CARD', 'PIX']),
            'due_date' => $this->faker->optional()->dateTimeBetween('now', '+30 days'),
            'installment_count' => $this->faker->optional()->numberBetween(1, 12),
            'installment_value' => $this->faker->optional()->randomFloat(2, 10, 100),
            'installment_number' => $this->faker->optional()->numberBetween(1, 12),
            'discount_config' => $this->faker->optional()->randomElement([
                ['type' => 'percentage', 'value' => 10],
                ['type' => 'fixed', 'value' => 50],
                null
            ]),
            'fine_config' => $this->faker->optional()->randomElement([
                ['type' => 'percentage', 'value' => 2],
                ['type' => 'fixed', 'value' => 10],
                null
            ]),
            'interest_config' => $this->faker->optional()->randomElement([
                ['type' => 'percentage', 'value' => 1],
                null
            ]),
            'split_config' => $this->faker->optional()->randomElement([
                [
                    ['wallet_id' => 'wallet_123', 'percentage' => 70],
                    ['wallet_id' => 'wallet_456', 'percentage' => 30]
                ],
                null
            ]),
            'credit_card_data' => $this->faker->optional()->randomElement([
                [
                    'holder_name' => $this->faker->name(),
                    'number' => '****************',
                    'expiry_month' => '12',
                    'expiry_year' => '2025',
                    'ccv' => '123'
                ],
                null
            ]),
        ];
    }

    /**
     * Indicate that the sale has an ASAAS payment.
     */
    public function withAsaasPayment(): static
    {
        return $this->state(fn (array $attributes) => [
            'asaas_payment_id' => 'pay_' . $this->faker->uuid(),
            'payment_status' => $this->faker->randomElement(['pending', 'confirmed', 'received', 'overdue']),
            'billing_type' => $this->faker->randomElement(['BOLETO', 'CREDIT_CARD', 'PIX']),
            'due_date' => $this->faker->dateTimeBetween('now', '+30 days'),
            'original_value' => $attributes['total_value'] ?? $this->faker->randomFloat(2, 10, 1000),
            'asaas_synced_at' => now(),
        ]);
    }

    /**
     * Indicate that the sale payment is paid.
     */
    public function paid(): static
    {
        return $this->state(fn (array $attributes) => [
            'payment_status' => 'received',
            'payment_date' => $this->faker->dateTimeBetween('-30 days', 'now'),
            'net_value' => ($attributes['total_value'] ?? $this->faker->randomFloat(2, 10, 1000)) * 0.95,
        ]);
    }

    /**
     * Indicate that the sale payment is overdue.
     */
    public function overdue(): static
    {
        return $this->state(fn (array $attributes) => [
            'payment_status' => 'overdue',
            'due_date' => $this->faker->dateTimeBetween('-30 days', '-1 day'),
        ]);
    }

    /**
     * Indicate that the sale payment is pending.
     */
    public function pending(): static
    {
        return $this->state(fn (array $attributes) => [
            'payment_status' => 'pending',
            'due_date' => $this->faker->dateTimeBetween('now', '+30 days'),
        ]);
    }

    /**
     * Indicate that the sale has installments.
     */
    public function withInstallments(int $count = 3): static
    {
        return $this->state(fn (array $attributes) => [
            'installment_count' => $count,
            'installment_value' => ($attributes['total_value'] ?? $this->faker->randomFloat(2, 10, 1000)) / $count,
            'installment_number' => 1,
        ]);
    }

    /**
     * Indicate that the sale has PIX payment.
     */
    public function withPix(): static
    {
        return $this->state(fn (array $attributes) => [
            'billing_type' => 'PIX',
            'pix_qr_code' => $this->faker->text(200),
        ]);
    }

    /**
     * Indicate that the sale has credit card payment.
     */
    public function withCreditCard(): static
    {
        return $this->state(fn (array $attributes) => [
            'billing_type' => 'CREDIT_CARD',
        ]);
    }

    /**
     * Indicate that the sale has boleto payment.
     */
    public function withBoleto(): static
    {
        return $this->state(fn (array $attributes) => [
            'billing_type' => 'BOLETO',
            'bank_slip_url' => $this->faker->url(),
        ]);
    }

    /**
     * Indicate that the sale has sync errors.
     */
    public function withSyncErrors(): static
    {
        return $this->state(fn (array $attributes) => [
            'asaas_sync_errors' => [
                'error' => 'Test sync error',
                'timestamp' => now()->toISOString(),
            ],
        ]);
    }

    /**
     * Indicate that the sale needs sync.
     */
    public function needsSync(): static
    {
        return $this->state(fn (array $attributes) => [
            'asaas_payment_id' => 'pay_' . $this->faker->uuid(),
            'asaas_synced_at' => now()->subHours(2),
        ]);
    }

    /**
     * Indicate that the sale is synced.
     */
    public function synced(): static
    {
        return $this->state(fn (array $attributes) => [
            'asaas_payment_id' => 'pay_' . $this->faker->uuid(),
            'asaas_synced_at' => now(),
            'asaas_sync_errors' => null,
        ]);
    }
}
