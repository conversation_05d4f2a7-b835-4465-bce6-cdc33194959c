<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('messages', function (Blueprint $table) {
            // ChatBot related fields
            $table->unsignedBigInteger('conversation_id')->nullable()->after('campaign_id');
            $table->unsignedBigInteger('step_id')->nullable()->after('conversation_id');

            // Flags to identify message source
            $table->boolean('is_from_chatbot')->default(false)->after('step_id');
            $table->boolean('is_from_campaign')->default(true)->after('is_from_chatbot');

            // Indexes for better performance
            $table->index('conversation_id');
            $table->index('step_id');
            $table->index('is_from_chatbot');
            $table->index('is_from_campaign');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('messages', function (Blueprint $table) {
            // Drop indexes first
            $table->dropIndex(['conversation_id']);
            $table->dropIndex(['step_id']);
            $table->dropIndex(['is_from_chatbot']);
            $table->dropIndex(['is_from_campaign']);

            // Drop columns
            $table->dropColumn([
                'conversation_id',
                'step_id',
                'is_from_chatbot',
                'is_from_campaign'
            ]);
        });
    }
};
