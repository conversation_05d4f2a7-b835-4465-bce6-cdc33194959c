<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('steps', function (Blueprint $table) {
            // Add new fields
            $table->enum('step_type', ['message', 'interactive', 'input', 'command', 'condition', 'webhook', 'delay'])
                  ->after('type')
                  ->nullable()
                  ->comment('New enum-based step type');

            $table->json('configuration')
                  ->after('step_type')
                  ->nullable()
                  ->comment('Step configuration based on type');

            $table->json('navigation_rules')
                  ->after('configuration')
                  ->nullable()
                  ->comment('Conditional navigation rules');

            $table->integer('timeout_seconds')
                  ->after('navigation_rules')
                  ->nullable()
                  ->comment('Step-specific timeout in seconds');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('steps', function (Blueprint $table) {
            // Remove new fields
            $table->dropColumn([
                'step_type',
                'configuration',
                'navigation_rules',
                'timeout_seconds'
            ]);
        });
    }
};
