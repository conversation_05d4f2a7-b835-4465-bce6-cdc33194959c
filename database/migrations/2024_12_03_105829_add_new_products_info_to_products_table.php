<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('products', function (Blueprint $table) {
            $table->string("barcode")->nullable()->after("name");
            $table->index('barcode');
        });
        Schema::table('batches', function (Blueprint $table) {
            $table->integer("product_id")->nullable()->after("organization_id");
            $table->integer('quantity')->nullable()->after("description");
            $table->index('product_id');
        });
        Schema::table('stock_entries', function (Blueprint $table) {
            $table->integer("batch_id")->nullable()->after("product_id");
            $table->index('batch_id');
        });
        Schema::table('stock_exits', function (Blueprint $table) {
            $table->integer("batch_id")->nullable()->after("product_id");
            $table->index('batch_id');
        });

    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('products', function (Blueprint $table) {
            $table->dropColumn("barcode");
        });
        Schema::table('batches', function (Blueprint $table) {
            $table->dropColumn("product_id");
            $table->dropColumn("quantity");
        });
        Schema::table('stock_entries', function (Blueprint $table) {
            $table->dropColumn("batch_id");
        });
        Schema::table('stock_exits', function (Blueprint $table) {
            $table->dropColumn("batch_id");
        });
    }
};
