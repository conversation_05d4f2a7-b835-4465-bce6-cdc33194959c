<?php

use App\Enums\TelegramBotStatus;
use App\Enums\TelegramBotPublishingStatus;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('telegram_bots', function (Blueprint $table) {
            $table->integer('status')->default(TelegramBotStatus::is_draft->value)->after('is_active');
            $table->integer('publishing_status')->default(TelegramBotPublishingStatus::is_draft->value)->after('is_published');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('telegram_bots', function (Blueprint $table) {
            $table->dropColumn('status');
            $table->dropColumn('publishing_status');
        });
    }
};
