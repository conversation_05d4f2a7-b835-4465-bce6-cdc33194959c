<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('interactions', function (Blueprint $table) {
            $table->boolean('is_button_click')->default(false)->after('result');
            $table->unsignedBigInteger('button_click_id')->nullable()->after('is_button_click');
            $table->boolean('is_raw_message')->default(false)->after('button_click_id');
            $table->text('raw_data')->nullable()->after('is_raw_message');

            $table->index('button_click_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('interactions', function (Blueprint $table) {
            $table->dropForeign(['button_click_id']);
            $table->dropColumn(['is_button_click', 'button_click_id', 'is_raw_message', 'raw_data']);
        });
    }
};
