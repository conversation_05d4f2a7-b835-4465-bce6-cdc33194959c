<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('whatsapp_sync_logs', function (Blueprint $table) {
            $table->id();
            $table->enum('sync_type', ['message', 'campaign']); // Type of sync operation
            $table->unsignedBigInteger('entity_id'); // message_id or campaign_id
            $table->enum('status', ['success', 'failed', 'partial']); // Sync result
            $table->json('response_data_json')->nullable(); // WhatsApp API response
            $table->text('error_message')->nullable(); // Error details if failed
            $table->integer('messages_synced')->default(0); // Count of messages synced
            $table->integer('messages_updated')->default(0); // Count of messages updated
            $table->timestamp('synced_at'); // When sync was performed
            $table->timestamps();

            // Indexes
            $table->index(['sync_type', 'entity_id']);
            $table->index(['status', 'synced_at']);
            $table->index(['synced_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('whatsapp_sync_logs');
    }
};
