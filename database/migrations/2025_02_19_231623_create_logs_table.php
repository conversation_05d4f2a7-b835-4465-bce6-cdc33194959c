<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('logs', function (Blueprint $table) {
            $table->id();
            $table->integer("organization_id")->nullable()->default(null);
            $table->integer("user_id")->nullable()->default(null);
            $table->boolean("is_error")->default(false);
            $table->string("from")->default(null);
            $table->string("message")->default(null);
            $table->text("log")->default(null);
            $table->timestamps();

            $table->index(["organization_id", "user_id"]);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('logs');
    }
};
