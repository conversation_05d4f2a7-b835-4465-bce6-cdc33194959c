<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('steps', function (Blueprint $table) {
            $table->boolean('is_input_required')->default(false)->after('navigation_rules');
            $table->boolean('is_button_click_required')->default(false)->after('is_input_required');
            $table->boolean('is_moving_automatic')->default(false)->after('is_button_click_required');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('steps', function (Blueprint $table) {
            $table->dropColumn(['is_input_required', 'is_button_click_required', 'is_moving_automatic']);
        });
    }
};
