<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // For SQLite, we need to recreate the table with string columns
        if (DB::getDriverName() === 'sqlite') {
            // Create backup table
            DB::statement('CREATE TABLE whatsapp_webhook_logs_backup AS SELECT * FROM whatsapp_webhook_logs');
            
            // Drop original table
            Schema::dropIfExists('whatsapp_webhook_logs');
            
            // Create new table with string columns instead of enums
            Schema::create('whatsapp_webhook_logs', function (Blueprint $table) {
                $table->id();
                $table->unsignedBigInteger('organization_id')->nullable();
                $table->string('phone_number_id')->nullable();
                $table->string('event_type')->default('other'); // Changed from enum to string
                $table->json('webhook_payload');
                $table->timestamp('processed_at')->nullable();
                $table->string('processing_status')->default('pending'); // Changed from enum to string
                $table->text('error_message')->nullable();
                $table->timestamps();

                // Indexes for better performance
                $table->index('organization_id');
                $table->index('phone_number_id');
                $table->index('event_type');
                $table->index('processing_status');
                $table->index('processed_at');
                $table->index('created_at');

                // Foreign key constraint
                $table->foreign('organization_id')->references('id')->on('organizations')->onDelete('set null');
            });

            // Restore data from backup
            DB::statement('INSERT INTO whatsapp_webhook_logs SELECT * FROM whatsapp_webhook_logs_backup');
            
            // Clean up backup
            Schema::dropIfExists('whatsapp_webhook_logs_backup');
        } else {
            // For other databases, use ALTER TABLE
            Schema::table('whatsapp_webhook_logs', function (Blueprint $table) {
                // Change event_type from enum to string
                $table->string('event_type')->default('other')->change();
                
                // Change processing_status from enum to string
                $table->string('processing_status')->default('pending')->change();
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // For SQLite, recreate with enum-like constraints
        if (DB::getDriverName() === 'sqlite') {
            // Create backup table
            DB::statement('CREATE TABLE whatsapp_webhook_logs_backup AS SELECT * FROM whatsapp_webhook_logs');
            
            // Drop original table
            Schema::dropIfExists('whatsapp_webhook_logs');
            
            // Create new table with enum columns (back to original structure)
            Schema::create('whatsapp_webhook_logs', function (Blueprint $table) {
                $table->id();
                $table->unsignedBigInteger('organization_id')->nullable();
                $table->string('phone_number_id')->nullable();
                $table->enum('event_type', ['message', 'status', 'other', 'verification', 'security'])->default('other');
                $table->json('webhook_payload');
                $table->timestamp('processed_at')->nullable();
                $table->enum('processing_status', ['pending', 'success', 'failed'])->default('pending');
                $table->text('error_message')->nullable();
                $table->timestamps();

                // Indexes for better performance
                $table->index('organization_id');
                $table->index('phone_number_id');
                $table->index('event_type');
                $table->index('processing_status');
                $table->index('processed_at');
                $table->index('created_at');

                // Foreign key constraint
                $table->foreign('organization_id')->references('id')->on('organizations')->onDelete('set null');
            });

            // Restore data from backup (only valid enum values)
            DB::statement("
                INSERT INTO whatsapp_webhook_logs 
                SELECT * FROM whatsapp_webhook_logs_backup 
                WHERE event_type IN ('message', 'status', 'other', 'verification', 'security')
                AND processing_status IN ('pending', 'success', 'failed')
            ");
            
            // Clean up backup
            Schema::dropIfExists('whatsapp_webhook_logs_backup');
        } else {
            // For other databases, use ALTER TABLE back to enum
            Schema::table('whatsapp_webhook_logs', function (Blueprint $table) {
                // Change event_type back to enum
                DB::statement("ALTER TABLE whatsapp_webhook_logs MODIFY COLUMN event_type ENUM('message', 'status', 'other', 'verification', 'security') DEFAULT 'other'");
                
                // Change processing_status back to enum
                DB::statement("ALTER TABLE whatsapp_webhook_logs MODIFY COLUMN processing_status ENUM('pending', 'success', 'failed') DEFAULT 'pending'");
            });
        }
    }
};
