<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('buttons', function (Blueprint $table) {
            $table->json('commands_to_run')->nullable()->after('internal_data');
            $table->string('step_to_go')->nullable()->after('commands_to_run');
            $table->string('value')->nullable()->after('step_to_go');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('buttons', function (Blueprint $table) {
            $table->dropColumn(['commands_to_run', 'step_to_go', 'value']);
        });
    }
};
