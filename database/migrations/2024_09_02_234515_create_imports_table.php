<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('imports', function (Blueprint $table) {
            $table->id();
            $table->integer("organization_id");
            $table->integer("user_id");
            $table->string('model')->nullable();
            $table->string('status')->nullable();
            $table->text("header")->nullable();
            $table->text("map")->nullable();
            $table->boolean("is_processed")->default(false);
            $table->string('file')->nullable();
            $table->string('filename')->nullable();
            $table->string('filesize')->nullable();
            $table->string('filepath')->nullable();
            $table->string('file_extension')->nullable();
            $table->string('file_mime_type')->nullable();
            $table->timestamps();
            $table->softDeletes();

            $table->index('organization_id');
            $table->index('user_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('imports');
    }
};
