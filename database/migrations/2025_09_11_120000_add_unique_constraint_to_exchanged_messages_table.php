<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('exchanged_messages', function (Blueprint $table) {
            // Add unique constraint to prevent duplicate ExchangedMessages for the same message
            // This will prevent race conditions when multiple webhooks try to create
            // ExchangedMessage for the same message_id + organization_id combination
            $table->unique(['message_id', 'organization_id'], 'unique_exchanged_message_per_organization');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('exchanged_messages', function (Blueprint $table) {
            $table->dropUnique('unique_exchanged_message_per_organization');
        });
    }
};
