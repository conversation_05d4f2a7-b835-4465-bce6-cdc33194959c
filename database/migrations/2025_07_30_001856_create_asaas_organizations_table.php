<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('asaas_organizations', function (Blueprint $table) {
            $table->id();

            $table->foreignId('organization_id')->constrained()->onDelete('cascade')->unique();

            $table->string('asaas_account_id')->nullable()->index();
            $table->string('asaas_api_key')->nullable();
            $table->string('asaas_wallet_id')->nullable()->index();
            $table->string('asaas_environment')->index();

            $table->string('name')->nullable()->index();
            $table->string('email')->nullable()->index();
            $table->string('phone')->nullable();
            $table->string('mobile_phone')->nullable();

            $table->string('address')->nullable();
            $table->string('address_number')->nullable();
            $table->string('complement')->nullable();
            $table->string('province')->nullable();
            $table->string('postal_code')->nullable()->index();

            $table->string('cpf_cnpj', 20)->nullable()->index();
            $table->string('company_type', 20)->nullable();

            $table->decimal('income_value', 12, 2)->nullable();
            $table->string('site')->nullable();

            $table->boolean('is_active')->default(true)->index();
            $table->timestamp('last_sync_at')->nullable()->index();
            $table->json('sync_errors')->nullable();

            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('asaas_organizations');
    }
};
