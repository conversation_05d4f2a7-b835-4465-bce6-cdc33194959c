<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('clients', function (Blueprint $table) {
            // ASAAS Customer Integration
            $table->string('asaas_customer_id')->nullable()->after('id');
            $table->timestamp('asaas_synced_at')->nullable()->after('asaas_customer_id');
            $table->json('asaas_sync_errors')->nullable()->after('asaas_synced_at');
            
            // Index for better performance
            $table->index('asaas_customer_id');
            $table->index('asaas_synced_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('clients', function (Blueprint $table) {
            $table->dropIndex(['asaas_customer_id']);
            $table->dropIndex(['asaas_synced_at']);
            
            $table->dropColumn([
                'asaas_customer_id',
                'asaas_synced_at',
                'asaas_sync_errors',
            ]);
        });
    }
};
