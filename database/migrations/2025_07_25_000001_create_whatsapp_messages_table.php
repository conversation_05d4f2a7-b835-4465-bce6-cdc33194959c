<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('whatsapp_messages', function (Blueprint $table) {
            $table->id();
            $table->foreignId('message_id')->constrained('messages');

            // WhatsApp response fields
            $table->string('whatsapp_message_id')->nullable(); // The WhatsApp message ID
            $table->string('message_status')->nullable(); // accepted, sent, delivered, read, failed
            $table->string('wa_id')->nullable(); // WhatsApp ID from contacts
            $table->string('input_phone')->nullable(); // Input phone from contacts
            $table->string('messaging_product')->default('whatsapp');
            $table->text('json')->nullable(); // Full WhatsApp response

            $table->timestamps();
            $table->softDeletes();

            $table->engine = 'InnoDB';

            // INDEXES
            $table->index('message_id');
            $table->index('whatsapp_message_id');
            $table->index('message_status');
            $table->index('wa_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('whatsapp_messages');
    }
};
