<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('campaign_performance_snapshots', function (Blueprint $table) {
            $table->id();
            $table->foreignId('campaign_id')->constrained()->onDelete('cascade');
            $table->date('snapshot_date');
            $table->json('metrics_json'); // Complete metrics snapshot
            $table->integer('total_messages_at_date')->default(0);
            $table->integer('sent_count_at_date')->default(0);
            $table->integer('delivered_count_at_date')->default(0);
            $table->integer('failed_count_at_date')->default(0);
            $table->integer('read_count_at_date')->default(0);
            $table->integer('response_count_at_date')->default(0);
            $table->decimal('delivery_rate_at_date', 5, 2)->default(0);
            $table->decimal('read_rate_at_date', 5, 2)->default(0);
            $table->decimal('response_rate_at_date', 5, 2)->default(0);
            $table->timestamps();

            // Indexes
            $table->unique(['campaign_id', 'snapshot_date']);
            $table->index(['snapshot_date']);
            $table->index(['campaign_id', 'snapshot_date']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('campaign_performance_snapshots');
    }
};
