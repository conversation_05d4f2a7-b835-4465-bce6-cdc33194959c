<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add WhatsApp fields to conversations table
        Schema::table('conversations', function (Blueprint $table) {
            // Check if columns don't exist before adding them
            if (!Schema::hasColumn('conversations', 'whatsapp_metadata')) {
                $table->json('whatsapp_metadata')->nullable()->after('raw_data');
            }
            if (!Schema::hasColumn('conversations', 'whatsapp_contact_name')) {
                $table->string('whatsapp_contact_name')->nullable()->after('whatsapp_metadata');
            }
            if (!Schema::hasColumn('conversations', 'whatsapp_profile_name')) {
                $table->string('whatsapp_profile_name')->nullable()->after('whatsapp_contact_name');
            }
        });

        // Add WhatsApp fields to interactions table
        Schema::table('interactions', function (Blueprint $table) {
            // Check if columns don't exist before adding them
            if (!Schema::hasColumn('interactions', 'whatsapp_raw_data')) {
                $table->json('whatsapp_raw_data')->nullable()->after('timeout_seconds');
            }
            if (!Schema::hasColumn('interactions', 'whatsapp_message_id')) {
                $table->string('whatsapp_message_id')->nullable()->after('whatsapp_raw_data');

                // Add unique index for non-null values only (MySQL 8.0+ feature)
                // If this fails, we'll skip the unique constraint
                try {
                    $table->unique('whatsapp_message_id', 'unique_whatsapp_message_id_not_null');
                } catch (\Exception $e) {
                    // Ignore if unique constraint fails
                }
            }
            if (!Schema::hasColumn('interactions', 'whatsapp_message_type')) {
                $table->string('whatsapp_message_type')->nullable()->after('whatsapp_message_id');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('conversations', function (Blueprint $table) {
            $columnsToCheck = ['whatsapp_contact_name', 'whatsapp_profile_name', 'whatsapp_metadata'];
            foreach ($columnsToCheck as $column) {
                if (Schema::hasColumn('conversations', $column)) {
                    $table->dropColumn($column);
                }
            }
        });

        Schema::table('interactions', function (Blueprint $table) {
            // Drop unique index if it exists
            try {
                $table->dropUnique('unique_whatsapp_message_id_not_null');
            } catch (\Exception $e) {
                // Ignore if index doesn't exist
            }

            $columnsToCheck = ['whatsapp_message_id', 'whatsapp_message_type', 'whatsapp_raw_data'];
            foreach ($columnsToCheck as $column) {
                if (Schema::hasColumn('interactions', $column)) {
                    $table->dropColumn($column);
                }
            }
        });
    }
};
