<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('campaigns', function (Blueprint $table) {
            $table->foreignId('phone_number_id')->nullable()->after('template_id')->index();
        });
        Schema::table('templates', function (Blueprint $table) {
            $table->foreignId('phone_number_id')->nullable()->after('organization_id')->index();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('campaigns', function (Blueprint $table) {
            $table->dropIndex(['phone_number_id']);
            $table->dropColumn('phone_number_id');
        });
        Schema::table('templates', function (Blueprint $table) {
            $table->dropIndex(['phone_number_id']);
            $table->dropColumn('phone_number_id');
        });
    }
};
