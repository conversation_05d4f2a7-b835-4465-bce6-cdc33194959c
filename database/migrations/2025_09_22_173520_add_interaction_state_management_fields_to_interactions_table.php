<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('interactions', function (Blueprint $table) {
            $table->string('state', 20)->default('pending')->after('raw_data');
            $table->timestamp('sent_at')->nullable()->after('state');
            $table->timestamp('responded_at')->nullable()->after('sent_at');
            $table->timestamp('processed_at')->nullable()->after('responded_at');
            $table->boolean('requires_response')->default(true)->after('processed_at');
            $table->string('expected_response_type', 20)->nullable()->after('requires_response');
            $table->integer('timeout_seconds')->nullable()->after('expected_response_type');

            // Add indexes for performance
            $table->index(['state', 'conversation_id']);
            $table->index(['sent_at', 'state']);
            $table->index(['requires_response', 'state']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('interactions', function (Blueprint $table) {
            $table->dropIndex(['state', 'conversation_id']);
            $table->dropIndex(['sent_at', 'state']);
            $table->dropIndex(['requires_response', 'state']);

            $table->dropColumn([
                'state',
                'sent_at',
                'responded_at',
                'processed_at',
                'requires_response',
                'expected_response_type',
                'timeout_seconds'
            ]);
        });
    }
};
