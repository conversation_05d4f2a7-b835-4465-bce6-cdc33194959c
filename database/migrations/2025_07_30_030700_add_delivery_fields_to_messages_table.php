<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('messages', function (Blueprint $table) {
            // Add delivery tracking fields
            $table->integer('delivery_attempts')->default(0)->after('is_direct_message');
            $table->timestamp('last_attempt_at')->nullable()->after('delivery_attempts');
            $table->integer('max_retries')->default(3)->after('last_attempt_at');
            $table->timestamp('next_retry_at')->nullable()->after('max_retries');
            $table->text('last_error_message')->nullable()->after('next_retry_at');
            
            // Add indexes for retry logic
            $table->index(['status', 'next_retry_at']);
            $table->index(['campaign_id', 'status']);
            $table->index(['delivery_attempts', 'max_retries']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('messages', function (Blueprint $table) {
            $table->dropIndex(['status', 'next_retry_at']);
            $table->dropIndex(['campaign_id', 'status']);
            $table->dropIndex(['delivery_attempts', 'max_retries']);
            
            $table->dropColumn([
                'delivery_attempts',
                'last_attempt_at',
                'max_retries',
                'next_retry_at',
                'last_error_message'
            ]);
        });
    }
};
