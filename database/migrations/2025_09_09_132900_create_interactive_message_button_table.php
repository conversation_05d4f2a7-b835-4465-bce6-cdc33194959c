<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Check if table already exists before creating
        if (!Schema::hasTable('interactive_message_button')) {
            Schema::create('interactive_message_button', function (Blueprint $table) {
                $table->id();
                $table->bigInteger('interactive_message_id')->nullable();
                $table->bigInteger('button_id')->nullable();
                $table->timestamps();

                // Unique constraint to prevent duplicate relationships
                $table->unique(['interactive_message_id', 'button_id'], 'im_button_unique');

                // Indexes
                $table->index(['interactive_message_id']);
                $table->index(['button_id']);
            });
        } else {
            // Table exists, try to add unique constraint if it doesn't exist
            try {
                Schema::table('interactive_message_button', function (Blueprint $table) {
                    $table->unique(['interactive_message_id', 'button_id'], 'im_button_unique');
                });
            } catch (\Exception $e) {
                // Constraint might already exist, ignore the error
            }
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('interactive_message_button');
    }
};
