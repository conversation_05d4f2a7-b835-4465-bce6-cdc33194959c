<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('organizations', function (Blueprint $table) {
            // ASAAS Integration Fields
            $table->string('asaas_account_id')->nullable()->after('id');
            $table->string('asaas_api_key')->nullable()->after('asaas_account_id');
            $table->string('asaas_wallet_id')->nullable()->after('asaas_api_key');
            $table->string('asaas_environment')->default('sandbox')->after('asaas_wallet_id'); // sandbox or production
            
            // Subscription Management
            $table->string('asaas_subscription_id')->nullable()->after('asaas_environment');
            $table->string('subscription_status')->default('inactive')->after('asaas_subscription_id'); // active, inactive, overdue, canceled
            $table->decimal('subscription_value', 10, 2)->nullable()->after('subscription_status');
            $table->date('subscription_due_date')->nullable()->after('subscription_value');
            $table->date('subscription_started_at')->nullable()->after('subscription_due_date');
            $table->date('subscription_expires_at')->nullable()->after('subscription_started_at');
            
            // Courtesy Control
            $table->boolean('is_courtesy')->default(false)->after('subscription_expires_at');
            $table->date('courtesy_expires_at')->nullable()->after('is_courtesy');
            $table->text('courtesy_reason')->nullable()->after('courtesy_expires_at');
            
            // Billing Information
            $table->decimal('monthly_revenue', 15, 2)->nullable()->after('courtesy_reason');
            $table->string('company_type')->nullable()->after('monthly_revenue'); // MEI, LTDA, SA, etc.
            $table->date('birth_date')->nullable()->after('company_type'); // For individual persons
            
            // Address Information (if not already present)
            if (!Schema::hasColumn('organizations', 'address')) {
                $table->string('address')->nullable()->after('birth_date');
            }
            if (!Schema::hasColumn('organizations', 'address_number')) {
                $table->string('address_number')->nullable()->after('address');
            }
            if (!Schema::hasColumn('organizations', 'complement')) {
                $table->string('complement')->nullable()->after('address_number');
            }
            if (!Schema::hasColumn('organizations', 'province')) {
                $table->string('province')->nullable()->after('complement');
            }
            if (!Schema::hasColumn('organizations', 'city')) {
                $table->string('city')->nullable()->after('province');
            }
            if (!Schema::hasColumn('organizations', 'state')) {
                $table->string('state', 2)->nullable()->after('city');
            }
            if (!Schema::hasColumn('organizations', 'postal_code')) {
                $table->string('postal_code', 10)->nullable()->after('state');
            }
            if (!Schema::hasColumn('organizations', 'phone')) {
                $table->string('phone')->nullable()->after('postal_code');
            }
            if (!Schema::hasColumn('organizations', 'mobile_phone')) {
                $table->string('mobile_phone')->nullable()->after('phone');
            }
            
            // Indexes for better performance
            $table->index('asaas_account_id');
            $table->index('asaas_subscription_id');
            $table->index('subscription_status');
            $table->index('subscription_due_date');
            $table->index('is_courtesy');
            $table->index('courtesy_expires_at');
            $table->index(['subscription_status', 'is_courtesy']);
            $table->index(['subscription_due_date', 'subscription_status']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('organizations', function (Blueprint $table) {
            // Remove ASAAS fields
            $table->dropColumn([
                'asaas_account_id',
                'asaas_api_key',
                'asaas_wallet_id',
                'asaas_environment',
                'asaas_subscription_id',
                'subscription_status',
                'subscription_value',
                'subscription_due_date',
                'subscription_started_at',
                'subscription_expires_at',
                'is_courtesy',
                'courtesy_expires_at',
                'courtesy_reason',
                'monthly_revenue',
                'company_type',
                'birth_date',
            ]);
            
            // Only drop address fields if they were created by this migration
            // In a real scenario, you might want to check if these fields existed before
            $addressFields = [
                'address', 'address_number', 'complement', 'province', 
                'city', 'state', 'postal_code', 'phone', 'mobile_phone'
            ];
            
            foreach ($addressFields as $field) {
                if (Schema::hasColumn('organizations', $field)) {
                    // Only drop if it was likely created by this migration
                    // This is a simplified approach - in production you might want more sophisticated logic
                    $table->dropColumn($field);
                }
            }
        });
    }
};
