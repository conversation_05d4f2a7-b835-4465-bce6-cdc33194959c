<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('subscriptions', function (Blueprint $table) {
            $table->integer('client_id')->nullable()->after('organization_id');

            $table->boolean('is_client_subscription')->default(false)->after('client_id');

            $table->index('client_id');
            $table->index('is_client_subscription');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('subscriptions', function (Blueprint $table) {
            $table->dropIndex(['client_id']);
            $table->dropIndex(['is_client_subscription']);

            $table->dropColumn([
                'client_id',
                'is_client_subscription',
            ]);
        });
    }
};
