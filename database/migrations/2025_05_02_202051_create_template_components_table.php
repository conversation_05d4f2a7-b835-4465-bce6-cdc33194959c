<?php

use App\Enums\OTPTypeType;
use App\Enums\TypeType;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('template_components', function (Blueprint $table) {
            $table->id();

            $table->integer('organization_id')->nullable();
            $table->integer('client_id')->nullable();
            $table->integer('template_id');

            $table->string('type');
            $table->string('format');
            $table->string('text')->nullable();
            $table->json('example')->nullable();    //For (Header, Component and Footer)
            $table->json('buttons')->nullable();    //For (Buttons)

            /************************Buttons propertys *************************************/

            // $table->json('library_template_button_inputs');
            // $table->enum('type', array_column(TypeType::cases(), 'value'));
            // //$table->string('type');
            // $table->enum('otp_type', array_column(OTPTypeType::cases(), 'value'))->nullable();
            // //$table->string('otp_type')->nullable();
            // $table->string('phone_number')->nullable();
            // $table->json('url')->nullable();
            // $table->boolean('zero_tap_terms_accepted')->nullable();
            // $table->json('supported_apps')->nullable();

            /************************Header/Component/Footer propertys *************************************/

            // $table->json('library_template_body_inputs');
            // $table->boolean('add_contact_number')->nullable();
            // $table->boolean('add_learn_more_link')->nullable();
            // $table->boolean('add_security_recommendation')->nullable();
            // $table->boolean('add_track_package_link')->nullable();
            // $table->integer('code_expiration_minutes')->nullable();

            $table->timestamps();
            $table->softDeletes();

            $table->engine = 'InnoDB';

            // INDEXES
            $table->index('organization_id');
            $table->index('template_id');
            $table->index('client_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('whats_template_components');
    }
};
