<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('whatsapp_messages', function (Blueprint $table) {
            // Sync tracking fields
            $table->timestamp('last_status_check')->nullable()->after('updated_at');
            $table->integer('status_check_count')->default(0)->after('last_status_check');
            $table->timestamp('delivery_confirmed_at')->nullable()->after('status_check_count');
            $table->boolean('needs_status_check')->default(false)->after('delivery_confirmed_at');
            
            // Add indexes for sync operations
            $table->index(['needs_status_check', 'last_status_check']);
            $table->index(['delivery_confirmed_at']);
            $table->index(['status_check_count']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('whatsapp_messages', function (Blueprint $table) {
            $table->dropIndex(['needs_status_check', 'last_status_check']);
            $table->dropIndex(['delivery_confirmed_at']);
            $table->dropIndex(['status_check_count']);
            
            $table->dropColumn([
                'last_status_check',
                'status_check_count',
                'delivery_confirmed_at',
                'needs_status_check'
            ]);
        });
    }
};
