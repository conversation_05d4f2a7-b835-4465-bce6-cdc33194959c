<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Check if table already exists before creating
        if (!Schema::hasTable('interactive_messages')) {
            Schema::create('interactive_messages', function (Blueprint $table) {
            $table->id();
            $table->bigInteger('organization_id')->nullable();

            // Message content
            $table->string('header')->nullable(); // Optional header text
            $table->text('body'); // Required body text
            $table->string('footer')->nullable(); // Optional footer text

            // Interactive type (button, list, flow)
            $table->enum('type', ['button', 'list', 'flow']);

            // For LIST type - text shown on the list button
            $table->string('button_text')->nullable()->default('Ver opções');

            $table->timestamps();

            // Indexes
            $table->index(['organization_id', 'type']);
            $table->index(['organization_id', 'created_at']);
        });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('interactive_messages');
    }
};
