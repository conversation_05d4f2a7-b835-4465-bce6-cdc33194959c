<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('telegram_chats', function (Blueprint $table) {
            $table->id();
            $table->bigInteger('organization_id')->nullable()->default(null);
            $table->bigInteger('bot_id')->nullable()->default(null);
            $table->bigInteger('user_id')->nullable()->default(null);
            $table->bigInteger('chat_id');
            $table->bigInteger('from_id');
            $table->boolean('has_active_flow')->default(false);
            $table->boolean('has_broken_flow')->default(false);
            $table->timestamps();

            $table->index('chat_id');
            $table->index('organization_id');
            $table->index('user_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('telegram_chats');
    }
};
