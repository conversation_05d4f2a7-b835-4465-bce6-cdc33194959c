<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('exchanged_messages', function (Blueprint $table) {
            $table->id();
            $table->bigInteger('organization_id');
            $table->bigInteger('client_id')->nullable();
            $table->bigInteger('phone_number_id')->nullable();
            $table->bigInteger('conversation_id')->nullable();
            $table->bigInteger('webhook_log_id')->nullable();
            $table->bigInteger('message_id')->nullable();

            $table->boolean('inbound')->default(false);
            $table->boolean('outbound')->default(false);
            $table->text('message')->nullable();
            $table->json('json')->nullable();
            $table->timestamp('sent_at')->nullable();

            $table->timestamps();
            $table->softDeletes();

            $table->engine = 'InnoDB';

            // INDEXES
            $table->index('organization_id');
            $table->index('client_id');
            $table->index('phone_number_id');
            $table->index('conversation_id');
            $table->index('webhook_log_id');
            $table->index('message_id');
            $table->index('inbound');
            $table->index('outbound');
            $table->index('sent_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('exchanged_messages');
    }
};
