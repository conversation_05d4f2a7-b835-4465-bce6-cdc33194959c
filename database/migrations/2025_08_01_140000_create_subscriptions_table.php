<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('subscriptions', function (Blueprint $table) {
            $table->id();
            
            // Relacionamentos
            $table->foreignId('organization_id')->unique()->constrained()->onDelete('cascade');
            
            // Dados da Assinatura
            $table->enum('status', ['ACTIVE', 'INACTIVE', 'SUSPENDED', 'CANCELLED', 'EXPIRED'])->default('ACTIVE');
            $table->enum('billing_type', ['BOLETO', 'CREDIT_CARD', 'PIX', 'UNDEFINED'])->default('BOLETO');
            $table->enum('cycle', ['MONTHLY', 'QUARTERLY', 'SEMIANNUAL', 'YEARLY'])->default('MONTHLY');
            $table->decimal('value', 10, 2)->default(0.00);
            
            // Datas de Controle
            $table->timestamp('started_at');
            $table->timestamp('expires_at')->nullable();
            $table->timestamp('next_due_date')->nullable();
            $table->timestamp('end_date')->nullable();
            
            // Cortesia/Teste Gratuito
            $table->boolean('is_courtesy')->default(false);
            $table->timestamp('courtesy_expires_at')->nullable();
            $table->string('courtesy_reason')->nullable();
            $table->boolean('is_trial')->default(false);
            $table->timestamp('trial_expires_at')->nullable();
            $table->integer('trial_days')->nullable()->default(30);
            
            // Dados Adicionais
            $table->text('description')->nullable();
            $table->integer('max_payments')->nullable();
            $table->string('external_reference')->nullable();
            
            // Desconto
            $table->decimal('discount_value', 8, 2)->nullable();
            $table->enum('discount_type', ['FIXED', 'PERCENTAGE'])->nullable();
            $table->integer('discount_due_date_limit_days')->nullable()->default(0);
            
            // Multa e Juros
            $table->decimal('fine_value', 8, 2)->nullable()->default(0.00);
            $table->decimal('interest_value', 8, 2)->nullable()->default(0.00);
            
            // Controle
            $table->boolean('deleted')->default(false);
            
            $table->timestamps();
            $table->softDeletes();
            
            // Indexes for better performance
            $table->index('organization_id');
            $table->index('status');
            $table->index('expires_at');
            $table->index('courtesy_expires_at');
            $table->index('trial_expires_at');
            $table->index('next_due_date');
            $table->index('is_courtesy');
            $table->index('is_trial');
            $table->index('external_reference');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('subscriptions');
    }
};
