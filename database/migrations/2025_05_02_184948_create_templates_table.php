<?php

use App\Enums\CategoryType;
use App\Enums\LanguageType;
use App\Enums\ParameterFormatType;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('templates', function (Blueprint $table) {
            $table->id();

            $table->integer('organization_id')->nullable();
            $table->integer('user_id')->nullable();
            $table->integer('client_id')->nullable();

            $table->string('name');
            $table->enum('category', array_column(CategoryType::cases(), 'value'))->default(CategoryType::Marketing->value);
            //$table->string('category');
            $table->enum('parameter_format', array_column(ParameterFormatType::cases(), 'value'))->nullable();
            //$table->enum('parameter_format')->nullable();
            $table->enum('language', array_column(LanguageType::cases(), 'value'))->default(LanguageType::Portugues_BR->value);
            //$table->enum('language');
            $table->string('library_template_name')->nullable();
            $table->string('id_external')->nullable();
            $table->string('status')->nullable();

            $table->timestamps();
            $table->softDeletes();

            $table->engine = 'InnoDB';

            // INDEXES
            $table->index('organization_id');
            $table->index('user_id');
            $table->index('client_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('whats_templates');
    }
};
