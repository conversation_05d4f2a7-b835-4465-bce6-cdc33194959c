<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('templates_publishings', function (Blueprint $table) {
            $table->id();
            $table->foreignId('template_id')->constrained('templates')->onDelete('cascade');
            $table->integer('service_id')->nullable();
            $table->boolean('is_queued')->default(false);
            $table->boolean('is_published')->default(false);
            $table->string('status')->nullable();
            $table->timestamp('published_at')->nullable();
            $table->timestamps();

            $table->index('template_id');
            $table->index('service_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('templates_publishings');
    }
};
