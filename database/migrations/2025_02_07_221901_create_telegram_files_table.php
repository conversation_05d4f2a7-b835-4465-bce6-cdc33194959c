<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('telegram_files', function (Blueprint $table) {
            $table->id();

            $table->bigInteger('organization_id')->nullable()->default(null);
            $table->bigInteger('user_id')->nullable()->default(null);
            $table->bigInteger('bot_id')->nullable()->default(null);
            $table->bigInteger('chat_id')->nullable()->default(null);
            $table->bigInteger('from_id')->nullable()->default(null);

            $table->string('file')->nullable();
            $table->string('filename')->nullable();
            $table->string('filesize')->nullable();
            $table->string('filepath')->nullable();
            $table->string('file_extension')->nullable();
            $table->string('file_mime_type')->nullable();

            $table->timestamps();
            $table->softDeletes();

            $table->index('organization_id');
            $table->index('user_id');
            $table->index('bot_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('telegram_files');
    }
};
