<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('projects', function (Blueprint $table) {
            $table->id();
            $table->integer('organization_id')->nullable();
            $table->integer('client_id')->nullable();
            $table->integer('budget_id')->nullable();

            $table->string('name');
            $table->longText('description')->nullable();

            $table->decimal('value')->nullable();
            $table->decimal('cost')->nullable();

            $table->timestamps();
            $table->softDeletes();

            $table->engine = 'InnoDB';

            // INDEXES
            $table->index('organization_id');
            $table->index('client_id');
            $table->index('budget_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('projects');
    }
};
