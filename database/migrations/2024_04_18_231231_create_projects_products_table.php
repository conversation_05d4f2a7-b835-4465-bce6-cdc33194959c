<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('projects_products', function (Blueprint $table) {
            $table->id();
            $table->integer('project_id')->nullable();
            $table->integer('product_id')->nullable();
            $table->integer('quantity')->nullable();
            $table->decimal('value')->nullable();
            $table->string('description')->nullable();
            $table->timestamps();

            $table->engine = 'InnoDB';
            // INDEXES
            $table->index('project_id');
            $table->index('product_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('projects_products');
    }
};
