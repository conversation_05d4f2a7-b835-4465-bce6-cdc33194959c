<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('stocks', function (Blueprint $table) {
            $table->integer('shop_id')->nullable()->after("organization_id");
            $table->index('shop_id');
        });
        Schema::table('stock_entries', function (Blueprint $table) {
            $table->integer('shop_id')->nullable()->after("organization_id");
            $table->index('shop_id');
        });
        Schema::table('stock_exits', function (Blueprint $table) {
            $table->integer('shop_id')->nullable()->after("organization_id");
            $table->index('shop_id');
        });
        Schema::table('batches', function (Blueprint $table) {
            $table->integer('shop_id')->nullable()->after("organization_id");
            $table->index('shop_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('stocks', function (Blueprint $table) {
            $table->dropColumn("shop_id");
        });
        Schema::table('stock_entries', function (Blueprint $table) {
            $table->dropColumn("shop_id");
        });
        Schema::table('stock_exits', function (Blueprint $table) {
            $table->dropColumn("shop_id");
        });
        Schema::table('batches', function (Blueprint $table) {
            $table->dropColumn("shop_id");
        });
    }
};
