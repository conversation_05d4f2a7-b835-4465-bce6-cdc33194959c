<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('flows', function (Blueprint $table) {
            $table->id();
            $table->bigInteger('organization_id')->nullable()->default(null);
            $table->string("name")->nullable()->default(null);
            $table->text("description")->nullable()->default(null);
            $table->integer('steps_count')->nullable()->default(null);
            $table->text("json")->nullable()->default(null);
            $table->timestamps();
            $table->softDeletes();

            $table->index('organization_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('flows');
    }
};
