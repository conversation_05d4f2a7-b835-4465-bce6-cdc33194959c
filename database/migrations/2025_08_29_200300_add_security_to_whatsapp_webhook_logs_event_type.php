<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // For SQLite, we need to recreate the table with the new enum values
        if (DB::getDriverName() === 'sqlite') {
            // Drop existing table and recreate with new enum
            Schema::dropIfExists('whatsapp_webhook_logs_backup');

            // Backup existing data
            DB::statement('CREATE TABLE whatsapp_webhook_logs_backup AS SELECT * FROM whatsapp_webhook_logs');

            // Drop original table
            Schema::dropIfExists('whatsapp_webhook_logs');

            // Create new table with updated enum
            Schema::create('whatsapp_webhook_logs', function (Blueprint $table) {
                $table->id();
                $table->unsignedBigInteger('organization_id')->nullable();
                $table->string('phone_number_id')->nullable();
                $table->enum('event_type', ['message', 'status', 'other', 'verification', 'security'])->default('other');
                $table->json('webhook_payload');
                $table->timestamp('processed_at')->nullable();
                $table->enum('processing_status', ['pending', 'success', 'failed'])->default('pending');
                $table->text('error_message')->nullable();
                $table->timestamps();

                // Indexes for better performance
                $table->index('organization_id');
                $table->index('phone_number_id');
                $table->index('event_type');
                $table->index('processing_status');
                $table->index('processed_at');
                $table->index('created_at');

                // Foreign key constraint
                $table->foreign('organization_id')->references('id')->on('organizations')->onDelete('set null');
            });

            // Restore data
            DB::statement('INSERT INTO whatsapp_webhook_logs SELECT * FROM whatsapp_webhook_logs_backup');

            // Clean up backup
            Schema::dropIfExists('whatsapp_webhook_logs_backup');
        } else {
            // For other databases, use ALTER TABLE
            DB::statement("ALTER TABLE whatsapp_webhook_logs MODIFY COLUMN event_type ENUM('message', 'status', 'other', 'verification', 'security') DEFAULT 'other'");
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // For SQLite, we need to recreate the table with the original enum values
        if (DB::getDriverName() === 'sqlite') {
            // Create temporary table with original enum values
            Schema::create('whatsapp_webhook_logs_temp', function (Blueprint $table) {
                $table->id();
                $table->unsignedBigInteger('organization_id')->nullable();
                $table->string('phone_number_id')->nullable();
                $table->enum('event_type', ['message', 'status', 'other', 'verification'])->default('other');
                $table->json('webhook_payload');
                $table->timestamp('processed_at')->nullable();
                $table->enum('processing_status', ['pending', 'success', 'failed'])->default('pending');
                $table->text('error_message')->nullable();
                $table->timestamps();

                // Indexes for better performance
                $table->index('organization_id');
                $table->index('phone_number_id');
                $table->index('event_type');
                $table->index('processing_status');
                $table->index('processed_at');
                $table->index('created_at');

                // Foreign key constraint
                $table->foreign('organization_id')->references('id')->on('organizations')->onDelete('set null');
            });

            // Copy data from original table (excluding security events)
            DB::statement("INSERT INTO whatsapp_webhook_logs_temp SELECT * FROM whatsapp_webhook_logs WHERE event_type != 'security'");

            // Drop original table
            Schema::dropIfExists('whatsapp_webhook_logs');

            // Rename temp table
            Schema::rename('whatsapp_webhook_logs_temp', 'whatsapp_webhook_logs');
        } else {
            // For other databases, use ALTER TABLE
            DB::statement("ALTER TABLE whatsapp_webhook_logs MODIFY COLUMN event_type ENUM('message', 'status', 'other', 'verification') DEFAULT 'other'");
        }
    }
};
