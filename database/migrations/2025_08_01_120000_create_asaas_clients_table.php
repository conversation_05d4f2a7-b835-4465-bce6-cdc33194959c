<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('asaas_clients', function (Blueprint $table) {
            $table->id();
            
            // Relationships
            $table->foreignId('client_id')->constrained()->onDelete('cascade');
            $table->foreignId('organization_id')->constrained()->onDelete('cascade');
            $table->string('asaas_customer_id')->unique();
            
            // Sync Control
            $table->timestamp('asaas_synced_at')->nullable();
            $table->json('asaas_sync_errors')->nullable();
            $table->enum('sync_status', ['pending', 'synced', 'error'])->default('pending');
            
            // Customer Data from Asaas API
            $table->string('name')->nullable();
            $table->string('email')->nullable();
            $table->string('phone', 20)->nullable();
            $table->string('mobile_phone', 20)->nullable();
            $table->string('address')->nullable();
            $table->string('address_number', 10)->nullable();
            $table->string('complement')->nullable();
            $table->string('province', 100)->nullable();
            $table->string('city_name', 100)->nullable();
            $table->string('state', 2)->nullable();
            $table->string('country', 100)->nullable();
            $table->string('postal_code', 10)->nullable();
            $table->string('cpf_cnpj', 18)->nullable();
            $table->enum('person_type', ['FISICA', 'JURIDICA'])->nullable();
            $table->string('external_reference')->nullable();
            $table->boolean('notification_disabled')->default(false);
            $table->text('additional_emails')->nullable();
            $table->text('observations')->nullable();
            $table->boolean('foreign_customer')->default(false);
            $table->boolean('deleted')->default(false);
            
            $table->timestamps();
            $table->softDeletes();
            
            // Indexes for better performance
            $table->index('asaas_customer_id');
            $table->index('client_id');
            $table->index('organization_id');
            $table->index('sync_status');
            $table->index('asaas_synced_at');
            $table->index('external_reference');
            $table->index('cpf_cnpj');
            $table->index('email');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('asaas_clients');
    }
};
