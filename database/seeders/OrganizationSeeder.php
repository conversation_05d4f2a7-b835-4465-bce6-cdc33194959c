<?php

namespace Database\Seeders;

// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use App\Models\Organization;
use Illuminate\Database\Seeder;

class OrganizationSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        $user = Organization::where('name', 'Zona51')->first();
        if(!$user){
            Organization::create([
                'name' => 'Zona51',
                'description' => 'Owners of all the shit!',
                'is_active' => true,
                'is_suspended' => false,
            ]);
        }
    }
}
