<?php

namespace Database\Seeders;

// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Profile;

class ProfileSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        $profiles = [
            "admin" => "Administrador",
            "employee" => "Colaborador",
            "client" => "Cliente",
            "super_admin" => "Super Admin"
        ];

        foreach ($profiles as $slug => $profile){
            if(Profile::where("slug", $slug)->first()){
                continue;
            }
            Profile::create([
                "slug" => $slug,
                "name" => $profile,
                "is_admin" => ($slug == "admin"),
                "is_super_admin" => ($slug == "super_admin")
            ]);
        }
    }
}
