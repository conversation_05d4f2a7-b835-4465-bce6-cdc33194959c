{"info": {"_postman_id": "whatsapp-realtime-messages", "name": "WhatsApp Real-Time Messages", "description": "Collection for WhatsApp real-time message sending API endpoints", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{auth_token}}", "type": "string"}]}, "variable": [{"key": "base_url", "value": "{{base_url}}", "type": "string"}, {"key": "auth_token", "value": "{{auth_token}}", "type": "string"}], "item": [{"name": "Send Real-Time Message", "item": [{"name": "Send Text Message (Success)", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response has success status\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.status).to.eql(\"success\");", "});", "", "pm.test(\"Response contains message data\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.data).to.have.property(\"message_id\");", "    pm.expect(jsonData.data).to.have.property(\"whatsapp_message_id\");", "    pm.expect(jsonData.data).to.have.property(\"status\");", "    pm.expect(jsonData.data).to.have.property(\"meta_response\");", "});", "", "pm.test(\"Message ID is present\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.data.message_id).to.be.a(\"number\");", "    pm.expect(jsonData.data.message_id).to.be.above(0);", "});", "", "// Store message_id for other requests", "if (pm.response.code === 200) {", "    var jsonData = pm.response.json();", "    pm.environment.set(\"last_message_id\", jsonData.data.message_id);", "    pm.environment.set(\"last_whatsapp_message_id\", jsonData.data.whatsapp_message_id);", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"text\": \"<PERSON><PERSON><PERSON> {{client.name}}! Esta é uma mensagem em tempo real enviada via API.\",\n    \"client_id\": {{client_id}},\n    \"phone_number_id\": {{phone_number_id}},\n    \"is_direct_message\": true\n}"}, "url": {"raw": "{{base_url}}/api/message/send-realtime", "host": ["{{base_url}}"], "path": ["api", "message", "send-realtime"]}, "description": "Sends a real-time WhatsApp message without using a template. The message supports variable substitution using {{client.field}} format."}, "response": [{"name": "Success Response", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"text\": \"<PERSON><PERSON><PERSON>! Esta é uma mensagem em tempo real.\",\n    \"client_id\": 1,\n    \"phone_number_id\": 1,\n    \"is_direct_message\": true\n}"}, "url": {"raw": "{{base_url}}/api/message/send-realtime", "host": ["{{base_url}}"], "path": ["api", "message", "send-realtime"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n    \"status\": \"success\",\n    \"message\": \"Message sent successfully\",\n    \"data\": {\n        \"message_id\": 123,\n        \"whatsapp_message_id\": \"wamid.HBgLNTU3OTkxNTE0OTU3FQIAEhggRDdBQjlCNzNCNzA4QzlGNEE4QjY4RjVGNzU4NzI1RTI\",\n        \"status\": \"sent\",\n        \"meta_response\": {\n            \"messages\": [\n                {\n                    \"id\": \"wamid.HBgLNTU3OTkxNTE0OTU3FQIAEhggRDdBQjlCNzNCNzA4QzlGNEE4QjY4RjVGNzU4NzI1RTI\"\n                }\n            ],\n            \"messaging_product\": \"whatsapp\",\n            \"contacts\": [\n                {\n                    \"input\": \"+5511999999999\",\n                    \"wa_id\": \"5511999999999\"\n                }\n            ]\n        }\n    },\n    \"errors\": null,\n    \"pagination\": null\n}"}]}, {"name": "Send Message with Template", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response has success status\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.status).to.eql(\"success\");", "});", "", "pm.test(\"Message uses template\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.data).to.have.property(\"message_id\");", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"text\": \"Mensagem usando template personalizado\",\n    \"client_id\": {{client_id}},\n    \"phone_number_id\": {{phone_number_id}},\n    \"template_id\": {{template_id}},\n    \"is_direct_message\": false\n}"}, "url": {"raw": "{{base_url}}/api/message/send-realtime", "host": ["{{base_url}}"], "path": ["api", "message", "send-realtime"]}, "description": "Sends a real-time WhatsApp message using a specific template. The template must be published to WhatsApp and belong to the user's organization."}, "response": []}], "description": "Endpoints for sending real-time WhatsApp messages"}, {"name": "Validation Tests", "item": [{"name": "Missing Required Fields", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {", "    pm.response.to.have.status(422);", "});", "", "pm.test(\"Response has validation errors\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.message).to.include(\"validation\");", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"client_id\": {{client_id}}\n    // Missing text and phone_number_id\n}"}, "url": {"raw": "{{base_url}}/api/message/send-realtime", "host": ["{{base_url}}"], "path": ["api", "message", "send-realtime"]}, "description": "Tests validation when required fields are missing"}, "response": []}, {"name": "Text Too Long", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {", "    pm.response.to.have.status(422);", "});", "", "pm.test(\"Response has validation error for text length\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.message).to.include(\"text\");", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"text\": \"{{long_text_over_4096_chars}}\",\n    \"client_id\": {{client_id}},\n    \"phone_number_id\": {{phone_number_id}}\n}"}, "url": {"raw": "{{base_url}}/api/message/send-realtime", "host": ["{{base_url}}"], "path": ["api", "message", "send-realtime"]}, "description": "Tests validation when text exceeds 4096 characters"}, "response": []}, {"name": "Invalid Client ID", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 500\", function () {", "    pm.response.to.have.status(500);", "});", "", "pm.test(\"Response has error about client\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.status).to.eql(\"error\");", "    pm.expect(jsonData.message).to.include(\"client\");", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"text\": \"Test message\",\n    \"client_id\": 99999,\n    \"phone_number_id\": {{phone_number_id}}\n}"}, "url": {"raw": "{{base_url}}/api/message/send-realtime", "host": ["{{base_url}}"], "path": ["api", "message", "send-realtime"]}, "description": "Tests error handling when client doesn't exist"}, "response": []}], "description": "Tests for validation and error handling"}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["// Set default variables if not already set", "if (!pm.environment.get(\"base_url\")) {", "    pm.environment.set(\"base_url\", \"http://localhost:8000\");", "}", "", "if (!pm.environment.get(\"client_id\")) {", "    pm.environment.set(\"client_id\", \"1\");", "}", "", "if (!pm.environment.get(\"phone_number_id\")) {", "    pm.environment.set(\"phone_number_id\", \"1\");", "}", "", "if (!pm.environment.get(\"template_id\")) {", "    pm.environment.set(\"template_id\", \"1\");", "}", "", "// Generate long text for validation testing", "var longText = \"A\".repeat(4097);", "pm.environment.set(\"long_text_over_4096_chars\", longText);"]}}]}