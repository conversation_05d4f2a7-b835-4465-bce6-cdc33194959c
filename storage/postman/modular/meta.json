{"info": {"_postman_id": "6b191c3f-2de2-4a50-9192-7960dc1113c5", "name": "Obvio API - Complete Collection", "description": "Complete API collection for Obvio system with organized endpoints, proper filters, and comprehensive request examples.", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "5508133", "_collection_link": "https://zona51.postman.co/workspace/Zona51~65e22124-fc5f-446b-9523-2ad13d170a45/collection/15215157-6b191c3f-2de2-4a50-9192-7960dc1113c5?action=share&source=collection_link&creator=5508133"}}