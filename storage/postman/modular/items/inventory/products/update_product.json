{"name": "Update Product", "request": {"method": "PUT", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Updated Product Name\"\n}"}, "url": {"raw": "{{URL}}/products/1", "host": ["{{URL}}"], "path": ["products", "1"]}}, "response": []}