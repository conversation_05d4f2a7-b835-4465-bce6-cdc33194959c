{"name": "Get All Products", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/products?name=&brand_id=&has_stock=&order_by=name&order_direction=asc", "host": ["{{URL}}"], "path": ["products"], "query": [{"key": "name", "value": "", "description": "Filter by product name"}, {"key": "brand_id", "value": "", "description": "Filter by brand ID"}, {"key": "has_stock", "value": "", "description": "Filter by stock availability"}, {"key": "order_by", "value": "name", "description": "Order by field"}, {"key": "order_direction", "value": "asc", "description": "Order direction"}]}}, "response": []}