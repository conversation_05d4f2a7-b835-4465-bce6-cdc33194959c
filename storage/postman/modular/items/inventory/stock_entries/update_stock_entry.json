{"name": "Update Stock Entry", "request": {"method": "PUT", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"brand_id\": 2,\n  \"product_id\": 2,\n  \"batch_id\": 2,\n  \"client_id\": 2,\n  \"project_id\": 2,\n  \"quantity\": 150,\n  \"value\": 2250.75,\n  \"description\": \"Updated stock entry - Additional materials from secondary supplier batch #2024-002\"\n}"}, "url": {"raw": "{{URL}}/stock_entries/1", "host": ["{{URL}}"], "path": ["stock_entries", "1"]}}, "response": []}