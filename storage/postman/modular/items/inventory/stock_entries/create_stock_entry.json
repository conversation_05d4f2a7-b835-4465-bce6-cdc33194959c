{"name": "Create Stock Entry", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"shop_id\": 1,\n  \"brand_id\": 1,\n  \"product_id\": 1,\n  \"batch_id\": 1,\n  \"client_id\": 1,\n  \"project_id\": 1,\n  \"quantity\": 100,\n  \"value\": 1500.50,\n  \"description\": \"Stock entry from supplier delivery - Premium construction materials batch #2024-001\"\n}"}, "url": {"raw": "{{URL}}/stock_entries", "host": ["{{URL}}"], "path": ["stock_entries"]}}, "response": []}