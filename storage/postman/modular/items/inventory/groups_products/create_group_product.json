{"name": "Create Group Product", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"group_id\": 1,\n  \"product_id\": 1\n}"}, "url": {"raw": "{{URL}}/groups_products", "host": ["{{URL}}"], "path": ["groups_products"]}}, "response": []}