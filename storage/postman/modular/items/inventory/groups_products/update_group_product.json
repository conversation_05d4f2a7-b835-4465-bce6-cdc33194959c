{"name": "Update Group Product", "request": {"method": "PUT", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"group_id\": 2,\n  \"product_id\": 2\n}"}, "url": {"raw": "{{URL}}/groups_products/1", "host": ["{{URL}}"], "path": ["groups_products", "1"]}}, "response": []}