{"name": "Get All Budgets", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/budgets?client=&client_id=&project_id=&order_by=created_at&order_direction=desc", "host": ["{{URL}}"], "path": ["budgets"], "query": [{"key": "client", "value": "", "description": "Filter by client name"}, {"key": "client_id", "value": "", "description": "Filter by client ID"}, {"key": "project_id", "value": "", "description": "Filter by project ID"}, {"key": "order_by", "value": "created_at", "description": "Order by field"}, {"key": "order_direction", "value": "desc", "description": "Order direction"}]}}, "response": []}