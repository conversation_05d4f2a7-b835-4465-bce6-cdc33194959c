{"name": "Create Budget", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"client_id\": 1,\n  \"value\": 25000.50,\n  \"cost\": 18500.75,\n  \"name\": \"Residential Construction Project Budget\",\n  \"description\": \"Comprehensive budget for 3-bedroom house construction including materials, labor, permits, and contingency funds\"\n}"}, "url": {"raw": "{{URL}}/budgets", "host": ["{{URL}}"], "path": ["budgets"]}}, "response": []}