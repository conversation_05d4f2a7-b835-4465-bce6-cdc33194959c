{"name": "Update Product History", "request": {"method": "PUT", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"product_id\": 1,\n  \"field\": \"description\",\n  \"alias\": \"<PERSON><PERSON>ri<PERSON>\",\n  \"old\": \"Old description\",\n  \"new\": \"New updated description\"\n}"}, "url": {"raw": "{{URL}}/products_histories/1", "host": ["{{URL}}"], "path": ["products_histories", "1"]}}, "response": []}