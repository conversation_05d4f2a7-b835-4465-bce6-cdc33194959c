{"name": "Create Product History", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"product_id\": 1,\n  \"field\": \"price\",\n  \"alias\": \"Preço\",\n  \"old\": \"10.50\",\n  \"new\": \"15.75\"\n}"}, "url": {"raw": "{{URL}}/products_histories", "host": ["{{URL}}"], "path": ["products_histories"]}}, "response": []}