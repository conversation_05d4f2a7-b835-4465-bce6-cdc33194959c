{"name": "Model Count Report", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/reports/products/count?start_date=2024-01-01&end_date=2024-12-31", "host": ["{{URL}}"], "path": ["reports", "products", "count"], "query": [{"key": "start_date", "value": "2024-01-01"}, {"key": "end_date", "value": "2024-12-31"}]}}, "response": []}