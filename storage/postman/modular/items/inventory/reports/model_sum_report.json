{"name": "Model Sum Report", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/reports/stock_entries/sum/value?start_date=2024-01-01&end_date=2024-12-31", "host": ["{{URL}}"], "path": ["reports", "stock_entries", "sum", "value"], "query": [{"key": "start_date", "value": "2024-01-01"}, {"key": "end_date", "value": "2024-12-31"}]}}, "response": []}