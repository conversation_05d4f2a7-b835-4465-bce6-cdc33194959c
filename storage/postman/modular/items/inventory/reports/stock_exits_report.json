{"name": "Stock Exits Report", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/reports/stock_exits?start_date=2024-01-01&end_date=2024-12-31&shop_id=1&brand_id=1", "host": ["{{URL}}"], "path": ["reports", "stock_exits"], "query": [{"key": "start_date", "value": "2024-01-01"}, {"key": "end_date", "value": "2024-12-31"}, {"key": "shop_id", "value": "1"}, {"key": "brand_id", "value": "1"}]}}, "response": []}