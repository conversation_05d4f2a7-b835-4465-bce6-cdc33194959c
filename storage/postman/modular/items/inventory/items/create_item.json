{"name": "Create <PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"product_id\": 1,\n  \"quantity\": 5,\n  \"price\": 100.0\n}"}, "url": {"raw": "{{URL}}/items", "host": ["{{URL}}"], "path": ["items"]}}, "response": []}