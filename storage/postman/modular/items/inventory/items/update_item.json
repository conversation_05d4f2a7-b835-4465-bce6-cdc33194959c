{"name": "Update Item", "request": {"method": "PUT", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"product_id\": 1,\n  \"quantity\": 10,\n  \"price\": 120.0\n}"}, "url": {"raw": "{{URL}}/items/1", "host": ["{{URL}}"], "path": ["items", "1"]}}, "response": []}