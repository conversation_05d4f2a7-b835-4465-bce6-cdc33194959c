{"name": "Get All Items", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/items?product_id=&sale_id=&budget_id=&order_by=created_at&order_direction=desc", "host": ["{{URL}}"], "path": ["items"], "query": [{"key": "product_id", "value": "", "description": "Filter by product ID"}, {"key": "sale_id", "value": "", "description": "Filter by sale ID"}, {"key": "budget_id", "value": "", "description": "Filter by budget ID"}, {"key": "order_by", "value": "created_at", "description": "Order by field"}, {"key": "order_direction", "value": "desc", "description": "Order direction"}]}}, "response": []}