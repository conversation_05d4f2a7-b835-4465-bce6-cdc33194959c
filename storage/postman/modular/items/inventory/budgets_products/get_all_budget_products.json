{"name": "Get All Budget Products", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/budgets_products?page=1&per_page=15&order=created_at&by=desc", "host": ["{{URL}}"], "path": ["budgets_products"], "query": [{"key": "page", "value": "1"}, {"key": "per_page", "value": "15"}, {"key": "order", "value": "created_at"}, {"key": "by", "value": "desc"}, {"key": "budget_id", "value": "1", "disabled": true}, {"key": "product_id", "value": "1", "disabled": true}]}}, "response": []}