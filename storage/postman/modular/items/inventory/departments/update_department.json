{"name": "Update Department", "request": {"method": "PUT", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Advanced Construction Department\",\n  \"is_active\": true\n}"}, "url": {"raw": "{{URL}}/departments/1", "host": ["{{URL}}"], "path": ["departments", "1"]}}, "response": []}