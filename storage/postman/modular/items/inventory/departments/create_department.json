{"name": "Create Department", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Construction Department\",\n  \"is_active\": true\n}"}, "url": {"raw": "{{URL}}/departments", "host": ["{{URL}}"], "path": ["departments"]}}, "response": []}