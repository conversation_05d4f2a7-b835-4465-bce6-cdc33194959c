{"name": "Create Custom Product", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"project_id\": 1,\n  \"budget_id\": 1,\n  \"quantity\": 10,\n  \"value\": 150.50,\n  \"description\": \"Custom product for project - High quality materials\"\n}"}, "url": {"raw": "{{URL}}/custom_products", "host": ["{{URL}}"], "path": ["custom_products"]}}, "response": []}