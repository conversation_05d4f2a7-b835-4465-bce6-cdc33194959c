{"name": "Get All Custom Products", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/custom_products?page=1&per_page=15&order=created_at&by=desc", "host": ["{{URL}}"], "path": ["custom_products"], "query": [{"key": "page", "value": "1"}, {"key": "per_page", "value": "15"}, {"key": "order", "value": "created_at"}, {"key": "by", "value": "desc"}, {"key": "project_id", "value": "1", "disabled": true}, {"key": "budget_id", "value": "1", "disabled": true}, {"key": "min_value", "value": "50.00", "disabled": true}, {"key": "max_value", "value": "500.00", "disabled": true}]}}, "response": []}