{"name": "Update Custom Product", "request": {"method": "PUT", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"project_id\": 1,\n  \"budget_id\": 2,\n  \"quantity\": 15,\n  \"value\": 225.75,\n  \"description\": \"Updated custom product - Premium materials with extended warranty\"\n}"}, "url": {"raw": "{{URL}}/custom_products/1", "host": ["{{URL}}"], "path": ["custom_products", "1"]}}, "response": []}