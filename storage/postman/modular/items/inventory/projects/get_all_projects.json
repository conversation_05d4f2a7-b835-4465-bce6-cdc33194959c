{"name": "Get All Projects", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/projects?name=&client=&client_id=&budget_id=&order_by=name&order_direction=asc", "host": ["{{URL}}"], "path": ["projects"], "query": [{"key": "name", "value": "", "description": "Filter by project name"}, {"key": "client", "value": "", "description": "Filter by client name"}, {"key": "client_id", "value": "", "description": "Filter by client ID"}, {"key": "budget_id", "value": "", "description": "Filter by budget ID"}, {"key": "order_by", "value": "name", "description": "Order by field"}, {"key": "order_direction", "value": "asc", "description": "Order direction"}]}}, "response": []}