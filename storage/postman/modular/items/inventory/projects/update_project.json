{"name": "Update Project", "request": {"method": "PUT", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Updated Project Name\"\n}"}, "url": {"raw": "{{URL}}/projects/1", "host": ["{{URL}}"], "path": ["projects", "1"]}}, "response": []}