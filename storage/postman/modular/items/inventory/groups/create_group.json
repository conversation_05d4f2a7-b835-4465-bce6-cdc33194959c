{"name": "Create Group", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Construction Materials\",\n  \"description\": \"Group for all construction-related materials including cement, steel, wood, and other building supplies\"\n}"}, "url": {"raw": "{{URL}}/groups", "host": ["{{URL}}"], "path": ["groups"]}}, "response": []}