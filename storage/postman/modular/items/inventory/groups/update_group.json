{"name": "Update Group", "request": {"method": "PUT", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Premium Construction Materials\",\n  \"description\": \"Updated group for premium construction materials including high-grade cement, reinforced steel, treated wood, and specialized building supplies\"\n}"}, "url": {"raw": "{{URL}}/groups/1", "host": ["{{URL}}"], "path": ["groups", "1"]}}, "response": []}