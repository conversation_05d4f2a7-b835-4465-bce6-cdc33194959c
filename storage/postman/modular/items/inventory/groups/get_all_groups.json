{"name": "Get All Groups", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/groups?page=1&per_page=15&order=name&by=asc", "host": ["{{URL}}"], "path": ["groups"], "query": [{"key": "page", "value": "1"}, {"key": "per_page", "value": "15"}, {"key": "order", "value": "name"}, {"key": "by", "value": "asc"}, {"key": "name", "value": "Construction", "disabled": true}]}}, "response": []}