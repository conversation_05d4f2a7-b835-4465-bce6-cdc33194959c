{"name": "Create Client", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"<PERSON>ru<PERSON>ões Ltda\",\n  \"phone\": \"+55 11 99999-8888\",\n  \"email\": \"<EMAIL>\",\n  \"profession\": \"Engenheiro Civil\",\n  \"birthdate\": \"1985-03-15\",\n  \"cpf\": \"123.456.789-01\",\n  \"cnpj\": \"12.345.678/0001-90\",\n  \"service\": \"Construção Civil e Reformas\",\n  \"address\": \"Rua das Construções, 123\",\n  \"number\": \"123\",\n  \"neighborhood\": \"Centro\",\n  \"cep\": \"01234-567\",\n  \"complement\": \"Sala 101\",\n  \"civil_state\": \"married\",\n  \"description\": \"Cliente especializado em construção residencial e comercial com mais de 15 anos de experiência no mercado\",\n  \"enable_asaas_integration\": true\n}"}, "url": {"raw": "{{URL}}/clients", "host": ["{{URL}}"], "path": ["clients"]}}, "response": []}