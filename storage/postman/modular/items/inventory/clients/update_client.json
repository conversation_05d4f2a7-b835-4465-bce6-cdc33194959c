{"name": "Update Client", "request": {"method": "PUT", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Updated Client Name\"\n}"}, "url": {"raw": "{{URL}}/clients/1", "host": ["{{URL}}"], "path": ["clients", "1"]}}, "response": []}