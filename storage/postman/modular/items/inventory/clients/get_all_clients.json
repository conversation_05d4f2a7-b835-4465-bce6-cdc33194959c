{"name": "Get All Clients", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/clients?name=&order_by=name&order_direction=asc", "host": ["{{URL}}"], "path": ["clients"], "query": [{"key": "name", "value": "", "description": "Filter by client name"}, {"key": "order_by", "value": "name", "description": "Order by field"}, {"key": "order_direction", "value": "asc", "description": "Order direction"}]}}, "response": []}