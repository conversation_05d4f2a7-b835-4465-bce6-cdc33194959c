{"name": "Update Department User", "request": {"method": "PUT", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"user_id\": 2,\n  \"department_id\": 2\n}"}, "url": {"raw": "{{URL}}/department_users/1", "host": ["{{URL}}"], "path": ["department_users", "1"]}}, "response": []}