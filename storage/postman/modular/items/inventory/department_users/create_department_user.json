{"name": "Create Department User", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"user_id\": 1,\n  \"department_id\": 1\n}"}, "url": {"raw": "{{URL}}/department_users", "host": ["{{URL}}"], "path": ["department_users"]}}, "response": []}