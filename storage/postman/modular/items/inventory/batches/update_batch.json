{"name": "Update Batch", "request": {"method": "PUT", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"batch_number\": \"BATCH-2024-001-UPDATED\",\n  \"name\": \"Updated Premium Construction Materials Batch\",\n  \"description\": \"Updated high-quality construction materials batch with extended warranty\",\n  \"quantity\": 750,\n  \"produced_at\": \"2024-01-20\",\n  \"expired_at\": \"2025-01-31\",\n  \"processed_at\": \"2024-02-01\",\n  \"is_processed_at_stock\": true\n}"}, "url": {"raw": "{{URL}}/batches/1", "host": ["{{URL}}"], "path": ["batches", "1"]}}, "response": []}