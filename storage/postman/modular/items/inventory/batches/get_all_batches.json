{"name": "Get All Batches", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/batches?page=1&per_page=15&order=created_at&by=desc", "host": ["{{URL}}"], "path": ["batches"], "query": [{"key": "page", "value": "1"}, {"key": "per_page", "value": "15"}, {"key": "order", "value": "created_at"}, {"key": "by", "value": "desc"}, {"key": "shop_id", "value": "1", "disabled": true}, {"key": "product_id", "value": "1", "disabled": true}, {"key": "batch_number", "value": "BATCH-2024", "disabled": true}, {"key": "is_processed_at_stock", "value": "false", "disabled": true}, {"key": "expired_after", "value": "2024-12-31", "disabled": true}, {"key": "produced_before", "value": "2024-01-01", "disabled": true}]}}, "response": []}