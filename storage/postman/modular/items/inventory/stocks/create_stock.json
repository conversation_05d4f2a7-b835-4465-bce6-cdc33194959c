{"name": "Create Stock", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"product_id\": 1,\n  \"quantity\": 100\n}"}, "url": {"raw": "{{URL}}/stocks", "host": ["{{URL}}"], "path": ["stocks"]}}, "response": []}