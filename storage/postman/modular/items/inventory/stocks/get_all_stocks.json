{"name": "Get All Stocks", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/stocks?product_id=&order_by=created_at&order_direction=desc", "host": ["{{URL}}"], "path": ["stocks"], "query": [{"key": "product_id", "value": "", "description": "Filter by product ID"}, {"key": "order_by", "value": "created_at", "description": "Order by field"}, {"key": "order_direction", "value": "desc", "description": "Order direction"}]}}, "response": []}