{"name": "Update Stock", "request": {"method": "PUT", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"product_id\": 1,\n  \"quantity\": 150\n}"}, "url": {"raw": "{{URL}}/stocks/1", "host": ["{{URL}}"], "path": ["stocks", "1"]}}, "response": []}