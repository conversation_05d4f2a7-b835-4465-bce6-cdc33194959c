{"name": "Update Shop", "request": {"method": "PUT", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Updated Shop Name\"\n}"}, "url": {"raw": "{{URL}}/shops/1", "host": ["{{URL}}"], "path": ["shops", "1"]}}, "response": []}