{"name": "Create Shop", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Shop Name\"\n}"}, "url": {"raw": "{{URL}}/shops", "host": ["{{URL}}"], "path": ["shops"]}}, "response": []}