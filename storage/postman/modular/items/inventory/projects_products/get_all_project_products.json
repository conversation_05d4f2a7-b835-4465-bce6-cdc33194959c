{"name": "Get All Project Products", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/projects_products?page=1&per_page=15&order=created_at&by=desc", "host": ["{{URL}}"], "path": ["projects_products"], "query": [{"key": "page", "value": "1"}, {"key": "per_page", "value": "15"}, {"key": "order", "value": "created_at"}, {"key": "by", "value": "desc"}, {"key": "project_id", "value": "1", "disabled": true}, {"key": "product_id", "value": "1", "disabled": true}, {"key": "min_quantity", "value": "5", "disabled": true}, {"key": "max_quantity", "value": "100", "disabled": true}, {"key": "min_value", "value": "50.00", "disabled": true}, {"key": "max_value", "value": "1000.00", "disabled": true}]}}, "response": []}