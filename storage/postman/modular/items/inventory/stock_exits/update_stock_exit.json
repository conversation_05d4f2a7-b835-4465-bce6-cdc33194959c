{"name": "Update Stock Exit", "request": {"method": "PUT", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"brand_id\": 2,\n  \"product_id\": 2,\n  \"batch_id\": 2,\n  \"client_id\": 2,\n  \"project_id\": 2,\n  \"quantity\": 75,\n  \"value\": 1125.50,\n  \"description\": \"Updated stock exit - Additional materials used in structural phase\"\n}"}, "url": {"raw": "{{URL}}/stock_exits/1", "host": ["{{URL}}"], "path": ["stock_exits", "1"]}}, "response": []}