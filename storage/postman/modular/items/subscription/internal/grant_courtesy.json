{"name": "<PERSON>", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"organization_id\": 1,\n  \"days\": 30,\n  \"reason\": \"Customer support courtesy extension\",\n  \"granted_by\": \"admin\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{URL}}/subscriptions/grant-courtesy", "host": ["{{URL}}"], "path": ["subscriptions", "grant-courtesy"]}}, "response": []}