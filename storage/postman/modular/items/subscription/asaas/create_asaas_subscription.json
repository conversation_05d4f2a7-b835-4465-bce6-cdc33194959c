{"name": "Create ASAAS Subscription", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"subscription_id\": 1,\n  \"customer\": \"cus_000005492485\",\n  \"billingType\": \"CREDIT_CARD\",\n  \"value\": 99.90,\n  \"nextDueDate\": \"2024-02-01\",\n  \"cycle\": \"MONTHLY\",\n  \"description\": \"Monthly subscription plan\",\n  \"endDate\": \"2024-12-31\",\n  \"maxPayments\": 12\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{URL}}/asaas/subscription/create-asaas-subscription", "host": ["{{URL}}"], "path": ["asaas", "subscription", "create-asaas-subscription"]}}, "response": []}