{"name": "Create Client Customer", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"client_id\": 1,\n  \"name\": \"<PERSON>\",\n  \"email\": \"<EMAIL>\",\n  \"cpfCnpj\": \"12345678901\",\n  \"phone\": \"11999999999\",\n  \"mobilePhone\": \"11999999999\",\n  \"address\": \"<PERSON>ua Example, 123\",\n  \"addressNumber\": \"123\",\n  \"complement\": \"Apt 1\",\n  \"province\": \"Centro\",\n  \"postalCode\": \"01234567\",\n  \"externalReference\": \"CLIENT_001\",\n  \"notificationDisabled\": false,\n  \"additionalEmails\": \"<EMAIL>\",\n  \"municipalInscription\": \"123456\",\n  \"stateInscription\": \"ISENTO\",\n  \"observations\": \"VIP Customer\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{URL}}/asaas/client/create-customer", "host": ["{{URL}}"], "path": ["asaas", "client", "create-customer"]}}, "response": []}