{"name": "Update ASAAS Sale", "request": {"method": "PUT", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}"}], "body": {"mode": "raw", "raw": "{\n    \"status\": \"RECEIVED\",\n    \"payment_date\": \"2024-08-18\",\n    \"client_payment_date\": \"2024-08-18\",\n    \"net_value\": 145.50,\n    \"sync_status\": \"synced\",\n    \"description\": \"Pagamento de serviços de consultoria - Recebido\"\n}"}, "url": {"raw": "{{URL}}/asaas-resources/sales/1", "host": ["{{URL}}"], "path": ["asaas-resources", "sales", "1"]}, "description": "Update an existing ASAAS sale record"}, "response": []}