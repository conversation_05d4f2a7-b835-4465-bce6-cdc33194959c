{"name": "Create ASAAS Sale", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}"}], "body": {"mode": "raw", "raw": "{\n    \"sale_id\": 1,\n    \"organization_id\": 1,\n    \"client_id\": 1,\n    \"asaas_payment_id\": \"pay_123456789\",\n    \"asaas_customer_id\": \"cus_000005492183\",\n    \"value\": 150.00,\n    \"net_value\": 145.50,\n    \"original_value\": 150.00,\n    \"interest_value\": 0.00,\n    \"discount_value\": 4.50,\n    \"description\": \"Pagamento de serviços de consultoria\",\n    \"billing_type\": \"PIX\",\n    \"due_date\": \"2024-12-31\",\n    \"status\": \"PENDING\",\n    \"external_reference\": \"SALE_001_2024\",\n    \"installment_count\": 1,\n    \"installment_value\": 150.00,\n    \"installment_number\": 1,\n    \"anticipated\": false,\n    \"anticipable\": true,\n    \"can_be_paid_after_due_date\": true,\n    \"sync_status\": \"synced\",\n    \"discount_config\": {\n        \"value\": 4.50,\n        \"dueDateLimitDays\": 0,\n        \"type\": \"FIXED\"\n    },\n    \"fine_config\": {\n        \"value\": 2.00,\n        \"type\": \"FIXED\"\n    },\n    \"interest_config\": {\n        \"value\": 1.00,\n        \"type\": \"PERCENTAGE\"\n    }\n}"}, "url": {"raw": "{{URL}}/asaas-resources/sales", "host": ["{{URL}}"], "path": ["asaas-resources", "sales"]}, "description": "Create a new ASAAS sale record in the database"}, "response": []}