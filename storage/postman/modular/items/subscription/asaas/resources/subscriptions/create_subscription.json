{"name": "Create ASAAS Subscription", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}"}], "body": {"mode": "raw", "raw": "{\n    \"subscription_id\": 1,\n    \"asaas_customer_id\": \"cus_000005492183\",\n    \"asaas_subscription_id\": \"sub_123456789\",\n    \"billing_type\": \"CREDIT_CARD\",\n    \"cycle\": \"MONTHLY\",\n    \"value\": 99.90,\n    \"next_due_date\": \"2024-09-18\",\n    \"description\": \"Assinatura mensal do plano premium\",\n    \"status\": \"ACTIVE\",\n    \"max_payments\": 12,\n    \"external_reference\": \"SUBSCRIPTION_001\",\n    \"payment_link\": \"https://www.asaas.com/c/123456789\",\n    \"discount_value\": 9.99,\n    \"discount_type\": \"FIXED\",\n    \"discount_due_date_limit_days\": 5,\n    \"fine_value\": 5.00,\n    \"fine_type\": \"FIXED\",\n    \"interest_value\": 2.00,\n    \"credit_card_number\": \"**** **** **** 1234\",\n    \"credit_card_brand\": \"VISA\",\n    \"credit_card_token\": \"cc_token_123456789\",\n    \"sync_status\": \"synced\",\n    \"split_data\": {\n        \"walletId\": \"wallet_123456789\",\n        \"fixedValue\": 10.00,\n        \"percentualValue\": 5.0\n    }\n}"}, "url": {"raw": "{{URL}}/asaas-resources/subscriptions", "host": ["{{URL}}"], "path": ["asaas-resources", "subscriptions"]}, "description": "Create a new ASAAS subscription record in the database"}, "response": []}