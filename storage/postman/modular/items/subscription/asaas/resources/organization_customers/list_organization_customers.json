{"name": "List ASAAS Organization Customers", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}"}], "url": {"raw": "{{URL}}/asaas-resources/organization-customers", "host": ["{{URL}}"], "path": ["asaas-resources", "organization-customers"]}, "description": "Retrieve all ASAAS organization customers from the database"}, "response": []}