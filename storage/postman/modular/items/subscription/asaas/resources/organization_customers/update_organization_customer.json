{"name": "Update ASAAS Organization Customer", "request": {"method": "PUT", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"Cliente Organização Exemplo - Atualizado\",\n    \"email\": \"<EMAIL>\",\n    \"sync_status\": \"pending\",\n    \"notification_disabled\": true,\n    \"observations\": \"Cliente atualizado com novas informações de contato\"\n}"}, "url": {"raw": "{{URL}}/asaas-resources/organization-customers/1", "host": ["{{URL}}"], "path": ["asaas-resources", "organization-customers", "1"]}, "description": "Update an existing ASAAS organization customer record"}, "response": []}