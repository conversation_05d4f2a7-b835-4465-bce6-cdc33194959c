{"name": "Get ASAAS Organization Customer", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}"}], "url": {"raw": "{{URL}}/asaas-resources/organization-customers/1", "host": ["{{URL}}"], "path": ["asaas-resources", "organization-customers", "1"]}, "description": "Retrieve a specific ASAAS organization customer by ID"}, "response": []}