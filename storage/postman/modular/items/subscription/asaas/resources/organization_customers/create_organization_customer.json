{"name": "Create ASAAS Organization Customer", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}"}], "body": {"mode": "raw", "raw": "{\n    \"organization_id\": 1,\n    \"asaas_customer_id\": \"cus_000005492182\",\n    \"sync_status\": \"synced\",\n    \"name\": \"Cliente Organização Exemplo\",\n    \"email\": \"<EMAIL>\",\n    \"phone\": \"11987654321\",\n    \"mobile_phone\": \"11976543210\",\n    \"address\": \"Av. Paul<PERSON>, 1000\",\n    \"address_number\": \"1000\",\n    \"complement\": \"Conjunto 101\",\n    \"province\": \"Bela Vista\",\n    \"city_name\": \"São Paulo\",\n    \"state\": \"SP\",\n    \"country\": \"Brasil\",\n    \"postal_code\": \"01310-100\",\n    \"cpf_cnpj\": \"98.765.432/0001-10\",\n    \"person_type\": \"JURIDICA\",\n    \"external_reference\": \"ORG_CUSTOMER_001\",\n    \"notification_disabled\": false,\n    \"additional_emails\": \"<EMAIL>,<EMAIL>\",\n    \"observations\": \"Cliente corporativo com alto volume de transações\",\n    \"foreign_customer\": false\n}"}, "url": {"raw": "{{URL}}/asaas-resources/organization-customers", "host": ["{{URL}}"], "path": ["asaas-resources", "organization-customers"]}, "description": "Create a new ASAAS organization customer record in the database"}, "response": []}