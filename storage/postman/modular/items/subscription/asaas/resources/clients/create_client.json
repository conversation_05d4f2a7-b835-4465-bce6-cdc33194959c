{"name": "Create ASAAS Client", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}"}], "body": {"mode": "raw", "raw": "{\n    \"client_id\": 1,\n    \"organization_id\": 1,\n    \"asaas_customer_id\": \"cus_000005492183\",\n    \"sync_status\": \"synced\",\n    \"name\": \"<PERSON>\",\n    \"email\": \"<EMAIL>\",\n    \"phone\": \"11912345678\",\n    \"mobile_phone\": \"11987654321\",\n    \"address\": \"Rua das Palmeiras, 456\",\n    \"address_number\": \"456\",\n    \"complement\": \"Apto 789\",\n    \"province\": \"Vila Madalena\",\n    \"city_name\": \"São Paulo\",\n    \"state\": \"SP\",\n    \"country\": \"Brasil\",\n    \"postal_code\": \"05435-010\",\n    \"cpf_cnpj\": \"123.456.789-00\",\n    \"person_type\": \"FISICA\",\n    \"external_reference\": \"CLIENT_001\",\n    \"notification_disabled\": false,\n    \"additional_emails\": \"<EMAIL>\",\n    \"observations\": \"Cliente pessoa física com histórico de pagamentos em dia\",\n    \"foreign_customer\": false\n}"}, "url": {"raw": "{{URL}}/asaas-resources/clients", "host": ["{{URL}}"], "path": ["asaas-resources", "clients"]}, "description": "Create a new ASAAS client record in the database"}, "response": []}