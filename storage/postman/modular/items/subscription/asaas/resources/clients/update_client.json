{"name": "Update ASAAS Client", "request": {"method": "PUT", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"<PERSON>ualiza<PERSON>\",\n    \"email\": \"<EMAIL>\",\n    \"sync_status\": \"pending\",\n    \"mobile_phone\": \"11999887766\",\n    \"observations\": \"Cliente atualizado com novo telefone e email\"\n}"}, "url": {"raw": "{{URL}}/asaas-resources/clients/1", "host": ["{{URL}}"], "path": ["asaas-resources", "clients", "1"]}, "description": "Update an existing ASAAS client record"}, "response": []}