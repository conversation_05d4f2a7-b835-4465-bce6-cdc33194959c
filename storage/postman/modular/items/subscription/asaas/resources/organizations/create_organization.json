{"name": "Create ASAAS Organization", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}"}], "body": {"mode": "raw", "raw": "{\n    \"organization_id\": 1,\n    \"asaas_account_id\": \"acc_123456789\",\n    \"asaas_api_key\": \"$aact_YTU5YTE0M2M2N2I4MTliNzk0YTI5N2U5MzdjNWZmNDQ6OjAwMDAwMDAwMDAwMDAwNzI2NzM6OiRhYWNoXzRlNTEzYWY3LWVmMTItNGE5Yy1iMDMyLTM5ZGRkOGVkNjE4OA==\",\n    \"asaas_wallet_id\": \"wallet_123456789\",\n    \"asaas_environment\": \"sandbox\",\n    \"is_active\": true,\n    \"name\": \"Empresa Exemplo LTDA\",\n    \"email\": \"<EMAIL>\",\n    \"login_email\": \"<EMAIL>\",\n    \"phone\": \"***********\",\n    \"mobile_phone\": \"***********\",\n    \"address\": \"Rua das Flores, 123\",\n    \"address_number\": \"123\",\n    \"complement\": \"Sala 456\",\n    \"province\": \"Centro\",\n    \"postal_code\": \"01234-567\",\n    \"cpf_cnpj\": \"12.345.678/0001-90\",\n    \"person_type\": \"JURIDICA\",\n    \"company_type\": \"LTDA\",\n    \"city\": 3550308,\n    \"state\": \"SP\",\n    \"country\": \"Brasil\",\n    \"trading_name\": \"Empresa Exemplo\",\n    \"income_value\": 50000.00,\n    \"site\": \"https://www.empresaexemplo.com.br\"\n}"}, "url": {"raw": "{{URL}}/asaas-resources/organizations", "host": ["{{URL}}"], "path": ["asaas-resources", "organizations"]}, "description": "Create a new ASAAS organization record in the database"}, "response": []}