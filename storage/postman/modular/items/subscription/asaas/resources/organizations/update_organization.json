{"name": "Update ASAAS Organization", "request": {"method": "PUT", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"Empresa Exemplo LTDA - Atualizada\",\n    \"email\": \"<EMAIL>\",\n    \"is_active\": false,\n    \"asaas_environment\": \"production\",\n    \"income_value\": 75000.00\n}"}, "url": {"raw": "{{URL}}/asaas-resources/organizations/1", "host": ["{{URL}}"], "path": ["asaas-resources", "organizations", "1"]}, "description": "Update an existing ASAAS organization record"}, "response": []}