{"name": "Update Payment", "request": {"method": "PUT", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"value\": 150.00,\n  \"dueDate\": \"2024-12-31\",\n  \"description\": \"Pagamento de teste atualizado\",\n  \"externalReference\": \"REF123456-UPDATED\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{URL}}/asaas/payment/pay_123456789", "host": ["{{URL}}"], "path": ["asaas", "payment", "pay_123456789"]}}, "response": []}