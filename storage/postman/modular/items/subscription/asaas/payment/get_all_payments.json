{"name": "Get All Payments", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/asaas/payments?limit=20&offset=0&customer=cus_000005492182", "host": ["{{URL}}"], "path": ["asaas", "payments"], "query": [{"key": "limit", "value": "20"}, {"key": "offset", "value": "0"}, {"key": "customer", "value": "cus_000005492182"}]}}, "response": []}