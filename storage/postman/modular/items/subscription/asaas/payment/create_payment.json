{"name": "Create Payment", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"customer\": \"cus_000005492182\",\n  \"billingType\": \"BOLETO\",\n  \"value\": 285.50,\n  \"dueDate\": \"2024-12-31\",\n  \"description\": \"Pagamento de materiais de construção - Pedido #1234\",\n  \"externalReference\": \"VENDA-CONSTRUCAO-1234\",\n  \"installmentCount\": 1,\n  \"installmentValue\": 285.50,\n  \"discount\": {\n    \"value\": 15.00,\n    \"type\": \"FIXED\",\n    \"dueDateLimitDays\": 5\n  },\n  \"fine\": {\n    \"value\": 2.5,\n    \"type\": \"PERCENTAGE\"\n  },\n  \"interest\": {\n    \"value\": 1.0,\n    \"type\": \"PERCENTAGE\"\n  },\n  \"postalService\": false,\n  \"split\": []\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{URL}}/asaas/payment", "host": ["{{URL}}"], "path": ["asaas", "payment"]}}, "response": []}