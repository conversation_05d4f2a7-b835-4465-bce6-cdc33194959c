{"name": "Update Customer", "request": {"method": "PUT", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"<PERSON>\",\n  \"email\": \"<EMAIL>\",\n  \"phone\": \"11999999999\",\n  \"mobilePhone\": \"11888888888\",\n  \"address\": \"Rua das Flores Updated\",\n  \"addressNumber\": \"456\",\n  \"complement\": \"Apto 78\",\n  \"province\": \"Centro\",\n  \"city\": \"São Paulo\",\n  \"state\": \"SP\",\n  \"postalCode\": \"01234-567\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{URL}}/asaas/customer/cus_000005492182", "host": ["{{URL}}"], "path": ["asaas", "customer", "cus_000005492182"]}}, "response": []}