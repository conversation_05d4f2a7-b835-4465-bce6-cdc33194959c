{"name": "Search Customer by Email", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/asaas/customer/search/email?email=<EMAIL>", "host": ["{{URL}}"], "path": ["asaas", "customer", "search", "email"], "query": [{"key": "email", "value": "<EMAIL>"}]}}, "response": []}