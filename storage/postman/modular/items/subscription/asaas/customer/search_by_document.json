{"name": "Search Customer by Document", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/asaas/customer/search/document?cpf_cnpj=12345678901", "host": ["{{URL}}"], "path": ["asaas", "customer", "search", "document"], "query": [{"key": "cpf_cnpj", "value": "12345678901"}]}}, "response": []}