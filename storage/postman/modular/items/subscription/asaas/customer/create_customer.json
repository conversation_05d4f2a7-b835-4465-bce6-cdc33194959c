{"name": "Create Customer", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"<PERSON>\",\n  \"email\": \"<EMAIL>\",\n  \"phone\": \"11999999999\",\n  \"mobilePhone\": \"11888888888\",\n  \"cpfCnpj\": \"12345678901\",\n  \"personType\": \"FISICA\",\n  \"address\": \"Rua das Flores\",\n  \"addressNumber\": \"123\",\n  \"complement\": \"Apto 45\",\n  \"province\": \"Centro\",\n  \"city\": \"São Paulo\",\n  \"state\": \"SP\",\n  \"postalCode\": \"01234-567\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{URL}}/asaas/customer", "host": ["{{URL}}"], "path": ["asaas", "customer"]}}, "response": []}