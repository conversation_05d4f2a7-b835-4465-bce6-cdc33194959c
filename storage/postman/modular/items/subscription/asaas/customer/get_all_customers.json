{"name": "Get All Customers", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/asaas/customers?limit=20&offset=0", "host": ["{{URL}}"], "path": ["asaas", "customers"], "query": [{"key": "limit", "value": "20"}, {"key": "offset", "value": "0"}]}}, "response": []}