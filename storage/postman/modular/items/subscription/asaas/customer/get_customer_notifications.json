{"name": "Get Customer Notifications", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/asaas/customer/cus_000005492182/notifications?limit=20&offset=0", "host": ["{{URL}}"], "path": ["asaas", "customer", "cus_000005492182", "notifications"], "query": [{"key": "limit", "value": "20"}, {"key": "offset", "value": "0"}]}}, "response": []}