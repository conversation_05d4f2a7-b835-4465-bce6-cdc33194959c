{"name": "Get Subaccount", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/asaas/account/subaccount?organization_id=1", "host": ["{{URL}}"], "path": ["asaas", "account", "subaccount"], "query": [{"key": "organization_id", "value": "1"}]}}, "response": []}