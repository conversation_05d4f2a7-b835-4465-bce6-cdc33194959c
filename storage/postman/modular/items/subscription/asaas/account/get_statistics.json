{"name": "Get Statistics", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/asaas/account/statistics?dateFrom=2024-01-01&dateTo=2024-12-31", "host": ["{{URL}}"], "path": ["asaas", "account", "statistics"], "query": [{"key": "dateFrom", "value": "2024-01-01"}, {"key": "dateTo", "value": "2024-12-31"}]}}, "response": []}