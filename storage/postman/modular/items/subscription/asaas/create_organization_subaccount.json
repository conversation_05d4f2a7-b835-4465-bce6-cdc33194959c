{"name": "Create Organization Subaccount", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"organization_id\": 1,\n  \"name\": \"Organization Subaccount\",\n  \"email\": \"<EMAIL>\",\n  \"cpfCnpj\": \"***********\",\n  \"phone\": \"***********\",\n  \"mobilePhone\": \"***********\",\n  \"address\": \"<PERSON>ua Example, 123\",\n  \"addressNumber\": \"123\",\n  \"complement\": \"Sala 1\",\n  \"province\": \"Centro\",\n  \"postalCode\": \"********\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{URL}}/asaas/organization/create-subaccount", "host": ["{{URL}}"], "path": ["asaas", "organization", "create-subaccount"]}}, "response": []}