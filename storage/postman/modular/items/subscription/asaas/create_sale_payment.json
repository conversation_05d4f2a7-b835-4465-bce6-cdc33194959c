{"name": "Create Sale Payment", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"sale_id\": 1,\n  \"customer\": \"cus_000005492485\",\n  \"billingType\": \"CREDIT_CARD\",\n  \"value\": 299.90,\n  \"dueDate\": \"2024-02-01\",\n  \"description\": \"Product sale payment\",\n  \"externalReference\": \"SALE_001\",\n  \"installmentCount\": 1,\n  \"installmentValue\": 299.90,\n  \"discount\": {\n    \"value\": 10.00,\n    \"dueDateLimitDays\": 5\n  },\n  \"interest\": {\n    \"value\": 2.00\n  },\n  \"fine\": {\n    \"value\": 1.00\n  },\n  \"postalService\": false\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{URL}}/asaas/sale/create-payment", "host": ["{{URL}}"], "path": ["asaas", "sale", "create-payment"]}}, "response": []}