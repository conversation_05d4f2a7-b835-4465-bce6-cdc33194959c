{"name": "Create Subscription", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"customer\": \"cus_000005492182\",\n  \"billingType\": \"CREDIT_CARD\",\n  \"value\": 50.00,\n  \"nextDueDate\": \"2024-01-31\",\n  \"cycle\": \"MONTHLY\",\n  \"description\": \"Assinatura mensal\",\n  \"endDate\": \"2024-12-31\",\n  \"maxPayments\": 12\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{URL}}/asaas/subscription", "host": ["{{URL}}"], "path": ["asaas", "subscription"]}}, "response": []}