{"name": "Get Subscription Payments", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/asaas/subscription/sub_123456789/payments?limit=20&offset=0", "host": ["{{URL}}"], "path": ["asaas", "subscription", "sub_123456789", "payments"], "query": [{"key": "limit", "value": "20"}, {"key": "offset", "value": "0"}]}}, "response": []}