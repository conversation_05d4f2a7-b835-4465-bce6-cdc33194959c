{"name": "🌍 Webhook Verify (Global Token)", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "url": {"raw": "{{URL}}/api/whatsapp/webhook?hub_mode=subscribe&hub_challenge=9876543210&hub_verify_token={{global_webhook_token}}", "host": ["{{URL}}"], "path": ["api", "whatsapp", "webhook"], "query": [{"key": "hub_mode", "value": "subscribe", "description": "Webhook verification mode - must be 'subscribe'"}, {"key": "hub_challenge", "value": "9876543210", "description": "Challenge token from Meta - will be returned if verification succeeds"}, {"key": "hub_verify_token", "value": "{{global_webhook_token}}", "description": "Global webhook verify token (fallback when no organization token matches)"}]}, "description": "**WhatsApp Webhook Verification with Global Token**\n\nThis demonstrates webhook verification using the global fallback token when no organization-specific token matches.\n\n**Use Cases:**\n- 🔄 Fallback when organization token not found\n- 🏢 Multi-tenant setup with shared webhook\n- 🧪 Testing webhook verification\n\n**Token Resolution Order:**\n1. ❌ No organization token matches\n2. ✅ Global token used as fallback\n\n**Variables needed:**\n- `{{global_webhook_token}}`: Set to your global webhook verify token (from config)\n- `{{URL}}`: Your API base URL\n\n**Expected Response:**\n- Success: Returns challenge as integer (e.g., 9876543210)\n- Logs created with type: 'global'\n\n**Configuration:**\nSet in your .env file:\n```\nWHATSAPP_WEBHOOK_VERIFY_TOKEN=your_global_token_here\n```"}, "response": []}