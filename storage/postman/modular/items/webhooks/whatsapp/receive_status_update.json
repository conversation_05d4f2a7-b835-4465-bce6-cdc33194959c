{"name": "📊 Receive Status Update", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Accept", "value": "application/json", "type": "text"}, {"key": "X-Hub-Signature-256", "value": "{{webhook_signature}}", "type": "text", "description": "HMAC SHA-256 signature for webhook security validation"}], "body": {"mode": "raw", "raw": "{\n    \"object\": \"whatsapp_business_account\",\n    \"entry\": [\n        {\n            \"id\": \"***************\",\n            \"changes\": [\n                {\n                    \"field\": \"messages\",\n                    \"value\": {\n                        \"messaging_product\": \"whatsapp\",\n                        \"metadata\": {\n                            \"display_phone_number\": \"+55 11 99999-9999\",\n                            \"phone_number_id\": \"{{phone_number_id}}\"\n                        },\n                        \"statuses\": [\n                            {\n                                \"id\": \"wamid.************************************************************************\",\n                                \"status\": \"delivered\",\n                                \"timestamp\": \"**********\",\n                                \"recipient_id\": \"*************\",\n                                \"conversation\": {\n                                    \"id\": \"CONVERSATION_ID\",\n                                    \"expiration_timestamp\": \"1699209856\",\n                                    \"origin\": {\n                                        \"type\": \"business_initiated\"\n                                    }\n                                },\n                                \"pricing\": {\n                                    \"billable\": true,\n                                    \"pricing_model\": \"CBP\",\n                                    \"category\": \"business_initiated\"\n                                }\n                            },\n                            {\n                                \"id\": \"wamid.************************************************************************\",\n                                \"status\": \"read\",\n                                \"timestamp\": \"1699123500\",\n                                \"recipient_id\": \"*************\"\n                            }\n                        ]\n                    }\n                }\n            ]\n        }\n    ]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{URL}}/api/whatsapp/webhook", "host": ["{{URL}}"], "path": ["api", "whatsapp", "webhook"]}, "description": "**WhatsApp Status Update Webhook**\n\nThis endpoint receives status updates for messages sent through WhatsApp Business API. Status updates inform you about message delivery, read receipts, and failures.\n\n**Status Types:**\n- 📤 `sent`: Message sent to WhatsApp servers\n- 📬 `delivered`: Message delivered to recipient's device\n- 👁️ `read`: Message read by recipient\n- ❌ `failed`: Message delivery failed\n\n**Processing Features:**\n- 🔐 Signature validation for security\n- 🏢 Organization identification via phone_number_id\n- 📊 Status summary generation\n- 📝 Comprehensive logging\n- 🔄 Integration with existing ProcessWebhookEntry service\n\n**Status Information Includes:**\n- Message ID and recipient\n- Timestamp of status change\n- Conversation details (if applicable)\n- Pricing information (if applicable)\n- Error details (for failed messages)\n\n**Variables needed:**\n- `{{webhook_signature}}`: HMAC SHA-256 signature\n- `{{phone_number_id}}`: Your WhatsApp Business phone number ID\n- `{{URL}}`: Your API base URL\n\n**Expected Response:**\n```json\n{\n    \"status\": \"success\",\n    \"processed\": 1,\n    \"results\": [\n        {\n            \"success\": true,\n            \"type\": \"status\",\n            \"processed\": 2,\n            \"organization_id\": 1,\n            \"phone_number_id\": \"123456789\",\n            \"statuses_processed\": {\n                \"total\": 2,\n                \"by_status\": {\n                    \"delivered\": 1,\n                    \"read\": 1\n                },\n                \"message_ids\": [\"msg1\", \"msg2\"]\n            }\n        }\n    ]\n}\n```"}, "response": []}