{"name": "🚫 Test Invalid Signature", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Accept", "value": "application/json", "type": "text"}, {"key": "X-Hub-Signature-256", "value": "sha256=invalid_signature_for_testing_security_validation", "type": "text", "description": "Invalid signature to test security validation"}], "body": {"mode": "raw", "raw": "{\n    \"object\": \"whatsapp_business_account\",\n    \"entry\": [\n        {\n            \"id\": \"***************\",\n            \"changes\": [\n                {\n                    \"field\": \"messages\",\n                    \"value\": {\n                        \"messaging_product\": \"whatsapp\",\n                        \"metadata\": {\n                            \"display_phone_number\": \"+55 11 99999-9999\",\n                            \"phone_number_id\": \"{{phone_number_id}}\"\n                        },\n                        \"messages\": [\n                            {\n                                \"from\": \"*************\",\n                                \"id\": \"wamid.test_security\",\n                                \"timestamp\": \"**********\",\n                                \"type\": \"text\",\n                                \"text\": {\n                                    \"body\": \"This message should be rejected due to invalid signature\"\n                                }\n                            }\n                        ]\n                    }\n                }\n            ]\n        }\n    ]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{URL}}/api/whatsapp/webhook", "host": ["{{URL}}"], "path": ["api", "whatsapp", "webhook"]}, "description": "**Security Test: Invalid Signature**\n\nThis request tests the webhook security validation by sending an invalid HMAC SHA-256 signature. The endpoint should reject this request with a 403 Forbidden response.\n\n**Security Features Tested:**\n- 🔐 HMAC SHA-256 signature validation\n- 🛡️ Timing-safe comparison\n- 📝 Security event logging\n- 🚫 Request rejection for invalid signatures\n\n**Expected Behavior:**\n1. ❌ Signature validation fails\n2. 🚫 Request rejected with 403 status\n3. 📝 Security log created with masked signature\n4. 🔒 No message processing occurs\n\n**Expected Response:**\n```json\n{\n    \"error\": \"Forbidden\"\n}\n```\n\n**Security Logging:**\nA log entry will be created with:\n- `event_type`: 'security'\n- `processing_status`: 'failed'\n- `error_message`: 'Invalid signature'\n- `webhook_payload.signature`: 'sha256=invalid_signa...' (masked)\n\n**Variables needed:**\n- `{{phone_number_id}}`: Your WhatsApp Business phone number ID\n- `{{URL}}`: Your API base URL\n\n**Use Cases:**\n- 🧪 Testing webhook security\n- 🔍 Validating signature validation logic\n- 📊 Monitoring security attempts\n- 🛡️ Ensuring unauthorized requests are blocked"}, "response": []}