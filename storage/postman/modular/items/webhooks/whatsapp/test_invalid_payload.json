{"name": "🔧 Test Invalid Payload", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Accept", "value": "application/json", "type": "text"}, {"key": "X-Hub-Signature-256", "value": "{{webhook_signature}}", "type": "text", "description": "Valid signature for invalid payload (to test payload validation)"}], "body": {"mode": "raw", "raw": "{\n    \"invalid\": \"payload\",\n    \"missing\": {\n        \"object\": \"field\",\n        \"entry\": \"array\"\n    },\n    \"test\": \"This payload has valid signature but invalid structure\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{URL}}/api/whatsapp/webhook", "host": ["{{URL}}"], "path": ["api", "whatsapp", "webhook"]}, "description": "**Payload Validation Test: Invalid Structure**\n\nThis request tests the webhook payload validation by sending a structurally invalid payload with a valid signature. This demonstrates that signature validation occurs before payload validation.\n\n**Validation Order:**\n1. ✅ Signature validation (passes)\n2. ❌ Payload structure validation (fails)\n\n**Features Tested:**\n- 🔐 Signature validation priority\n- 🔧 Payload structure validation\n- 📝 Error logging for invalid payloads\n- 🚫 Request rejection for malformed data\n\n**Invalid Payload Structure:**\n- ❌ Missing `object` field\n- ❌ Missing `entry` array\n- ❌ Invalid overall structure\n\n**Expected Behavior:**\n1. ✅ Signature validation passes\n2. ❌ Payload validation fails\n3. 🚫 Request rejected with 400 status\n4. 📝 Error log created with 'other' event type\n\n**Expected Response:**\n```json\n{\n    \"error\": \"Invalid webhook data\"\n}\n```\n\n**Error Logging:**\nA log entry will be created with:\n- `event_type`: 'other'\n- `processing_status`: 'failed'\n- `error_message`: 'Invalid webhook data'\n- `webhook_payload`: Contains the invalid payload structure\n\n**Variables needed:**\n- `{{webhook_signature}}`: Valid HMAC SHA-256 signature for this payload\n- `{{URL}}`: Your API base URL\n\n**Use Cases:**\n- 🧪 Testing payload validation logic\n- 🔍 Validating error handling\n- 📊 Monitoring malformed webhook attempts\n- 🛡️ Ensuring data integrity requirements\n\n**Note:**\nGenerate the signature using your webhook secret and this exact payload to ensure signature validation passes while payload validation fails."}, "response": []}