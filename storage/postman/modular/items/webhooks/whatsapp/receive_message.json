{"name": "📨 Receive Message (with Signature)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Accept", "value": "application/json", "type": "text"}, {"key": "X-Hub-Signature-256", "value": "{{webhook_signature}}", "type": "text", "description": "HMAC SHA-256 signature for webhook security validation"}], "body": {"mode": "raw", "raw": "{\n    \"object\": \"whatsapp_business_account\",\n    \"entry\": [\n        {\n            \"id\": \"***************\",\n            \"changes\": [\n                {\n                    \"field\": \"messages\",\n                    \"value\": {\n                        \"messaging_product\": \"whatsapp\",\n                        \"metadata\": {\n                            \"display_phone_number\": \"+55 11 99999-9999\",\n                            \"phone_number_id\": \"{{phone_number_id}}\"\n                        },\n                        \"contacts\": [\n                            {\n                                \"profile\": {\n                                    \"name\": \"<PERSON>\"\n                                },\n                                \"wa_id\": \"*************\"\n                            }\n                        ],\n                        \"messages\": [\n                            {\n                                \"from\": \"*************\",\n                                \"id\": \"wamid.************************************************************************\",\n                                \"timestamp\": \"1699123456\",\n                                \"type\": \"text\",\n                                \"text\": {\n                                    \"body\": \"Olá! Como posso ajudar você hoje?\"\n                                }\n                            }\n                        ]\n                    }\n                }\n            ]\n        }\n    ]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{URL}}/api/whatsapp/webhook", "host": ["{{URL}}"], "path": ["api", "whatsapp", "webhook"]}, "description": "**WhatsApp Webhook Message Receiver**\n\nThis endpoint receives incoming messages from Meta's WhatsApp Business API. It includes comprehensive security validation and intelligent message processing.\n\n**Security Features:**\n- 🔐 HMAC SHA-256 signature validation (X-Hub-Signature-256)\n- 🏢 Organization identification via phone_number_id\n- 📝 Comprehensive logging of all webhook events\n- 🛡️ Payload structure validation\n\n**Processing Features:**\n- 📱 Automatic organization detection\n- 🤖 ChatBot integration for automated responses\n- 📊 Message and status update processing\n- 🔄 Outgoing message filtering\n\n**Required Headers:**\n- `Content-Type: application/json`\n- `X-Hub-Signature-256: sha256=<hmac_signature>`\n\n**Variables needed:**\n- `{{webhook_signature}}`: HMAC SHA-256 signature (sha256=...)\n- `{{phone_number_id}}`: Your WhatsApp Business phone number ID\n- `{{URL}}`: Your API base URL\n\n**Signature Generation:**\nThe signature is generated using your webhook secret:\n```\nsha256=HMAC_SHA256(request_body, webhook_secret)\n```\n\n**Expected Response:**\n- Success: `{\"status\": \"success\", \"processed\": 1, \"results\": [...]}`\n- Invalid signature: `403 {\"error\": \"Forbidden\"}`\n- Invalid payload: `400 {\"error\": \"Invalid webhook data\"}`"}, "response": []}