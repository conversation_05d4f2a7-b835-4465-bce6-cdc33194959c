{"name": "🔐 Webhook Verify (Organization Token)", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "url": {"raw": "{{URL}}/api/whatsapp/webhook?hub_mode=subscribe&hub_challenge=1234567890&hub_verify_token={{organization_webhook_token}}", "host": ["{{URL}}"], "path": ["api", "whatsapp", "webhook"], "query": [{"key": "hub_mode", "value": "subscribe", "description": "Webhook verification mode - must be 'subscribe'"}, {"key": "hub_challenge", "value": "1234567890", "description": "Challenge token from Meta - will be returned if verification succeeds"}, {"key": "hub_verify_token", "value": "{{organization_webhook_token}}", "description": "Organization-specific webhook verify token (priority over global token)"}]}, "description": "**WhatsApp Webhook Verification Endpoint**\n\nThis endpoint is called by Meta to verify your webhook URL during setup. It supports both organization-specific tokens and global fallback tokens.\n\n**Features:**\n- ✅ Organization-specific token verification (priority)\n- ✅ Global token fallback\n- ✅ Comprehensive logging of verification attempts\n- ✅ Security validation\n\n**How it works:**\n1. Meta sends GET request with hub_mode, hub_challenge, and hub_verify_token\n2. System validates the token (organization first, then global)\n3. If valid, returns the hub_challenge as integer\n4. If invalid, returns 403 Forbidden\n\n**Token Priority:**\n1. Organization-specific token (whatsapp_webhook_verify_token)\n2. Global token (config: whatsapp.webhook_verify_token)\n\n**Variables needed:**\n- `{{organization_webhook_token}}`: Set to your organization's webhook verify token\n- `{{URL}}`: Your API base URL\n\n**Expected Response:**\n- Success: Returns challenge as integer (e.g., 1234567890)\n- Failure: 403 with {\"error\": \"Forbidden\"}"}, "response": []}