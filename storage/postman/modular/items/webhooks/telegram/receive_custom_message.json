{"name": "Receive Custom Message", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Accept", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"update_id\": 123456789,\n    \"message\": {\n        \"message_id\": 1,\n        \"from\": {\n            \"id\": 123456789,\n            \"is_bot\": false,\n            \"first_name\": \"<PERSON>\",\n            \"username\": \"johndoe\"\n        },\n        \"chat\": {\n            \"id\": 123456789,\n            \"first_name\": \"<PERSON>\",\n            \"username\": \"johndoe\",\n            \"type\": \"private\"\n        },\n        \"date\": 1234567890,\n        \"text\": \"Hello Custom Bot!\"\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{URL}}/telegram/{{bot_id}}/receive", "host": ["{{URL}}"], "path": ["telegram", "{{bot_id}}", "receive"], "variable": [{"key": "bot_id", "value": "1", "description": "The ID of the bot"}]}}, "response": []}