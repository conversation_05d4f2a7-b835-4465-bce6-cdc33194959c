{"name": "Read Single Notification", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{URL}}/notifications/read-one/1", "host": ["{{URL}}"], "path": ["notifications", "read-one", "1"]}}, "response": []}