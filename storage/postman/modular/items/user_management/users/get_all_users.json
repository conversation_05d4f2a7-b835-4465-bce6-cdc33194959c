{"name": "Get All Users", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/users?name=&email=&order_by=created_at&order_direction=desc", "host": ["{{URL}}"], "path": ["users"], "query": [{"key": "name", "value": "", "description": "Filter by first_name or last_name"}, {"key": "email", "value": "", "description": "Filter by email"}, {"key": "order_by", "value": "created_at", "description": "Order by field"}, {"key": "order_direction", "value": "desc", "description": "Order direction"}]}}, "response": []}