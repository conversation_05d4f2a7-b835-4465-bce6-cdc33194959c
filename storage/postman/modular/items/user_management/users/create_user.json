{"name": "Create User", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"profile_id\": 1,\n  \"first_name\": \"<PERSON>\",\n  \"last_name\": \"<PERSON><PERSON>\",\n  \"username\": \"joh<PERSON><PERSON>\",\n  \"password\": \"password123\",\n  \"password_confirmation\": \"password123\"\n}"}, "url": {"raw": "{{URL}}/users", "host": ["{{URL}}"], "path": ["users"]}}, "response": []}