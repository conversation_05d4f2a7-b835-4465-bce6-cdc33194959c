{"name": "Update User", "request": {"method": "PUT", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"first_name\": \"John Updated\",\n  \"last_name\": \"<PERSON><PERSON> Updated\",\n  \"username\": \"joh<PERSON><PERSON>_updated\",\n  \"password\": \"newpassword123\",\n  \"password_confirmation\": \"newpassword123\"\n}"}, "url": {"raw": "{{URL}}/users/1", "host": ["{{URL}}"], "path": ["users", "1"]}}, "response": []}