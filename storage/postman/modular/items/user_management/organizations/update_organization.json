{"name": "Update Organization", "request": {"method": "PUT", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"<PERSON><PERSON><PERSON>\",\n    \"description\": \"\",\n    \"is_active\": true,\n    \"is_suspended\": false,\n    \"default_flow_id\": 3,\n    \"email\": \"<EMAIL>\",\n    \"cpf_cnpj\": \"05.100.274/0001-76\",\n    \"company_type\": \"MEI\",\n    \"phone\": \"1133344455\",\n    \"mobile_phone\": \"11987654321\",\n    \"address\": \"Rua das Flores\",\n    \"address_number\": \"123\",\n    \"complement\": \"Sala 101\",\n    \"province\": \"Centro\",\n    \"city\": \"São Paulo\",\n    \"state\": \"SP\",\n    \"postal_code\": \"01001000\",\n    \"birth_date\": \"1980-05-15\",\n    \"monthly_revenue\": 2500.75,\n    \"income_value\": 5000.5,\n    \"whatsapp_webhook_verify_token\": \"lndjw9or4uitfgzhCMNn\",\n    \"whatsapp_webhook_secret\": \"408e56185dfd3b7cf7482f0825809dbe\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{URL}}/organizations/1", "host": ["{{URL}}"], "path": ["organizations", "1"]}}, "response": []}