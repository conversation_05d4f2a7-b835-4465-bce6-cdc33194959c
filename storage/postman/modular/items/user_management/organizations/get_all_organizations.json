{"name": "Get All Organizations", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/organizations?order_by=name&order_direction=asc", "host": ["{{URL}}"], "path": ["organizations"], "query": [{"key": "order_by", "value": "name", "description": "Order by field"}, {"key": "order_direction", "value": "asc", "description": "Order direction"}]}}, "response": []}