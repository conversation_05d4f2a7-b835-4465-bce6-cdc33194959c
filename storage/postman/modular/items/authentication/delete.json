{"name": "Delete", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"method": "DELETE", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"confirmation\": \"1\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{URL}}/user/delete", "host": ["{{URL}}"], "path": ["user", "delete"]}}, "response": []}