{"name": "<PERSON><PERSON>", "event": [{"listen": "test", "script": {"exec": ["var jsonData = JSON.parse(responseBody);\r", "if(jsonData.status === \"success\"){\r", "    postman.setEnvironmentVariable(\"TOKEN\", jsonData.data.token);\r", "}"], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Accept", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"email\": \"<EMAIL>\",\n    \"password\": \"password123\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{URL}}/login", "host": ["{{URL}}"], "path": ["login"]}}, "response": []}