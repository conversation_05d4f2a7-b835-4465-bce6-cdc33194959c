{"name": "🔐 Authentication", "description": "Complete authentication system with user registration, login, password management, and account operations. Includes secure token-based authentication using Laravel Sanctum, password reset functionality with email verification, multi-session management, and account deletion with confirmation. Essential for user access control, security, and session management across the application."}