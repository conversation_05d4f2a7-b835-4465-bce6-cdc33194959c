{"id": "whatsapp-realtime-env", "name": "WhatsApp Real-Time Environment", "values": [{"key": "base_url", "value": "http://localhost:8000", "description": "Base URL for the API", "type": "default", "enabled": true}, {"key": "auth_token", "value": "", "description": "Bearer token for authentication. Get this from login endpoint or user dashboard.", "type": "secret", "enabled": true}, {"key": "client_id", "value": "1", "description": "ID of the client to send message to. Must belong to your organization.", "type": "default", "enabled": true}, {"key": "phone_number_id", "value": "1", "description": "ID of the WhatsApp phone number to send from. Must belong to your organization.", "type": "default", "enabled": true}, {"key": "template_id", "value": "1", "description": "ID of the WhatsApp template to use (optional). Must be published and belong to your organization.", "type": "default", "enabled": true}, {"key": "last_message_id", "value": "", "description": "ID of the last sent message (automatically set by tests)", "type": "default", "enabled": true}, {"key": "last_whatsapp_message_id", "value": "", "description": "WhatsApp message ID of the last sent message (automatically set by tests)", "type": "default", "enabled": true}, {"key": "long_text_over_4096_chars", "value": "", "description": "Long text for validation testing (automatically generated)", "type": "default", "enabled": true}], "_postman_variable_scope": "environment"}