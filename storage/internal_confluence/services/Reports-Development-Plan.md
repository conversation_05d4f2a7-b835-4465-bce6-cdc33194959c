# 📊 Reports Module Development Plan

## Overview
This document outlines a comprehensive development plan to improve our Reports module by building complete test coverage and enhancing Postman documentation. The plan covers 6 report entities with a focus on making our analytics and reporting system more developer-friendly and robust.

## 🎯 Goals
- Build unit and integration/feature tests for all Report entities
- Complete test coverage for UseCases, Domains, Factories, and Repositories
- Improve Postman documentation with complete API field coverage
- Ensure all reporting endpoints have proper request/response examples
- Test aggregation logic, filtering, and grouping functionality

## 📋 Report Entities (6 Total)

### Core Report System
1. **CountSumReport** - Universal count and sum reporting with dynamic repository support
2. **StockEntryReport** - Stock entry analytics with grouping and aggregation
3. **StockExitReport** - Stock exit analytics with grouping and aggregation

### Supporting Report Entities
4. **StockEntryGroup** - Grouped stock entry data with totals
5. **StockEntryRow** - Individual stock entry report rows
6. **StockExitGroup** - Grouped stock exit data with totals
7. **StockExitRow** - Individual stock exit report rows

---

## 🚀 Development Tasks

### Task 1: CountSumReport Entity (Universal Reporting)
**Priority: High** | **Estimated Time: 5-6 hours**

#### Current Status
- ✅ Domain: Complex universal reporting with dynamic repository support
- ✅ UseCases: Count/GetReport and Sum/GetReport
- ❌ Factory: Missing
- ❌ Repository: Uses dynamic repository resolution
- ⚠️ Tests: Incomplete domain tests, missing use case tests
- ✅ Postman: Basic endpoints exist via ReportController

#### Supported Models
- products, projects, clients, budgets, stock_entries, stock_exits, stocks, groups, brands, users, batches

#### Tasks
**Unit Tests:**
- [ ] Complete `tests/Unit/Domains/Inventory/Report/CountSumReportTest.php`
- [ ] Create `tests/Unit/Factories/Report/CountSumReportFactoryTest.php` with app()->make() test
- [ ] Test dynamic repository resolution for all supported models
- [ ] Test filter application for each model type

**Feature/Integration Tests:**
- [ ] Create `tests/Feature/Reports/CountSumReport/CountReportTest.php`
- [ ] Create `tests/Feature/Reports/CountSumReport/SumReportTest.php`
- [ ] Create `tests/Feature/Reports/CountSumReport/MultiModelTest.php`
- [ ] Create `tests/Feature/Reports/CountSumReport/FilteringTest.php`
- [ ] Test each supported model (products, clients, budgets, etc.)

**Postman Documentation:**
- [ ] Add count endpoints for all supported models
- [ ] Add sum endpoints with column specification
- [ ] Include filtering examples for each model type
- [ ] Document all supported models and their available columns
- [ ] Add response examples showing count and sum results

---

### Task 2: StockEntryReport Entity
**Priority: High** | **Estimated Time: 4-5 hours**

#### Current Status
- ✅ Domain: Complete with grouping and aggregation logic
- ✅ UseCases: StockEntry/GetReport with grouping support
- ❌ Factory: Missing (uses StockEntryReportFactory)
- ✅ Repository: StockEntryRepository with fetchReport method
- ❌ Tests: Missing all tests
- ✅ Postman: Basic endpoint exists

#### Tasks
**Unit Tests:**
- [ ] Create `tests/Unit/Domains/Inventory/Report/StockEntryReportTest.php`
- [ ] Create `tests/Unit/Factories/Report/StockEntryReportFactoryTest.php` with app()->make() test
- [ ] Test aggregation logic (total_quantity, total_value)
- [ ] Test grouping functionality

**Feature/Integration Tests:**
- [ ] Create `tests/Feature/Reports/StockEntryReport/GetReportTest.php`
- [ ] Create `tests/Feature/Reports/StockEntryReport/GroupingTest.php`
- [ ] Create `tests/Feature/Reports/StockEntryReport/FilteringTest.php`
- [ ] Create `tests/Feature/Reports/StockEntryReport/AggregationTest.php`

**Postman Documentation:**
- [ ] Add all StockEntryReport fields: total_quantity, total_value, stock_entries_groups
- [ ] Include grouping parameter examples (grouped_by)
- [ ] Document filtering with StockEntryReportFilters
- [ ] Add ordering and pagination examples
- [ ] Include nested group structure examples

---

### Task 3: StockExitReport Entity
**Priority: High** | **Estimated Time: 4-5 hours**

#### Current Status
- ✅ Domain: Complete with grouping and aggregation logic
- ✅ UseCases: StockExit/GetReport with grouping support
- ❌ Factory: Missing (uses StockExitReportFactory)
- ✅ Repository: StockExitRepository with fetchReport method
- ❌ Tests: Missing all tests
- ✅ Postman: Basic endpoint exists

#### Tasks
**Unit Tests:**
- [ ] Create `tests/Unit/Domains/Inventory/Report/StockExitReportTest.php`
- [ ] Create `tests/Unit/Factories/Report/StockExitReportFactoryTest.php` with app()->make() test
- [ ] Test aggregation logic (total_quantity, total_value)
- [ ] Test grouping functionality

**Feature/Integration Tests:**
- [ ] Create `tests/Feature/Reports/StockExitReport/GetReportTest.php`
- [ ] Create `tests/Feature/Reports/StockExitReport/GroupingTest.php`
- [ ] Create `tests/Feature/Reports/StockExitReport/FilteringTest.php`
- [ ] Create `tests/Feature/Reports/StockExitReport/AggregationTest.php`

**Postman Documentation:**
- [ ] Add all StockExitReport fields: total_quantity, total_value, stock_exits_groups
- [ ] Include grouping parameter examples (grouped_by)
- [ ] Document filtering with StockExitReportFilters
- [ ] Add ordering and pagination examples
- [ ] Include nested group structure examples

---

### Task 4: StockEntryGroup Entity
**Priority: Medium** | **Estimated Time: 3-4 hours**

#### Current Status
- ✅ Domain: Complete with group aggregation logic
- ❌ UseCases: No dedicated use cases (used within StockEntryReport)
- ❌ Factory: Missing
- ❌ Repository: No dedicated repository
- ❌ Tests: Missing all tests
- ❌ Postman: No dedicated endpoints (part of StockEntryReport)

#### Tasks
**Unit Tests:**
- [ ] Create `tests/Unit/Domains/Inventory/Report/StockEntryGroupTest.php`
- [ ] Create `tests/Unit/Factories/Report/StockEntryGroupFactoryTest.php` with app()->make() test
- [ ] Test group aggregation logic
- [ ] Test stock_entries array handling

**Feature/Integration Tests:**
- [ ] Create `tests/Feature/Reports/StockEntryGroup/GroupAggregationTest.php`
- [ ] Create `tests/Feature/Reports/StockEntryGroup/NestedDataTest.php`

**Postman Documentation:**
- [ ] Document group structure within StockEntryReport responses
- [ ] Add examples of grouped data with totals
- [ ] Include nested stock_entries array examples

---

### Task 5: StockExitGroup Entity
**Priority: Medium** | **Estimated Time: 3-4 hours**

#### Current Status
- ✅ Domain: Complete with group aggregation logic
- ❌ UseCases: No dedicated use cases (used within StockExitReport)
- ❌ Factory: Missing
- ❌ Repository: No dedicated repository
- ❌ Tests: Missing all tests
- ❌ Postman: No dedicated endpoints (part of StockExitReport)

#### Tasks
**Unit Tests:**
- [ ] Create `tests/Unit/Domains/Inventory/Report/StockExitGroupTest.php`
- [ ] Create `tests/Unit/Factories/Report/StockExitGroupFactoryTest.php` with app()->make() test
- [ ] Test group aggregation logic
- [ ] Test stock_exits array handling

**Feature/Integration Tests:**
- [ ] Create `tests/Feature/Reports/StockExitGroup/GroupAggregationTest.php`
- [ ] Create `tests/Feature/Reports/StockExitGroup/NestedDataTest.php`

**Postman Documentation:**
- [ ] Document group structure within StockExitReport responses
- [ ] Add examples of grouped data with totals
- [ ] Include nested stock_exits array examples

---

### Task 6: StockEntryRow & StockExitRow Entities
**Priority: Low** | **Estimated Time: 2-3 hours each**

#### Current Status
- ❌ Domain: Missing (referenced but not implemented)
- ❌ UseCases: Missing
- ❌ Factory: Missing
- ❌ Repository: Missing
- ❌ Tests: Missing
- ❌ Postman: Missing

#### Tasks
**Unit Tests:**
- [ ] Create `tests/Unit/Domains/Inventory/Report/StockEntryRowTest.php`
- [ ] Create `tests/Unit/Domains/Inventory/Report/StockExitRowTest.php`
- [ ] Create corresponding factory tests with app()->make() verification

**Feature/Integration Tests:**
- [ ] Create `tests/Feature/Reports/StockEntryRow/RowDataTest.php`
- [ ] Create `tests/Feature/Reports/StockExitRow/RowDataTest.php`

**Postman Documentation:**
- [ ] Document row structure within group responses
- [ ] Include individual transaction examples

---

## 🛠️ Implementation Guidelines

### Critical Testing Requirements
1. **Circular Dependency Prevention**: 
   - Always use `app()->make()` when encountering circular import issues
   - Test factory resolution through service container in every factory test
   - Avoid direct instantiation of factories with complex dependencies

2. **Factory Service Container Testing**:
   - Every factory MUST have a test verifying `app()->make(FactoryClass::class)` works
   - Example test pattern:
   ```php
   public function test_factory_can_be_resolved_via_service_container()
   {
       $factory = app()->make(ReportEntityFactory::class);
       $this->assertInstanceOf(ReportEntityFactory::class, $factory);
   }
   ```

3. **Aggregation Logic Testing**:
   - Test sum and count calculations with real data
   - Validate grouping logic with multiple group criteria
   - Test filtering with various filter combinations
   - Verify performance with large datasets

### Reports-Specific Guidelines

#### Dynamic Repository Testing
- Test CountSumReport with all supported models
- Validate repository resolution for each model type
- Test filter application for different entity types
- Verify error handling for unsupported models

#### Aggregation and Grouping Testing
- Test mathematical accuracy of sum and count operations
- Validate grouping by different criteria (date, product, client, etc.)
- Test nested aggregation (group totals vs overall totals)
- Verify handling of null values and edge cases

#### Performance Testing
- Test report generation with large datasets
- Validate query optimization for complex aggregations
- Test memory usage with extensive grouping
- Verify response times for different report types

### Test Organization
```
tests/
├── Unit/
│   ├── Domains/Inventory/Report/
│   └── Factories/Report/
└── Feature/
    └── Reports/
        ├── CountSumReport/
        ├── StockEntryReport/
        ├── StockExitReport/
        ├── StockEntryGroup/
        └── StockExitGroup/
```

### Priority Implementation Order

#### Phase 1: Core Reporting (Week 1)
1. **CountSumReport** - Universal reporting foundation
2. **StockEntryReport** - Stock entry analytics
3. **StockExitReport** - Stock exit analytics

#### Phase 2: Supporting Entities (Week 2)
1. **StockEntryGroup** - Entry grouping logic
2. **StockExitGroup** - Exit grouping logic
3. **Row entities** - Individual transaction details

### Success Metrics
- [ ] 100% test coverage for all Report entities
- [ ] Aggregation logic thoroughly tested and validated
- [ ] Dynamic repository resolution verified for all models
- [ ] All Postman endpoints include complete field coverage
- [ ] Performance testing completed for large datasets
- [ ] Factory service container resolution verified

### Next Steps
1. **FIRST**: Audit existing report endpoints against domain fields
2. Start with **CountSumReport** as it's the foundation
3. **MANDATORY**: Implement factory service container test for every factory
4. Focus on aggregation logic and mathematical accuracy
5. Test dynamic repository resolution thoroughly
6. Validate performance with realistic data volumes

### Critical Implementation Notes
- **Mathematical Accuracy**: Verify all sum and count calculations
- **Dynamic Resolution**: Test repository and filter resolution for all models
- **Performance**: Monitor query performance and memory usage
- **Data Integrity**: Ensure aggregated totals match individual records
- **Error Handling**: Robust handling of invalid grouping criteria and filters
