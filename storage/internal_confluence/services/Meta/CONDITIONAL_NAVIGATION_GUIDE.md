# Conditional Navigation System - Complete Guide

## 🎯 **Overview**

The Conditional Navigation System allows you to create dynamic flow paths where users can navigate to different steps based on their button selections, rather than following a linear sequence.

## 🏗️ **Core Concepts**

### **1. Step Identifiers**
- Each step has a unique `step` field that serves as its identifier
- Use descriptive names: `"welcome"`, `"menu_selection"`, `"address_input"`
- Identifiers must be unique within a flow

### **2. Conditional Buttons**
- Buttons with `internal_type = "condition"` enable conditional navigation
- `callback_data` contains the target step identifier
- When clicked, user jumps to the specified step instead of `next_step`

### **3. Navigation Logic**
- **Default Navigation**: Uses `next_step` field (linear flow)
- **Conditional Navigation**: Uses button's target step (dynamic flow)
- **Fallback**: If conditional target not found, uses default navigation

## 📋 **Implementation Steps**

### **1. Create Steps with Identifiers**
```php
Step::create([
    'step' => 'welcome',        // ← Step identifier
    'type' => 'welcome',
    'is_interactive' => true,
    'json' => json_encode([
        'message' => 'Welcome! What would you like to do?'
    ])
]);

Step::create([
    'step' => 'product_info',   // ← Target step identifier
    'type' => 'info',
    'is_message' => true,
    'json' => json_encode([
        'message' => 'Here are our products...'
    ])
]);
```

### **2. Create Conditional Buttons**
```php
Button::create([
    'text' => 'View Products',
    'type' => 'QUICK_REPLY',
    'internal_type' => 'condition',     // ← Makes it conditional
    'callback_data' => json_encode([
        'target_step' => 'product_info', // ← Target step identifier
        'button_id' => 'view_products'
    ])
]);
```

### **3. Alternative: Use Helper Method**
```php
$button = Button::createConditionalButton(
    $organizationId,
    'View Products',
    'product_info',  // target step identifier
    'view_products'  // button ID
);
```

## 🔀 **Navigation Flow Examples**

### **Simple Branching**
```
welcome
├── "View Products" → product_info
├── "Get Support" → support  
└── "Place Order" → order_process
```

### **Complex Navigation**
```
welcome
├── "Products" → product_info → completion
├── "Support" → support
│   ├── "Contact" → completion
│   ├── "Order" → order_process
│   └── "Back" → welcome
└── "Order" → order_process
    ├── "Pizza A" → address_input → completion
    ├── "Pizza B" → address_input → completion
    └── "Back" → welcome
```

## 🛠️ **Usage Examples**

### **Example 1: Restaurant Menu**
```php
// Welcome step with menu options
$welcomeStep = Step::create([
    'step' => 'welcome',
    'json' => json_encode([
        'message' => 'Welcome to Pizza Express! What would you like?'
    ])
]);

// Create conditional buttons
$buttons = [
    ['text' => '🍕 Order Pizza', 'target' => 'pizza_menu'],
    ['text' => '🥗 Order Salad', 'target' => 'salad_menu'],
    ['text' => '📞 Contact Us', 'target' => 'contact_info']
];

foreach ($buttons as $buttonData) {
    $button = Button::createConditionalButton(
        $organizationId,
        $buttonData['text'],
        $buttonData['target']
    );
    // Attach to component...
}
```

### **Example 2: Support Flow**
```php
// Support step with options
$supportStep = Step::create([
    'step' => 'support',
    'json' => json_encode([
        'message' => 'How can we help you?'
    ])
]);

// Conditional navigation based on support type
$buttons = [
    ['text' => '🐛 Report Bug', 'target' => 'bug_report'],
    ['text' => '💡 Feature Request', 'target' => 'feature_request'],
    ['text' => '❓ General Question', 'target' => 'general_support'],
    ['text' => '🔙 Back to Menu', 'target' => 'welcome']
];
```

## 🧪 **Testing & Validation**

### **Create Example Flow**
```bash
# Create a flow with conditional navigation
php artisan whatsapp:create-conditional-example 1
```

### **Validate Navigation**
```bash
# Validate specific flow
php artisan whatsapp:validate-navigation 1

# Validate all flows
php artisan whatsapp:validate-navigation
```

### **Test Flow**
1. Assign flow to a phone number
2. Send WhatsApp message to trigger flow
3. Click different buttons to test navigation paths
4. Check logs for conditional navigation events

## 🔍 **Debugging**

### **Check Button Configuration**
```php
$button = Button::find(1);
if ($button->isConditionalButton()) {
    $target = $button->getTargetStepIdentifier();
    echo "Button targets step: {$target}";
}
```

### **Validate Step Paths**
```php
$conditionalService = app(ConditionalNavigationService::class);
$paths = $conditionalService->getConditionalPaths($step);

foreach ($paths as $path) {
    echo "'{$path['button_text']}' → {$path['target_step']}\n";
}
```

### **Check Navigation Logs**
Look for log entries with `'Conditional navigation applied'` to see navigation decisions.

## 📊 **Best Practices**

### **1. Use Descriptive Step Identifiers**
```php
✅ Good: 'welcome', 'pizza_menu', 'address_input', 'order_confirmation'
❌ Avoid: 'step1', 'step2', 'abc', 'temp'
```

### **2. Provide Fallback Navigation**
Always set `next_step` as a fallback even when using conditional navigation.

### **3. Validate Your Flows**
Run validation commands regularly to catch broken navigation paths.

### **4. Use Consistent Button IDs**
```php
✅ Good: 'view_products', 'contact_support', 'back_to_menu'
❌ Avoid: 'btn1', 'click_here', 'button'
```

### **5. Document Your Flow Logic**
```php
// Flow structure:
// welcome → [products|support|order]
// products → completion
// support → [contact|order|back]
// order → [pizza_a|pizza_b|back] → address → completion
```

## 🚨 **Common Issues**

### **Navigation Not Working**
- Check `internal_type = 'condition'` is set
- Verify target step identifier exists in flow
- Ensure button is properly attached to component

### **Button Not Found**
- Check `button_id` in callback_data matches interaction
- Verify button is attached to current step's component

### **Target Step Not Found**
- Verify step identifier exists in same flow
- Check for typos in step identifiers
- Run validation command to find broken references

## 🔧 **Advanced Features**

### **Dynamic Button Creation**
```php
$conditionalService = app(ConditionalNavigationService::class);

$buttonMappings = [
    'View Products' => 'product_info',
    'Get Support' => 'support',
    'Place Order' => 'order_process'
];

$buttons = $conditionalService->createConditionalButtons($step, $buttonMappings);
```

### **Navigation Validation**
```php
$errors = $conditionalService->validateStepConditionalNavigation($step);
if (!empty($errors)) {
    foreach ($errors as $error) {
        Log::error("Navigation error: {$error}");
    }
}
```

### **Check Navigation Capability**
```php
if ($conditionalService->hasConditionalNavigation($step)) {
    echo "Step has conditional navigation";
} else {
    echo "Step uses linear navigation";
}
```

## 🎉 **What's Possible Now**

With conditional navigation, you can create:

- **Multi-path customer journeys**
- **Dynamic product catalogs**
- **Intelligent support routing**
- **Complex ordering workflows**
- **Interactive questionnaires**
- **Personalized experiences**

The system automatically handles:
- ✅ Button click detection
- ✅ Target step lookup
- ✅ Navigation validation
- ✅ Fallback to default flow
- ✅ Error handling and logging

## 🚀 **Next Steps**

1. **Create your first conditional flow**
2. **Test different navigation paths**
3. **Validate your flow logic**
4. **Monitor navigation analytics**
5. **Optimize user journeys based on data**

The conditional navigation system is production-ready and integrates seamlessly with variable substitution and all other ChatBot features!
