# Integração ChatGPT para Análise de Imagens - Proposta Emergencial

## Análise do Fluxo Atual (Telegram ReadDocText)

### Como Funciona Hoje

1. **Upload de Arquivo** (`WaitingForUpload.php`)
   - Usuário envia imagem/documento via Telegram
   - Sistema baixa arquivo usando `TelegramFileManager::getTelegramUploadedFile()`
   - Arquivo é salvo em `storage/app/public/telegram_files/`

2. **Processamento OCR** (`GoogleVisionOCR.php`)
   - Usa Google Vision API para extrair texto da imagem
   - Método `extractText()` processa imagem e retorna texto bruto
   - Fallback para Tesseract OCR se Google Vision falhar

3. **Parsing de Dados** (`MessageToDataArray.php`)
   - Detecta tipo de documento (Primavera, São Lucas, Unimed)
   - Aplica regex específicos para cada formato
   - Extrai dados estruturados (paciente, data, procedimentos, etc.)

4. **Envio para Webhook** (`SendDocumentToWebhook.php`)
   - Limpa e normaliza dados JSON
   - Envia para endpoint externo (`https://api.billdoctor.com.br/api/webhook-create`)
   - Inclui token de autenticação

### Problemas Identificados

- **Limitação do OCR**: Só extrai texto, não entende contexto
- **Parsing Rígido**: Depende de regex específicos para cada formato
- **Baixa Flexibilidade**: Não consegue adaptar a novos formatos automaticamente
- **Qualidade Variável**: OCR pode falhar com imagens de baixa qualidade

## Proposta de Integração com ChatGPT

### Abordagem Simples e Direta (Para Apresentação)

#### 1. Criar Serviço OpenAI

```php
// app/Services/OpenAI/OpenAIService.php
class OpenAIService
{
    private string $apiKey;
    private string $baseUrl = 'https://api.openai.com/v1';
    
    public function __construct()
    {
        $this->apiKey = config('services.openai.api_key');
    }
    
    public function analyzeImage(string $imagePath, string $prompt): array
    {
        // Converte imagem para base64
        $imageData = base64_encode(file_get_contents($imagePath));
        
        $payload = [
            'model' => 'gpt-4-vision-preview',
            'messages' => [
                [
                    'role' => 'user',
                    'content' => [
                        [
                            'type' => 'text',
                            'text' => $prompt
                        ],
                        [
                            'type' => 'image_url',
                            'image_url' => [
                                'url' => "data:image/jpeg;base64,{$imageData}"
                            ]
                        ]
                    ]
                ]
            ],
            'max_tokens' => 1000
        ];
        
        $response = Http::withHeaders([
            'Authorization' => "Bearer {$this->apiKey}",
            'Content-Type' => 'application/json'
        ])->post("{$this->baseUrl}/chat/completions", $payload);
        
        return $response->json();
    }
}
```

#### 2. Modificar Fluxo do Telegram

**Opção A: Substituir Google Vision**
```php
// Em WaitingForUpload.php, linha ~30
// Substituir:
// $ocrService = new GoogleVisionOCR();
// $text = $ocrService->extractText($this->telegram->telegramFile->file);

// Por:
$openAIService = new OpenAIService();
$prompt = "Analise esta imagem de documento médico e extraia as seguintes informações em formato JSON: paciente (nome completo), data_nascimento, convenio, data_inicio, cirurgiao, procedimento_descricao, diagnostico_pre_operatorio. Se alguma informação não estiver visível, use null.";

$result = $openAIService->analyzeImage(
    public_path('storage/' . $this->telegram->telegramFile->file),
    $prompt
);

$extractedData = json_decode($result['choices'][0]['message']['content'], true);
```

**Opção B: Usar como Fallback Inteligente**
```php
// Tentar OCR primeiro, depois ChatGPT se falhar
try {
    $ocrService = new GoogleVisionOCR();
    $text = $ocrService->extractText($this->telegram->telegramFile->file);
    
    // Se OCR retornar pouco texto ou dados incompletos
    if (strlen($text) < 50 || !$this->hasRequiredFields($text)) {
        throw new Exception("OCR insufficient, trying AI");
    }
    
    $messageToDataArray = new MessageToDataArray($this->telegram);
    $data = $messageToDataArray->perform($text);
    
} catch (Exception $e) {
    // Fallback para ChatGPT
    $openAIService = new OpenAIService();
    $data = $openAIService->analyzeImage($imagePath, $prompt);
}
```

#### 3. Configuração Rápida

**config/services.php**
```php
'openai' => [
    'api_key' => env('OPENAI_API_KEY'),
    'model' => env('OPENAI_MODEL', 'gpt-4-vision-preview'),
],
```

**.env**
```
OPENAI_API_KEY=sk-your-api-key-here
OPENAI_MODEL=gpt-4-vision-preview
```

### Vantagens da Integração ChatGPT

1. **Inteligência Contextual**: Entende o contexto da imagem, não só extrai texto
2. **Flexibilidade**: Pode adaptar a diferentes formatos sem regex
3. **Qualidade**: Funciona melhor com imagens de baixa qualidade
4. **Estruturação**: Retorna dados já estruturados em JSON
5. **Multilíngue**: Funciona com documentos em diferentes idiomas

### Implementação Emergencial (2-3 horas)

#### Passo 1: Instalar Dependências
```bash
composer require guzzlehttp/guzzle
```

#### Passo 2: Criar Serviço OpenAI
- Criar `app/Services/OpenAI/OpenAIService.php`
- Adicionar configuração em `config/services.php`

#### Passo 3: Modificar Fluxo Telegram
- Editar `WaitingForUpload.php` para usar ChatGPT
- Manter estrutura de resposta compatível

#### Passo 4: Testar
- Configurar API key no `.env`
- Testar com imagens de documentos médicos
- Validar formato JSON de resposta

### Prompt Otimizado para Documentos Médicos

```
Analise esta imagem de documento médico/relatório e extraia as informações em formato JSON válido.

Campos obrigatórios:
- paciente: nome completo do paciente
- data_nascimento: data no formato DD/MM/AAAA
- convenio: nome do convênio/plano de saúde
- data_inicio: data do procedimento/internação
- cirurgiao: nome do cirurgião responsável
- procedimento_descricao: descrição do procedimento realizado
- diagnostico_pre_operatorio: diagnóstico pré-operatório

Se alguma informação não estiver visível ou legível, use null.
Retorne APENAS o JSON, sem texto adicional.

Exemplo de resposta:
{
  "paciente": "João Silva Santos",
  "data_nascimento": "15/03/1980",
  "convenio": "Unimed",
  "data_inicio": "20/12/2024",
  "cirurgiao": "Dr. Maria Oliveira",
  "procedimento_descricao": "Cirurgia de vesícula",
  "diagnostico_pre_operatorio": "Colelitíase"
}
```

### Custos Estimados

- **GPT-4 Vision**: ~$0.01 por imagem (1024x1024)
- **Volume estimado**: 100 imagens/dia = $1/dia = $30/mês
- **Muito mais barato que desenvolvimento de OCR customizado**

### Próximos Passos (Pós-Apresentação)

1. **Criar Use Case Dedicado**: `AnalyzeImageWithAI`
2. **Implementar Cache**: Evitar reprocessar mesmas imagens
3. **Adicionar Validação**: Verificar qualidade dos dados extraídos
4. **Logging Detalhado**: Monitorar performance e custos
5. **Fallback Inteligente**: OCR → ChatGPT → Manual
6. **Interface Admin**: Para revisar e corrigir extrações

### Riscos e Mitigações

**Riscos:**
- Dependência de API externa
- Custos variáveis
- Latência maior que OCR

**Mitigações:**
- Manter OCR como fallback
- Implementar rate limiting
- Cache de resultados
- Monitoramento de custos

## Conclusão

A integração com ChatGPT pode revolucionar a análise de documentos médicos, oferecendo:
- **Maior precisão** na extração de dados
- **Flexibilidade** para novos formatos
- **Implementação rápida** para a apresentação
- **Escalabilidade** futura

Para a apresentação de hoje, recomendo a **Opção A** (substituição direta) por ser mais simples e demonstrar claramente o poder da IA na análise de imagens.
