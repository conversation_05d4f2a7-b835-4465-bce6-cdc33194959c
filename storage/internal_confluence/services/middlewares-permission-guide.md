# Guia de Middlewares de Permissão

## Middlewares Disponíveis

### 1. CheckPermission (`permission`)
Verifica se o usuário possui uma permissão específica.

**Uso:**
```php
Route::get('/example', [Controller::class, 'method'])->middleware('permission:manage_users');
```

**Funcionalidades:**
- Verifica se usuário está autenticado
- Verifica se usuário tem profile
- Converte Model para Domain para usar método `can()`
- Adiciona informações de permissão ao request
- Log de tentativas de acesso negado

### 2. CheckSuperAdmin (`super_admin`)
Verifica se o usuário é super admin.

**Uso:**
```php
Route::apiResource('permissions', PermissionController::class)->middleware('super_admin');
```

**Funcionalidades:**
- Apenas super admins podem acessar
- Adiciona informações do profile ao request
- Log de tentativas de acesso negado

### 3. CheckAdmin (`admin`)
Verifica se o usuário é admin ou super admin.

**Uso:**
```php
Route::apiResource('profiles', ProfileController::class)->middleware('admin');
```

**Funcionalidades:**
- Admins e super admins podem acessar
- Adiciona informações do profile ao request
- Log de tentativas de acesso negado

### 4. CheckOrganizationOwnership (`organization.ownership`)
Verifica se o usuário pertence à organização sendo acessada.

**Uso:**
```php
Route::get('/organization/{organizationId}/data', [Controller::class, 'method'])
    ->middleware('organization.ownership:organizationId');
```

**Funcionalidades:**
- Super admins podem acessar qualquer organização
- Usuários normais só acessam sua própria organização
- Parâmetro customizável para nome do ID da organização
- Adiciona informações de acesso ao request

## Grupos de Middleware

### 1. `protected`
Proteção básica com autenticação e verificação de organização.
```php
'protected' => [
    'auth:sanctum',
    \App\Http\Middleware\CheckOrganizationAccess::class,
]
```

### 2. `admin_protected`
Proteção para recursos que requerem privilégios de admin.
```php
'admin_protected' => [
    'auth:sanctum',
    \App\Http\Middleware\CheckOrganizationAccess::class,
    \App\Http\Middleware\CheckAdmin::class,
]
```

### 3. `super_admin_protected`
Proteção para recursos que requerem privilégios de super admin.
```php
'super_admin_protected' => [
    'auth:sanctum',
    \App\Http\Middleware\CheckOrganizationAccess::class,
    \App\Http\Middleware\CheckSuperAdmin::class,
]
```

## Exemplos de Uso

### Proteção por Permissão Específica
```php
// Apenas usuários com permissão 'manage_users' podem acessar
Route::apiResource('users', UserController::class)->middleware('permission:manage_users');

// Múltiplas permissões (aplicar em métodos específicos do controller)
Route::get('/users', [UserController::class, 'index'])->middleware('permission:view_users');
Route::post('/users', [UserController::class, 'store'])->middleware('permission:create_users');
```

### Proteção por Nível de Acesso
```php
// Apenas admins e super admins
Route::apiResource('profiles', ProfileController::class)->middleware('admin');

// Apenas super admins
Route::apiResource('permissions', PermissionController::class)->middleware('super_admin');
```

### Proteção com Isolamento Organizacional
```php
// Usuários só podem acessar dados da própria organização
Route::get('/organization/{organizationId}/reports', [ReportController::class, 'index'])
    ->middleware('organization.ownership:organizationId');
```

### Combinação de Middlewares
```php
// Admin + permissão específica + isolamento organizacional
Route::post('/organization/{organizationId}/users', [UserController::class, 'store'])
    ->middleware(['admin', 'permission:create_users', 'organization.ownership:organizationId']);
```

## Informações Adicionadas ao Request

Os middlewares adicionam informações úteis ao request que podem ser usadas nos controllers:

### CheckPermission
```php
$request->get('user_permissions'); // Array de permissões do usuário
$request->get('user_profile');     // Informações do profile
```

### CheckAdmin/CheckSuperAdmin
```php
$request->get('is_admin');         // true se é admin
$request->get('is_super_admin');   // true se é super admin
$request->get('user_profile');     // Informações do profile
```

### CheckOrganizationOwnership
```php
$request->get('organization_access_granted'); // true se acesso foi concedido
$request->get('access_reason');               // 'super_admin' ou 'organization_member'
$request->get('organization_id');             // ID da organização acessada
```

## Tratamento de Erros

Todos os middlewares retornam respostas apropriadas para:

### API (JSON)
```json
{
    "error": "Permission Denied",
    "message": "You don't have permission to access this resource.",
    "required_permission": "manage_users",
    "code": "PERMISSION_DENIED"
}
```

### Web (Redirect)
Redirecionamento para página anterior com mensagem de erro.

## Logs

Todos os middlewares fazem log de tentativas de acesso negado para auditoria:

```php
Log::warning('Permission denied', [
    'user_id' => $user->id,
    'required_permission' => $permission,
    'request_url' => $request->url(),
    // ... outros dados relevantes
]);
```
