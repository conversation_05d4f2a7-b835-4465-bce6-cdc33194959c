# WhatsApp Campaign Launch Process

## Overview
This document outlines the complete technical flow for creating, launching, and sending WhatsApp campaigns through the system.

## Step-by-Step Process

### 1. Create Phone Number
**Endpoint:** `POST /api/phone_numbers`
**Purpose:** Register a WhatsApp Business phone number in the system

**Technical Details:**
- Creates a `PhoneNumber` domain object
- Stores WhatsApp Business API credentials
- Links to organization for multi-tenancy
- Required for template publishing and message sending

**Key Fields:**
- `phone_number`: WhatsApp Business phone number
- `access_token`: Meta API access token
- `business_account_id`: WhatsApp Business Account ID
- `organization_id`: Organization ownership

### 2. Create Template
**Endpoint:** `POST /api/template/save`
**Purpose:** Create a WhatsApp message template with components, parameters, and buttons

**Technical Details:**
- Uses `SaveFullTemplate` use case following SaveFullFlow pattern
- Creates `Template` domain with related `Component`, `Parameter`, and `Button` objects
- Supports variable substitution using `{{model.variable}}` format (e.g., `{{client.name}}`)
- Template status starts as 'draft'

**Key Components:**
- **Template**: Main container with name, category, language
- **Components**: Header, body, footer sections
- **Parameters**: Dynamic variables for personalization
- **Buttons**: Call-to-action, URL, or quick reply buttons

### 3. Publish Template to WhatsApp
**Endpoint:** `POST /api/template/publish/whatsapp/{id}`
**Purpose:** Submit template to Meta for approval

**Technical Details:**
- Uses `PublishTemplate` use case
- Converts template to WhatsApp API format
- Submits to Meta Business API
- Creates `TemplatePublishing` record for tracking
- Template status changes to 'pending_approval'

**Cron Job:** `whatsapp:publish-templates` (runs every minute)
- Processes queued template publications
- Handles approval status updates from Meta
- Updates template status to 'approved' or 'rejected'

### 4. Create Campaign
**Endpoint:** `POST /api/campaigns`
**Purpose:** Create a campaign container for bulk messaging

**Technical Details:**
- Uses `Store` use case
- Creates `Campaign` domain object
- Links to approved template and phone number
- Initial status: not sent, not sending

**Key Fields:**
- `name`: Campaign identifier
- `template_id`: Reference to approved template
- `phone_number_id`: Sending phone number
- `organization_id`: Organization ownership
- `is_sent`: false (initial)
- `is_sending`: false (initial)

### 5. Add Clients to Campaign
**Endpoint:** `POST /api/campaign/add-clients/{id}`
**Purpose:** Associate target clients with the campaign

**Technical Details:**
- Uses `AddClientsToCampaign` use case
- Accepts array of `client_ids` in request body
- Uses Laravel's `sync()` method to prevent duplicates
- Validates campaign ownership and status
- Prevents modification of sent/sending campaigns

**Request Format:**
```json
{
  "client_ids": [1, 2, 3, 4, 5]
}
```

**Validation:**
- Campaign must belong to user's organization
- Campaign cannot be already sent (`is_sent = false`)
- Campaign cannot be currently sending (`is_sending = false`)

### 6. Launch Campaign
**Endpoint:** `POST /api/campaign/launch/{id}`
**Purpose:** Generate messages and initiate sending process

**Technical Details:**
- Uses `LaunchCampaign` use case
- Calls `GenerateMessages` use case internally
- Creates individual `Message` records for each client
- Applies variable substitution (e.g., `{{client.name}}` → actual client name)
- Sets campaign status: `is_sending = true`, `sent_at = now()`
- Message status: 'is_sending', `is_sent = false`

**Message Generation Process:**
1. Iterates through all campaign clients
2. Creates `Message` domain object for each client
3. Applies template variable substitution
4. Stores message with 'is_sending' status
5. Links message to campaign, client, and template

### 7. Send Messages (Cron Process)
**Command:** `whatsapp:send-messages`
**Schedule:** Every minute via Laravel Scheduler
**Purpose:** Process pending messages and send to WhatsApp API

**Technical Flow:**
1. **Fetch Messages**: `GetMessagesAvailableToSent` use case
   - Queries messages with status 'is_sending'
   - Filters by `is_sent = false`
   - Respects `scheduled_at` if set
   - Orders by creation date (FIFO)
   - Limits to 500 messages per run

2. **Send Process**: For each message
   - Uses `Send` use case with `MessageService`
   - Converts message domain to WhatsApp API payload
   - Sends via Meta Business API
   - Updates message status to 'sent' on success
   - Sets `sent_at` timestamp and `external_id`
   - Logs errors and sets status to 'failed' on failure

3. **Error Handling**:
   - Database transactions for consistency
   - Comprehensive error logging via `DBLog`
   - Failed messages marked for retry/investigation
   - Campaign continues sending remaining messages

## Key Technical Components

### Domain Objects
- **Campaign**: Main campaign entity with metadata
- **Message**: Individual message instance per client
- **Template**: WhatsApp template definition
- **Client**: Target recipient information
- **PhoneNumber**: Sending phone number configuration

### Use Cases
- **LaunchCampaign**: Orchestrates campaign launch
- **GenerateMessages**: Creates message instances
- **AddClientsToCampaign**: Manages client associations
- **Send**: Handles individual message transmission
- **GetMessagesAvailableToSent**: Fetches pending messages

### Services
- **MessageService**: WhatsApp API integration
- **TemplateService**: Template management and publishing

### Repositories
- **CampaignRepository**: Campaign data persistence
- **MessageRepository**: Message data operations
- **TemplateRepository**: Template data management

## Monitoring and Logging

### Campaign Status Tracking
- `is_sending`: Campaign is actively processing
- `is_sent`: All messages have been processed
- `sent_at`: Timestamp of campaign launch

### Message Status Tracking
- `pending`: Initial state (not used in current flow)
- `is_sending`: Ready for transmission
- `sent`: Successfully delivered to WhatsApp
- `failed`: Transmission failed

### Error Logging
- All failures logged via `DBLog::logError()`
- Includes context: message ID, client ID, error details
- Organized by organization and user for debugging

## Performance Considerations

### Rate Limiting
- 500 messages per minute maximum
- WhatsApp API rate limits respected
- Cron job prevents overlapping executions

### Database Optimization
- Indexed queries for message fetching
- Batch processing for large campaigns
- Transaction management for data consistency

### Scalability
- Stateless message processing
- Horizontal scaling possible via queue workers
- Organization-based data isolation

## Security

### Authorization
- Organization-based access control
- User authentication required for all operations
- Campaign ownership validation

### Data Protection
- Sensitive credentials stored securely
- API tokens encrypted in database
- Audit trail for all operations
