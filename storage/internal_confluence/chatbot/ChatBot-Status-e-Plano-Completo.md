# ChatBot WhatsApp - Status Atual e Plano para 100% Funcional

## 📋 Status Atual do Sistema

### ✅ Funcionalidades Implementadas

#### 1. **Estrutura de Domains**
- **Flow**: Fluxos de conversa com sequência de passos
- **Step**: Passos individuais com lógica condicional
- **Component**: Componentes de mensagem (header, body, footer, buttons)
- **Button**: Botões interativos com ações e condições
- **Parameter**: Parâmetros dinâmicos para personalização
- **Template**: Templates WhatsApp com componentes
- **Campaign**: Campanhas de marketing com targeting
- **Message**: Mensagens individuais com tracking de entrega
- **Conversation**: Sessões de chat ativas com clientes
- **Interaction**: Rastreamento de interações do usuário
- **PhoneNumber**: Gerenciamento de números WhatsApp Business

#### 2. **Processamento de Webhook**
```
WhatsApp Webhook → WhatsAppWebhookController
├── ProcessWebhookMessage → Valida e extrai dados
├── FindOrCreateClient → Identifica/cria cliente
├── FindOrCreateConversation → Gerencia conversa ativa
├── ProcessFlowStep → Processa passo atual do fluxo
└── SendWhatsAppResponse → Envia resposta automática
```

#### 3. **Tipos de Steps Suportados**
- **Message Steps**: Envio de mensagens simples
- **Interactive Steps**: Botões e listas interativas
- **Input Steps**: Coleta de dados do usuário
- **Command Steps**: Execução de comandos específicos

#### 4. **Navegação Condicional**
- Navegação baseada em botões com `internal_type: 'condition'`
- Identificação de steps por atributo `step`
- Coleta de input com padrão `client.field`

#### 5. **Timeout e Cleanup Básico**
- `ConversationTimeoutService` implementado
- Command `chatbot:cleanup-conversations` disponível
- Configuração em `config/chatbot.php`

### ⚠️ Problemas Críticos Identificados

#### 1. **Arquitetura de Domains Inconsistente**

##### **Flow Domain - Problemas:**
- ❌ **Falta campos de timeout**: `inactivity_minutes` e `ending_conversation_message`
- ❌ **Relacionamento com Steps mal definido**: Steps não têm referência clara ao Flow
- ❌ **Falta validação de integridade**: Não valida se steps formam um fluxo válido
- ❌ **Ausência de versionamento**: Mudanças no flow quebram conversas ativas

##### **Step Domain - Problemas Graves:**
- ❌ **Múltiplos tipos booleanos confusos**: `is_message`, `is_interactive`, `is_command`, `is_input`
- ❌ **Navegação primitiva**: Apenas `next_step` e `earlier_step` (não suporta condicionais complexas)
- ❌ **Configuração em JSON genérico**: Falta estrutura tipada para diferentes tipos de step
- ❌ **Ausência de validação**: Não valida se configuração JSON está correta
- ❌ **Relacionamento com Component confuso**: Um step pode ter um component, mas deveria ter múltiplos

##### **Component Domain - Limitações:**
- ❌ **Tipos hardcoded**: Constantes string em vez de enum
- ❌ **Relacionamento duplo**: Pertence a Step E Template (confuso)
- ❌ **Falta suporte a mídia**: Não suporta adequadamente imagens, vídeos, documentos
- ❌ **Buttons como array simples**: Deveria ser relacionamento tipado

##### **Button Domain - Não Conforme WhatsApp API:**
- ❌ **Tipos não padronizados**: `QUICK_REPLY`, `URL`, `PHONE_NUMBER` não seguem spec do Meta
- ❌ **Falta suporte a List Buttons**: WhatsApp suporta até 10 opções em lista
- ❌ **Ausência de Call-to-Action buttons**: Não suporta CTA buttons
- ❌ **internal_type e internal_data**: Campos customizados que não mapeiam para WhatsApp

##### **Parameter Domain - Limitado:**
- ❌ **Apenas texto**: Não suporta parâmetros de mídia (imagem, vídeo, documento)
- ❌ **Substituição básica**: Não suporta formatação avançada
- ❌ **Relacionamento confuso**: Pertence a Campaign E Component

#### 2. **Use Cases Ineficientes**

##### **ProcessFlowStep - Problemas:**
- ❌ **Lógica monolítica**: Um método gigante para todos os tipos de step
- ❌ **Navegação condicional limitada**: `getNextStepForSelection()` retorna sempre `next_step`
- ❌ **Falta validação de input**: Não valida dados coletados do usuário
- ❌ **Ausência de retry logic**: Não tenta novamente em caso de erro

##### **SendWhatsAppResponse - Limitações:**
- ❌ **Não segue padrões WhatsApp**: Não usa corretamente interactive messages
- ❌ **Falta suporte a templates**: Não integra com WhatsApp Business Templates
- ❌ **Ausência de fallback**: Não tem estratégia para falhas de envio

#### 3. **Incompatibilidade com WhatsApp Business API**

##### **Interactive Messages não conformes:**
- ❌ **Reply Buttons**: Máximo 3 botões, formato específico
- ❌ **List Messages**: Até 10 opções, estrutura de seções
- ❌ **Call-to-Action Buttons**: URL e Phone buttons com formato específico
- ❌ **Flow Messages**: Não suporta WhatsApp Flows (formulários complexos)

##### **Template Messages:**
- ❌ **Categorias limitadas**: Não suporta todas as categorias do WhatsApp
- ❌ **Parâmetros de mídia**: Não suporta header com imagem/vídeo
- ❌ **Botões de template**: Não suporta quick reply e call-to-action em templates

#### 4. **Problemas de Performance e Escalabilidade**
- ❌ **N+1 queries**: Carregamento de relacionamentos não otimizado
- ❌ **Falta cache**: Flows e Steps carregados a cada webhook
- ❌ **Processamento síncrono**: Webhook processing pode ser lento
- ❌ **Ausência de queue**: Envio de mensagens não é assíncrono

## 🎯 Plano de Refatoração Completa

### **FASE 1: Reestruturação de Domains (5-7 dias)**

#### 1.1 **Refatorar Step Domain**
```php
// app/Domains/ChatBot/Step.php - Nova estrutura
class Step
{
    public ?int $id;
    public ?int $organization_id;
    public ?int $flow_id;
    public ?string $name;
    public ?string $description;
    public ?int $position;
    public ?StepType $type; // ENUM: MESSAGE, INTERACTIVE, INPUT, COMMAND, CONDITION
    public ?array $configuration; // Configuração tipada por tipo
    public ?array $navigation_rules; // Regras de navegação condicional
    public ?int $timeout_seconds; // Timeout específico do step
    public ?bool $is_initial_step;
    public ?bool $is_ending_step;

    // Relacionamentos
    public ?Flow $flow;
    /** @var Component[] */
    public ?array $components;
    /** @var StepNavigation[] */
    public ?array $navigation_options;
}

enum StepType: string
{
    case MESSAGE = 'message';
    case INTERACTIVE = 'interactive';
    case INPUT = 'input';
    case COMMAND = 'command';
    case CONDITION = 'condition';
    case WEBHOOK = 'webhook';
    case DELAY = 'delay';
}
```

#### 1.2 **Criar StepNavigation Domain**
```php
// app/Domains/ChatBot/StepNavigation.php - Novo
class StepNavigation
{
    public ?int $id;
    public ?int $step_id;
    public ?int $target_step_id;
    public ?string $condition_type; // button_click, text_match, default
    public ?string $condition_value; // button_id, regex, etc
    public ?int $priority; // Para ordenação de condições
    public ?bool $is_default; // Navegação padrão se nenhuma condição atender
}
```

#### 1.3 **Refatorar Button Domain para WhatsApp Compliance**
```php
// app/Domains/ChatBot/Button.php - Refatorado
class Button
{
    public ?int $id;
    public ?int $component_id;
    public ?WhatsAppButtonType $type;
    public ?string $title; // Máximo 20 caracteres
    public ?string $id; // Para reply buttons
    public ?string $url; // Para URL buttons
    public ?string $phone_number; // Para phone buttons
    public ?int $position; // Ordem do botão (máximo 3 para reply)
}

enum WhatsAppButtonType: string
{
    case REPLY = 'reply';
    case URL = 'url';
    case PHONE_NUMBER = 'phone_number';
    case COPY_CODE = 'copy_code';
    case FLOW = 'flow'; // Para WhatsApp Flows
}
```

#### 1.4 **Criar InteractiveMessage Domain**
```php
// app/Domains/ChatBot/InteractiveMessage.php - Novo
class InteractiveMessage
{
    public ?int $id;
    public ?int $step_id;
    public ?InteractiveType $type;
    public ?string $header_text;
    public ?string $body_text;
    public ?string $footer_text;

    // Para List Messages
    public ?string $button_text; // "Ver opções"
    /** @var ListSection[] */
    public ?array $sections;

    // Para Button Messages
    /** @var Button[] */
    public ?array $buttons;
}

enum InteractiveType: string
{
    case BUTTON = 'button';
    case LIST = 'list';
    case FLOW = 'flow';
}
```

#### 1.5 **Estender Flow Domain**
```php
// app/Domains/ChatBot/Flow.php - Campos adicionais
public ?int $inactivity_minutes;
public ?string $ending_conversation_message;
public ?int $version; // Versionamento de flows
public ?FlowStatus $status; // DRAFT, ACTIVE, ARCHIVED
public ?array $variables; // Variáveis globais do flow
public ?array $webhooks; // Webhooks para integração externa

enum FlowStatus: string
{
    case DRAFT = 'draft';
    case ACTIVE = 'active';
    case ARCHIVED = 'archived';
}
```

### **FASE 2: Refatoração de Use Cases (4-5 dias)**

#### 2.1 **Criar StepProcessor Strategy Pattern**
```php
// app/Services/Meta/WhatsApp/ChatBot/StepProcessors/StepProcessorInterface.php
interface StepProcessorInterface
{
    public function canProcess(Step $step): bool;
    public function process(Step $step, Interaction $interaction, Conversation $conversation): StepResult;
}

// app/Services/Meta/WhatsApp/ChatBot/StepProcessors/MessageStepProcessor.php
class MessageStepProcessor implements StepProcessorInterface
{
    public function process(Step $step, Interaction $interaction, Conversation $conversation): StepResult
    {
        // Processar step de mensagem
        // Aplicar substituição de variáveis
        // Determinar próximo step baseado em navigation_rules
    }
}

// app/Services/Meta/WhatsApp/ChatBot/StepProcessors/InteractiveStepProcessor.php
class InteractiveStepProcessor implements StepProcessorInterface
{
    public function process(Step $step, Interaction $interaction, Conversation $conversation): StepResult
    {
        // Processar step interativo
        // Validar seleção do usuário
        // Aplicar navegação condicional baseada na seleção
    }
}
```

#### 2.2 **Criar WhatsAppMessageBuilder**
```php
// app/Services/Meta/WhatsApp/ChatBot/Builders/WhatsAppMessageBuilder.php
class WhatsAppMessageBuilder
{
    public function buildTextMessage(string $text, string $to): array
    {
        return [
            'messaging_product' => 'whatsapp',
            'to' => $to,
            'type' => 'text',
            'text' => ['body' => $text]
        ];
    }

    public function buildInteractiveButtonMessage(InteractiveMessage $interactive, string $to): array
    {
        return [
            'messaging_product' => 'whatsapp',
            'to' => $to,
            'type' => 'interactive',
            'interactive' => [
                'type' => 'button',
                'header' => $interactive->header_text ? ['type' => 'text', 'text' => $interactive->header_text] : null,
                'body' => ['text' => $interactive->body_text],
                'footer' => $interactive->footer_text ? ['text' => $interactive->footer_text] : null,
                'action' => [
                    'buttons' => array_map(fn($button) => [
                        'type' => 'reply',
                        'reply' => [
                            'id' => $button->id,
                            'title' => $button->title
                        ]
                    ], $interactive->buttons)
                ]
            ]
        ];
    }

    public function buildInteractiveListMessage(InteractiveMessage $interactive, string $to): array
    {
        return [
            'messaging_product' => 'whatsapp',
            'to' => $to,
            'type' => 'interactive',
            'interactive' => [
                'type' => 'list',
                'header' => $interactive->header_text ? ['type' => 'text', 'text' => $interactive->header_text] : null,
                'body' => ['text' => $interactive->body_text],
                'footer' => $interactive->footer_text ? ['text' => $interactive->footer_text] : null,
                'action' => [
                    'button' => $interactive->button_text ?? 'Ver opções',
                    'sections' => array_map(fn($section) => [
                        'title' => $section->title,
                        'rows' => array_map(fn($row) => [
                            'id' => $row->id,
                            'title' => $row->title,
                            'description' => $row->description
                        ], $section->rows)
                    ], $interactive->sections)
                ]
            ]
        ];
    }
}
```

### **FASE 3: Implementação de Performance e Cache (2-3 dias)**

#### 3.1 **Cache de Flows e Steps**
```php
// app/Services/Meta/WhatsApp/ChatBot/Services/FlowCacheService.php
class FlowCacheService
{
    public function getFlow(int $flowId): ?Flow
    {
        return Cache::remember("chatbot.flow.{$flowId}", 3600, function() use ($flowId) {
            return $this->flowRepository->fetchByIdWithSteps($flowId);
        });
    }

    public function invalidateFlow(int $flowId): void
    {
        Cache::forget("chatbot.flow.{$flowId}");
    }
}
```

#### 3.2 **Queue para Processamento Assíncrono**
```php
// app/Jobs/ChatBot/ProcessWebhookMessageJob.php
class ProcessWebhookMessageJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public function __construct(
        private array $webhookData,
        private int $organizationId,
        private int $phoneNumberId
    ) {}

    public function handle(): void
    {
        // Processar webhook de forma assíncrona
        $chatBotService = app()->make(ChatBotService::class);
        $chatBotService->processWebhook($this->webhookData);
    }
}
```

#### 3.3 **Otimização de Queries**
```php
// app/Repositories/ConversationRepository.php
public function findActiveConversationWithRelations(Client $client, PhoneNumber $phoneNumber): ?Conversation
{
    return ConversationModel::where('client_id', $client->id)
        ->where('phone_number_id', $phoneNumber->id)
        ->where('is_finished', false)
        ->with([
            'flow.steps.components.buttons',
            'flow.steps.navigation_options',
            'current_step.components.buttons'
        ])
        ->first();
}
```

### **FASE 4: Timeout e Cleanup Avançado (2-3 dias)**

#### 4.1 **ConversationTimeoutService Refatorado**
```php
// app/Services/Meta/WhatsApp/ChatBot/Services/ConversationTimeoutService.php
class ConversationTimeoutService
{
    public function findTimedOutConversationsByFlow(): array
    {
        $conversations = $this->conversationRepository->findActiveConversationsWithFlow();
        $timedOut = [];

        foreach ($conversations as $conversation) {
            if ($this->isConversationTimedOut($conversation)) {
                $timedOut[] = $conversation;
            }
        }

        return $timedOut;
    }

    private function isConversationTimedOut(Conversation $conversation): bool
    {
        if (!$conversation->flow || $conversation->is_finished) {
            return false;
        }

        // Verificar timeout do flow
        $flowTimeoutMinutes = $conversation->flow->inactivity_minutes ?? 60;

        // Verificar timeout específico do step atual
        $stepTimeoutSeconds = $conversation->current_step?->timeout_seconds;

        $lastActivity = $this->getLastActivityTime($conversation);

        // Usar o menor timeout (step ou flow)
        if ($stepTimeoutSeconds) {
            $stepTimeout = $lastActivity->addSeconds($stepTimeoutSeconds);
            $flowTimeout = $lastActivity->addMinutes($flowTimeoutMinutes);
            return $stepTimeout->isPast() || $flowTimeout->isPast();
        }

        return $lastActivity->addMinutes($flowTimeoutMinutes)->isPast();
    }

    public function handleTimeoutWithMessage(Conversation $conversation): array
    {
        // 1. Enviar mensagem de encerramento personalizada
        $endingMessage = $this->prepareEndingMessage($conversation);
        $messageSent = $this->sendEndingMessage($conversation, $endingMessage);

        // 2. Marcar conversa como finalizada
        $conversation->is_finished = true;
        $conversation->updated_at = now();

        // 3. Adicionar informações de timeout
        $conversationData = $conversation->json ? json_decode($conversation->json, true) : [];
        $conversationData['timeout_info'] = [
            'timed_out_at' => now()->toISOString(),
            'reason' => 'flow_timeout',
            'timeout_minutes' => $conversation->flow->inactivity_minutes,
            'ending_message_sent' => $messageSent,
            'ending_message' => $endingMessage,
        ];
        $conversation->json = json_encode($conversationData);

        // 4. Salvar conversa
        $this->conversationRepository->save($conversation);

        return [
            'conversation_id' => $conversation->id,
            'flow_id' => $conversation->flow_id,
            'timeout_minutes' => $conversation->flow->inactivity_minutes,
            'message_sent' => $messageSent,
            'processed_at' => now()->toISOString()
        ];
    }
}
```

#### 4.2 **Agendamento de Cron Job**
```php
// app/Console/Kernel.php
protected function schedule(Schedule $schedule): void
{
    // ... existing schedules

    // ChatBot conversation timeout processing
    $schedule->command('chatbot:cleanup-conversations')
        ->everyFiveMinutes()
        ->withoutOverlapping()
        ->runInBackground()
        ->appendOutputTo(storage_path('logs/chatbot-cleanup.log'));
}
```

### **FASE 5: Testes de Integração Completos (3-4 dias)**

#### 5.1 **Estrutura de Teste Refatorada**
```
tests/Feature/ChatBot/
├── Domains/
│   ├── FlowTest.php
│   ├── StepTest.php
│   ├── InteractiveMessageTest.php
│   └── ButtonTest.php
├── UseCases/
│   ├── ProcessFlowStepTest.php
│   ├── StepProcessors/
│   │   ├── MessageStepProcessorTest.php
│   │   ├── InteractiveStepProcessorTest.php
│   │   └── InputStepProcessorTest.php
│   └── SendWhatsAppResponseTest.php
├── Integration/
│   ├── EndToEndFlowTest.php
│   ├── ConversationTimeoutTest.php
│   ├── WhatsAppComplianceTest.php
│   └── PerformanceTest.php
└── Helpers/
    ├── ChatBotTestHelper.php
    ├── WhatsAppMockHelper.php
    └── FlowBuilder.php
```

#### 5.2 **Teste de Compliance com WhatsApp**
```php
// tests/Feature/ChatBot/Integration/WhatsAppComplianceTest.php
class WhatsAppComplianceTest extends TestCase
{
    /** @test */
    public function interactive_button_message_follows_whatsapp_spec()
    {
        $interactive = InteractiveMessageFactory::create([
            'type' => InteractiveType::BUTTON,
            'buttons' => [
                ButtonFactory::create(['type' => WhatsAppButtonType::REPLY, 'title' => 'Sim']),
                ButtonFactory::create(['type' => WhatsAppButtonType::REPLY, 'title' => 'Não']),
                ButtonFactory::create(['type' => WhatsAppButtonType::REPLY, 'title' => 'Talvez']),
            ]
        ]);

        $builder = new WhatsAppMessageBuilder();
        $payload = $builder->buildInteractiveButtonMessage($interactive, '+5511999999999');

        // Validar estrutura conforme WhatsApp API
        $this->assertEquals('whatsapp', $payload['messaging_product']);
        $this->assertEquals('interactive', $payload['type']);
        $this->assertEquals('button', $payload['interactive']['type']);
        $this->assertCount(3, $payload['interactive']['action']['buttons']);

        // Validar que cada botão tem máximo 20 caracteres no título
        foreach ($payload['interactive']['action']['buttons'] as $button) {
            $this->assertLessThanOrEqual(20, strlen($button['reply']['title']));
        }
    }

    /** @test */
    public function list_message_supports_up_to_10_options()
    {
        $sections = [
            ListSectionFactory::create([
                'title' => 'Produtos',
                'rows' => array_map(fn($i) => ListRowFactory::create([
                    'id' => "product_{$i}",
                    'title' => "Produto {$i}",
                    'description' => "Descrição do produto {$i}"
                ]), range(1, 10))
            ])
        ];

        $interactive = InteractiveMessageFactory::create([
            'type' => InteractiveType::LIST,
            'sections' => $sections
        ]);

        $builder = new WhatsAppMessageBuilder();
        $payload = $builder->buildInteractiveListMessage($interactive, '+5511999999999');

        $totalRows = array_sum(array_map(fn($section) => count($section['rows']), $payload['interactive']['action']['sections']));
        $this->assertLessThanOrEqual(10, $totalRows);
    }
}
```

## 📝 Cronograma de Refatoração Completa

| Fase | Duração | Foco | Entregáveis |
|------|---------|------|-------------|
| **Fase 1** | 5-7 dias | Reestruturação de Domains | Domains refatorados, enums, migrations |
| **Fase 2** | 4-5 dias | Refatoração de Use Cases | Strategy pattern, builders, processors |
| **Fase 3** | 2-3 dias | Performance e Cache | Cache service, queue jobs, otimizações |
| **Fase 4** | 2-3 dias | Timeout e Cleanup | Timeout service, cron jobs, cleanup |
| **Fase 5** | 3-4 dias | Testes Completos | Testes de integração, compliance, performance |
| **Total** | **16-22 dias** | **Sistema 100% Funcional** | **ChatBot enterprise-ready** |

## 🧪 Plano de Testes Detalhado

### **Teste de Integração: Fluxo Completo**

#### Cenário: "Atendimento de Suporte Técnico"
```php
// tests/Feature/ChatBot/EndToEndFlowTest.php
public function test_complete_support_flow_with_timeout()
{
    // 1. Setup: Criar flow com 30 minutos de timeout
    $flow = FlowFactory::create([
        'inactivity_minutes' => 30,
        'ending_conversation_message' => 'Sua sessão expirou. Digite "oi" para começar novamente.'
    ]);
    
    // 2. Simular webhook inicial
    $webhookData = $this->createWebhookData('Preciso de ajuda');
    
    // 3. Processar e validar resposta
    $response = $this->chatBotService->processWebhook($webhookData);
    
    // 4. Simular interações do usuário
    $this->simulateUserInteraction('1', 'button_selection');
    $this->simulateUserInteraction('João Silva', 'text_input');
    
    // 5. Simular timeout (avançar tempo)
    Carbon::setTestNow(now()->addMinutes(31));
    
    // 6. Executar cleanup
    Artisan::call('chatbot:cleanup-conversations');
    
    // 7. Validar mensagem de encerramento enviada
    $this->assertMessageSent('Sua sessão expirou...');
    
    // 8. Validar conversa marcada como finished
    $conversation = $this->getConversation();
    $this->assertTrue($conversation->is_finished);
}
```

#### Estrutura do Teste:
1. **Setup de Dados**: Flow, PhoneNumber, Client
2. **Simulação de Webhook**: Dados reais do WhatsApp
3. **Interações Sequenciais**: Botões, inputs, navegação
4. **Timeout Simulation**: Manipulação de tempo
5. **Cleanup Execution**: Comando de limpeza
6. **Validações**: Mensagens, estados, logs

### **Casos de Teste Específicos**

#### 1. **Timeout Diferenciado por Flow**
- Flow A: 15 minutos
- Flow B: 60 minutos
- Flow C: 120 minutos
- Validar que cada um respeita seu timeout

#### 2. **Mensagens de Encerramento Personalizadas**
- Diferentes mensagens por flow
- Substituição de variáveis ({{client.name}})
- Validar envio correto

#### 3. **Navegação Condicional Complexa**
- Múltiplos níveis de condições
- Loops e retornos
- Validar fluxo correto

#### 4. **Recovery de Erros**
- Falha no envio de mensagem
- Timeout durante processamento
- Webhook malformado

## 📊 Métricas e Monitoramento

### **KPIs do Sistema**
1. **Taxa de Conclusão de Fluxos**: % de conversas que chegam ao fim
2. **Tempo Médio de Conversa**: Duração média das sessões
3. **Taxa de Timeout**: % de conversas que expiram
4. **Taxa de Erro**: % de falhas no processamento

### **Logs Estruturados**
```php
// Exemplo de log estruturado
Log::info('Conversation timeout processed', [
    'conversation_id' => $conversation->id,
    'flow_id' => $conversation->flow_id,
    'timeout_minutes' => $flow->inactivity_minutes,
    'last_interaction' => $lastInteraction->created_at,
    'ending_message_sent' => true,
    'processing_time_ms' => $processingTime
]);
```

## 🚀 Cronograma de Execução

| Fase | Duração | Entregáveis |
|------|---------|-------------|
| **Fase 1** | 2-3 dias | Domain Flow estendido, migrations, testes unitários |
| **Fase 2** | 3-4 dias | Timeout service melhorado, envio de mensagens |
| **Fase 3** | 1-2 dias | Cron job configurado, command melhorado |
| **Fase 4** | 4-5 dias | Testes de integração completos |
| **Total** | **10-14 dias** | Sistema 100% funcional com testes |

## ✅ Critérios de Aceitação

### **Sistema será considerado 100% funcional quando:**

1. ✅ **Timeout Configurável**: Cada flow pode ter seu próprio `inactivity_minutes`
2. ✅ **Mensagem de Encerramento**: Cada flow pode ter sua `ending_conversation_message`
3. ✅ **Cron Automático**: Cleanup executa automaticamente a cada 5 minutos
4. ✅ **Envio de Mensagem**: Sistema envia mensagem antes de encerrar conversa
5. ✅ **Testes Completos**: Cobertura de 90%+ em testes de integração
6. ✅ **Performance**: Processa 1000+ conversas em < 30 segundos
7. ✅ **Monitoramento**: Logs estruturados e métricas disponíveis
8. ✅ **Documentação**: Guias de uso e troubleshooting completos

## 🔧 Especificações Técnicas Detalhadas

### **1. Extensão do Domain Flow**

#### Migration:
```sql
-- database/migrations/add_timeout_fields_to_flows_table.php
ALTER TABLE flows
ADD COLUMN inactivity_minutes INT DEFAULT 60 COMMENT 'Timeout em minutos para conversas inativas',
ADD COLUMN ending_conversation_message TEXT COMMENT 'Mensagem enviada ao encerrar por timeout';
```

#### Domain Flow Atualizado:
```php
// app/Domains/ChatBot/Flow.php
public ?int $inactivity_minutes;
public ?string $ending_conversation_message;

public function __construct(
    // ... existing parameters
    ?int $inactivity_minutes = 60,
    ?string $ending_conversation_message = null,
    // ... rest of parameters
) {
    // ... existing assignments
    $this->inactivity_minutes = $inactivity_minutes;
    $this->ending_conversation_message = $ending_conversation_message;
}

public function getTimeoutMinutes(): int
{
    return $this->inactivity_minutes ?? 60; // Default 1 hour
}

public function getEndingMessage(): string
{
    return $this->ending_conversation_message ?? 'Sua conversa foi encerrada por inatividade.';
}
```

### **2. ConversationTimeoutService Melhorado**

```php
// app/Services/Meta/WhatsApp/ChatBot/Services/ConversationTimeoutService.php

public function findTimedOutConversationsByFlow(): array
{
    $conversations = $this->conversationRepository->findActiveConversationsWithFlow();
    $timedOut = [];

    foreach ($conversations as $conversation) {
        if ($this->isConversationTimedOut($conversation)) {
            $timedOut[] = $conversation;
        }
    }

    return $timedOut;
}

private function isConversationTimedOut(Conversation $conversation): bool
{
    if (!$conversation->flow || $conversation->is_finished) {
        return false;
    }

    $timeoutMinutes = $conversation->flow->getTimeoutMinutes();
    $lastActivity = $this->getLastActivityTime($conversation);

    return $lastActivity->addMinutes($timeoutMinutes)->isPast();
}

public function processTimeoutsWithMessages(): array
{
    $timedOutConversations = $this->findTimedOutConversationsByFlow();
    $processed = [];

    foreach ($timedOutConversations as $conversation) {
        try {
            $result = $this->handleTimeoutWithMessage($conversation);
            $processed[] = $result;
        } catch (\Exception $e) {
            Log::error('Failed to process timeout for conversation', [
                'conversation_id' => $conversation->id,
                'error' => $e->getMessage()
            ]);
        }
    }

    return $processed;
}

private function handleTimeoutWithMessage(Conversation $conversation): array
{
    // 1. Enviar mensagem de encerramento
    $endingMessage = $this->prepareEndingMessage($conversation);
    $messageSent = $this->sendEndingMessage($conversation, $endingMessage);

    // 2. Marcar conversa como finalizada
    $conversation->is_finished = true;
    $conversation->updated_at = now();

    // 3. Adicionar informações de timeout
    $conversationData = $conversation->json ? json_decode($conversation->json, true) : [];
    $conversationData['timeout_info'] = [
        'timed_out_at' => now()->toISOString(),
        'reason' => 'flow_timeout',
        'timeout_minutes' => $conversation->flow->getTimeoutMinutes(),
        'ending_message_sent' => $messageSent,
        'ending_message' => $endingMessage,
    ];
    $conversation->json = json_encode($conversationData);

    // 4. Salvar conversa
    $this->conversationRepository->save($conversation);

    return [
        'conversation_id' => $conversation->id,
        'flow_id' => $conversation->flow_id,
        'timeout_minutes' => $conversation->flow->getTimeoutMinutes(),
        'message_sent' => $messageSent,
        'processed_at' => now()->toISOString()
    ];
}
```

### **3. Command Melhorado**

```php
// app/Console/Commands/ChatBot/CleanupInactiveConversations.php

public function handle(): int
{
    $this->info('Starting ChatBot conversation timeout processing...');

    try {
        // Processar timeouts específicos por flow
        $result = $this->conversationTimeoutService->processTimeoutsWithMessages();

        $this->displayDetailedResults($result);

        return Command::SUCCESS;
    } catch (\Exception $e) {
        $this->error('Timeout processing failed: ' . $e->getMessage());
        return Command::FAILURE;
    }
}

private function displayDetailedResults(array $results): void
{
    $this->info("Timeout processing completed:");
    $this->line("- Conversations processed: " . count($results));

    $groupedByFlow = collect($results)->groupBy('flow_id');

    foreach ($groupedByFlow as $flowId => $flowResults) {
        $this->line("  Flow {$flowId}: " . count($flowResults) . " conversations");
    }

    $messagesSent = collect($results)->where('message_sent', true)->count();
    $this->line("- Ending messages sent: {$messagesSent}");
}
```

### **4. Agendamento no Kernel**

```php
// app/Console/Kernel.php
protected function schedule(Schedule $schedule): void
{
    // ... existing schedules

    // ChatBot conversation timeout processing
    $schedule->command('chatbot:cleanup-conversations')
        ->everyFiveMinutes()
        ->withoutOverlapping()
        ->runInBackground()
        ->appendOutputTo(storage_path('logs/chatbot-cleanup.log'));
}
```

---

## 🚨 RECOMENDAÇÕES CRÍTICAS

### **DECISÃO ARQUITETURAL NECESSÁRIA**

Baseado na análise profunda dos domains, use cases e comparação com a documentação do Meta WhatsApp, o sistema atual tem **problemas estruturais graves** que impedem que seja considerado "100% funcional" apenas com melhorias incrementais.

#### **Opção 1: Refatoração Completa (RECOMENDADA)**
- **Duração**: 16-22 dias
- **Benefícios**: Sistema enterprise-ready, escalável, conforme WhatsApp API
- **Riscos**: Maior investimento inicial, possível quebra de compatibilidade

#### **Opção 2: Melhorias Incrementais (NÃO RECOMENDADA)**
- **Duração**: 10-14 dias
- **Benefícios**: Menor risco, mudanças graduais
- **Problemas**: Mantém dívida técnica, limitações de escalabilidade

### **PROBLEMAS CRÍTICOS IDENTIFICADOS**

#### **1. Domains Não Conformes com WhatsApp API**
- ❌ Button types incorretos (`QUICK_REPLY` vs `reply`)
- ❌ Ausência de List Messages (até 10 opções)
- ❌ Falta suporte a Call-to-Action buttons
- ❌ Interactive Messages não seguem spec oficial

#### **2. Arquitetura de Steps Problemática**
- ❌ Múltiplos booleanos confusos (`is_message`, `is_interactive`, etc.)
- ❌ Navegação condicional primitiva (apenas `next_step`)
- ❌ Configuração em JSON genérico sem validação
- ❌ Ausência de timeout por step

#### **3. Use Cases Ineficientes**
- ❌ `ProcessFlowStep` monolítico (200+ linhas)
- ❌ Ausência de Strategy Pattern para diferentes tipos
- ❌ Falta cache para flows/steps (N+1 queries)
- ❌ Processamento síncrono (não escalável)

#### **4. Falta Compliance Empresarial**
- ❌ Ausência de versionamento de flows
- ❌ Sem rollback para conversas ativas
- ❌ Logs não estruturados
- ❌ Ausência de métricas de performance

### **RECOMENDAÇÃO FINAL**

**RECOMENDO FORTEMENTE a Refatoração Completa** pelos seguintes motivos:

1. **Dívida Técnica**: O sistema atual tem dívida técnica significativa
2. **Escalabilidade**: Arquitetura atual não suporta crescimento
3. **Compliance**: Não está conforme WhatsApp Business API
4. **Manutenibilidade**: Código atual é difícil de manter e estender

### **Próximos Passos Sugeridos:**

1. **Aprovação da Refatoração Completa** (16-22 dias)
2. **Setup de Ambiente de Desenvolvimento** paralelo
3. **Migração Gradual** com feature flags
4. **Testes Extensivos** antes do deploy
5. **Rollback Plan** caso necessário

---

**A refatoração transformará o ChatBot de um MVP básico em um sistema enterprise-ready, escalável e conforme às melhores práticas da indústria.**

**Documentos de Apoio:**
- `storage/docs/ChatBot-Analise-Problemas-Criticos.md` - Análise detalhada dos problemas
- `storage/docs/ChatBot-Plano-Testes-Integracao.md` - Plano completo de testes
