# ChatBot - Templates para Jira

## 🎯 Epic Principal

**Título:** EPIC: Refatoração Completa do Sistema ChatBot  
**Tipo:** Epic  
**Prioridade:** High  
**Componente:** ChatBot  

**Descrição:**
```
Transformar o ChatBot de MVP básico em sistema enterprise-ready, escalável e conforme às melhores práticas da indústria.

## Objetivos Principais
• Compliance 100% com WhatsApp Business API
• Arquitetura escalável com Strategy Pattern  
• Performance otimizada com cache e queue
• Timeout configurável por flow e step
• Navegação condicional robusta

## Problemas Atuais Identificados
• Button types incorretos (QUICK_REPLY vs reply)
• Ausência de List Messages (até 10 opções)
• Arquitetura de Steps problemática (múltiplos booleanos)
• Use Cases ineficientes (ProcessFlowStep monolítico)
• Falta compliance empresarial (versionamento, logs)

## Fases do Projeto
1. Reestruturação de Domains (5-7 dias)
2. Refatoração de Use Cases (4-5 dias)
3. Performance e Cache (2-3 dias)
4. Timeout e Cleanup (2-3 dias)
5. Testes e Validação (3-4 dias)

## Estimativa Total
16-22 dias de desenvolvimento

## Documentação de Apoio
• storage/docs/ChatBot-Status-e-Plano-Completo.md
• storage/docs/ChatBot-Analise-Problemas-Criticos.md
• storage/docs/ChatBot-Plano-Testes-Integracao.md
```

---

## 📋 TICKET-001: Criar Enums e Value Objects

**Título:** Criar Enums e Value Objects para ChatBot  
**Tipo:** Story  
**Prioridade:** High  
**Componente:** ChatBot/Domains  
**Epic Link:** [Link para Epic criado]  
**Estimativa:** 2 dias  

**Descrição:**
```
Criar enums tipados e value objects para substituir constantes string e melhorar type safety do sistema ChatBot.

## Problema Atual
O sistema usa constantes string que causam:
• Erros de digitação não detectados
• Falta de validação de tipos
• Dificuldade de manutenção
• Inconsistências entre diferentes partes do código

## Solução Proposta
Implementar enums tipados para todos os tipos de dados categóricos.

## Tarefas Técnicas
□ Criar StepType enum (MESSAGE, INTERACTIVE, INPUT, COMMAND, CONDITION, WEBHOOK, DELAY)
□ Criar WhatsAppButtonType enum (REPLY, URL, PHONE_NUMBER, COPY_CODE, FLOW)
□ Criar InteractiveType enum (BUTTON, LIST, FLOW)
□ Criar FlowStatus enum (DRAFT, ACTIVE, ARCHIVED)
□ Criar ComponentFormat enum (TEXT, IMAGE, VIDEO, DOCUMENT)
□ Implementar métodos de validação em cada enum
□ Criar testes unitários para todos os enums
□ Atualizar documentação PHPDoc

## Arquivos Afetados
• app/Enums/ChatBot/StepType.php (novo)
• app/Enums/ChatBot/WhatsAppButtonType.php (novo)
• app/Enums/ChatBot/InteractiveType.php (novo)
• app/Enums/ChatBot/FlowStatus.php (novo)
• app/Enums/ChatBot/ComponentFormat.php (novo)
• tests/Unit/Enums/ChatBot/ (novos testes)
```

**Acceptance Criteria:**
```
□ Todos os enums criados com valores conformes à WhatsApp Business API
□ Enums implementam métodos de validação e conversão
□ Testes unitários para cada enum com 100% de cobertura
□ Documentação PHPDoc completa para todos os enums
□ Backward compatibility mantida durante transição
□ Enums seguem padrão PSR-12 de código
□ Métodos toArray() e fromString() implementados onde necessário
```

**Definição de Pronto:**
```
□ Código revisado e aprovado por tech lead
□ Todos os testes passando no CI/CD
□ Documentação atualizada
□ Sem breaking changes introduzidos
□ Performance não degradada
```

---

## 📋 TICKET-002: Refatorar Step Domain

**Título:** Refatorar Step Domain com Nova Arquitetura  
**Tipo:** Story  
**Prioridade:** High  
**Componente:** ChatBot/Domains  
**Epic Link:** [Link para Epic criado]  
**Estimativa:** 3 dias  
**Dependências:** TICKET-001  

**Descrição:**
```
Refatorar completamente o Step Domain eliminando múltiplos booleanos confusos e implementando navegação condicional robusta.

## Problema Atual
• Múltiplos booleanos confusos (is_message, is_interactive, is_command, is_input)
• Navegação primitiva (apenas next_step e earlier_step)
• Configuração em JSON genérico sem validação
• Ausência de timeout por step

## Solução Proposta
• Usar StepType enum ao invés de múltiplos booleanos
• Implementar sistema de navegação condicional
• Configuração tipada por tipo de step
• Timeout específico por step

## Tarefas Técnicas
□ Remover campos booleanos (is_message, is_interactive, is_command, is_input)
□ Adicionar campo type usando StepType enum
□ Implementar configuration array tipado por tipo de step
□ Adicionar navigation_rules para navegação condicional
□ Adicionar timeout_seconds para timeout específico por step
□ Criar métodos de validação de configuração por tipo
□ Criar migration para converter dados existentes
□ Atualizar Factory para nova estrutura
□ Atualizar Repository com novos métodos
□ Implementar método getNextStep() com lógica condicional

## Arquivos Afetados
• app/Domains/ChatBot/Step.php (refatorado)
• database/migrations/refactor_steps_table.php (novo)
• app/Factories/ChatBot/StepFactory.php (atualizado)
• app/Repositories/StepRepository.php (atualizado)
• tests/Unit/Domains/ChatBot/StepTest.php (atualizado)
```

**Acceptance Criteria:**
```
□ Step Domain usa apenas StepType enum ao invés de múltiplos booleanos
□ Configuração tipada implementada para cada tipo de step
□ Sistema de navegação condicional funcional com múltiplas regras
□ Timeout por step implementado e testado
□ Migration criada para converter dados existentes sem perda
□ Factory atualizada para nova estrutura
□ Repository atualizado com novos métodos de busca
□ Testes unitários cobrindo todos os cenários de navegação
□ Método getNextStep() implementado com lógica condicional
□ Validação de configuração por tipo funcionando
□ Backward compatibility durante migração
```

**Definição de Pronto:**
```
□ Migration executada com sucesso em ambiente de teste
□ Todos os testes unitários e integração passando
□ Dados existentes migrados corretamente
□ Performance mantida ou melhorada
□ Documentação atualizada
□ Code review aprovado
```

---

## 📋 TICKET-007: Strategy Pattern para Step Processors

**Título:** Implementar Strategy Pattern para Step Processors  
**Tipo:** Story  
**Prioridade:** High  
**Componente:** ChatBot/UseCases  
**Epic Link:** [Link para Epic criado]  
**Estimativa:** 3 dias  
**Dependências:** TICKET-002, TICKET-003, TICKET-005  

**Descrição:**
```
Refatorar ProcessFlowStep monolítico implementando Strategy Pattern com processors específicos para cada tipo de step.

## Problema Atual
• ProcessFlowStep é monolítico com 200+ linhas
• Múltiplas responsabilidades em um só método
• Difícil de testar e manter
• Difícil de estender para novos tipos de step

## Solução Proposta
• Implementar Strategy Pattern
• Processor específico para cada tipo de step
• Interface clara e extensível
• Fácil adição de novos tipos

## Tarefas Técnicas
□ Criar StepProcessorInterface com método process()
□ Implementar MessageStepProcessor para steps de mensagem
□ Implementar InteractiveStepProcessor para steps interativos
□ Implementar InputStepProcessor para coleta de dados
□ Implementar CommandStepProcessor para comandos
□ Implementar ConditionStepProcessor para lógica condicional
□ Refatorar ProcessFlowStep para usar strategy pattern
□ Criar factory para processors
□ Implementar testes unitários para cada processor
□ Criar testes de integração do sistema completo

## Arquivos Afetados
• app/Services/Meta/WhatsApp/ChatBot/StepProcessors/StepProcessorInterface.php (novo)
• app/Services/Meta/WhatsApp/ChatBot/StepProcessors/MessageStepProcessor.php (novo)
• app/Services/Meta/WhatsApp/ChatBot/StepProcessors/InteractiveStepProcessor.php (novo)
• app/Services/Meta/WhatsApp/ChatBot/StepProcessors/InputStepProcessor.php (novo)
• app/Services/Meta/WhatsApp/ChatBot/StepProcessors/CommandStepProcessor.php (novo)
• app/Services/Meta/WhatsApp/ChatBot/StepProcessors/ConditionStepProcessor.php (novo)
• app/Services/Meta/WhatsApp/ChatBot/UseCases/ProcessFlowStep.php (refatorado)
• tests/Unit/Services/Meta/WhatsApp/ChatBot/StepProcessors/ (novos testes)
```

**Acceptance Criteria:**
```
□ StepProcessorInterface criada com contrato claro
□ Processor específico para cada tipo de step implementado
□ MessageStepProcessor processa mensagens simples corretamente
□ InteractiveStepProcessor gerencia botões e listas
□ InputStepProcessor coleta e valida dados do usuário
□ CommandStepProcessor executa comandos de negócio
□ ConditionStepProcessor avalia condições e navega
□ ProcessFlowStep refatorado usando strategy pattern
□ Factory de processors implementada
□ Testes unitários para cada processor com cobertura > 90%
□ Testes de integração do sistema completo
□ Performance mantida ou melhorada
□ Fácil extensão para novos tipos de step
```

**Definição de Pronto:**
```
□ Código limpo e modular
□ Responsabilidades bem definidas
□ Fácil extensão para novos tipos
□ Performance mantida ou melhorada
□ Todos os testes passando
□ Documentação completa
□ Code review aprovado
```

---

## 📋 TICKET-013: Testes de Compliance WhatsApp

**Título:** Implementar Testes de Compliance com WhatsApp API  
**Tipo:** Story  
**Prioridade:** High  
**Componente:** ChatBot/Tests  
**Epic Link:** [Link para Epic criado]  
**Estimativa:** 2 dias  
**Dependências:** TICKET-008  

**Descrição:**
```
Criar suite completa de testes para validar compliance 100% com WhatsApp Business API.

## Objetivo
Garantir que todas as mensagens geradas estejam 100% conformes com a especificação oficial do WhatsApp Business API.

## Tarefas Técnicas
□ Criar testes de payload para cada tipo de mensagem
□ Implementar testes de validação de limites
□ Criar testes de interactive messages (buttons e lists)
□ Implementar testes de template messages
□ Adicionar testes de error handling
□ Criar mocks para WhatsApp API
□ Implementar helpers para validação de payload
□ Adicionar testes de performance

## Arquivos Afetados
• tests/Feature/ChatBot/Integration/WhatsAppComplianceTest.php (novo)
• tests/Feature/ChatBot/Helpers/WhatsAppMockHelper.php (novo)
• tests/Feature/ChatBot/Helpers/PayloadValidator.php (novo)
```

**Acceptance Criteria:**
```
□ Testes de payload para text, interactive e template messages
□ Validação de limites (3 botões, 10 list items, 20 chars)
□ Testes de interactive button e list messages
□ Testes de template messages com parâmetros
□ Testes de error handling e fallbacks
□ Cobertura de testes > 90%
□ Testes executando em CI/CD
□ Documentação de casos de teste
□ Mocks adequados para WhatsApp API
□ Helpers para validação de payload
```

**Definição de Pronto:**
```
□ Compliance 100% validada
□ Testes passando consistentemente
□ Cobertura adequada
□ Documentação completa
□ Integração com CI/CD
```
