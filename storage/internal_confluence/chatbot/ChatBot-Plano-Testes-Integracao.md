# ChatBot - Plano Detalhado de Testes de Integração

## 🎯 Objetivo dos Testes

Criar uma suíte completa de testes de integração que valide o funcionamento end-to-end do sistema de chatbot, incluindo:
- Processamento completo de webhooks
- Navegação através de fluxos complexos
- Timeout e cleanup de conversas
- Envio de mensagens de encerramento
- Coleta de dados do usuário
- Navegação condicional

## 📁 Estrutura de Arquivos de Teste

```
tests/Feature/ChatBot/
├── EndToEndFlowTest.php              # Teste completo de fluxo
├── ConversationTimeoutTest.php       # Testes específicos de timeout
├── WebhookProcessingTest.php         # Processamento de webhooks
├── ConditionalNavigationTest.php     # Navegação condicional
├── InputCollectionTest.php           # Coleta de dados
├── MessageDeliveryTest.php           # Envio de mensagens
└── Helpers/
    ├── ChatBotTestHelper.php         # Helper para testes
    ├── WebhookDataBuilder.php        # Builder para dados de webhook
    └── FlowBuilder.php               # Builder para fluxos de teste
```

## 🧪 Teste Principal: Fluxo Completo de Atendimento

### **Cenário: "Suporte Técnico com Timeout Personalizado"**

```php
<?php

namespace Tests\Feature\ChatBot;

use Tests\TestCase;
use App\Domains\ChatBot\Flow;
use App\Domains\ChatBot\PhoneNumber;
use App\Domains\Inventory\Client;
use App\Services\Meta\WhatsApp\ChatBot\ChatBotService;
use Tests\Feature\ChatBot\Helpers\ChatBotTestHelper;
use Tests\Feature\ChatBot\Helpers\WebhookDataBuilder;
use Tests\Feature\ChatBot\Helpers\FlowBuilder;
use Carbon\Carbon;
use Illuminate\Support\Facades\Artisan;

class EndToEndFlowTest extends TestCase
{
    use ChatBotTestHelper;

    private ChatBotService $chatBotService;
    private Flow $supportFlow;
    private PhoneNumber $phoneNumber;
    private Client $client;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->chatBotService = app()->make(ChatBotService::class);
        
        // Setup test data
        $this->setupTestEnvironment();
    }

    /** @test */
    public function complete_support_flow_with_custom_timeout()
    {
        // 1. SETUP: Criar fluxo de suporte com timeout de 30 minutos
        $this->supportFlow = FlowBuilder::create()
            ->withName('Suporte Técnico')
            ->withInactivityMinutes(30)
            ->withEndingMessage('Sua sessão de suporte expirou. Digite "suporte" para começar novamente.')
            ->withSteps([
                // Step 1: Mensagem de boas-vindas
                [
                    'step' => 'welcome',
                    'is_initial_step' => true,
                    'is_message' => true,
                    'message' => 'Olá! Como posso ajudá-lo hoje?',
                    'buttons' => [
                        ['id' => 'problema_tecnico', 'title' => '🔧 Problema Técnico'],
                        ['id' => 'duvida_produto', 'title' => '❓ Dúvida sobre Produto'],
                        ['id' => 'cancelamento', 'title' => '❌ Cancelamento']
                    ]
                ],
                // Step 2: Coleta de nome (condicional)
                [
                    'step' => 'collect_name',
                    'is_input' => true,
                    'message' => 'Por favor, informe seu nome completo:',
                    'input_field' => 'client.name'
                ],
                // Step 3: Coleta de detalhes do problema
                [
                    'step' => 'collect_details',
                    'is_input' => true,
                    'message' => 'Descreva seu problema em detalhes:',
                    'input_field' => 'client.problem_description'
                ],
                // Step 4: Confirmação e finalização
                [
                    'step' => 'confirmation',
                    'is_message' => true,
                    'message' => 'Obrigado {{client.name}}! Seu chamado foi registrado. Nossa equipe entrará em contato em até 24h.'
                ]
            ])
            ->build();

        // 2. INÍCIO: Simular webhook inicial do usuário
        $initialWebhook = WebhookDataBuilder::create()
            ->fromPhoneNumber($this->phoneNumber)
            ->withMessage('suporte')
            ->withClientPhone('+5511999999999')
            ->build();

        $response1 = $this->chatBotService->processWebhook($initialWebhook);

        // Validar resposta inicial
        $this->assertTrue($response1['success']);
        $this->assertMessageSent('Olá! Como posso ajudá-lo hoje?');
        $this->assertButtonsSent(['🔧 Problema Técnico', '❓ Dúvida sobre Produto', '❌ Cancelamento']);

        // 3. INTERAÇÃO 1: Usuário seleciona "Problema Técnico"
        $buttonWebhook = WebhookDataBuilder::create()
            ->fromPhoneNumber($this->phoneNumber)
            ->withButtonSelection('problema_tecnico')
            ->withClientPhone('+5511999999999')
            ->build();

        $response2 = $this->chatBotService->processWebhook($buttonWebhook);

        // Validar navegação para coleta de nome
        $this->assertTrue($response2['success']);
        $this->assertMessageSent('Por favor, informe seu nome completo:');
        $this->assertCurrentStep('collect_name');

        // 4. INTERAÇÃO 2: Usuário informa nome
        $nameWebhook = WebhookDataBuilder::create()
            ->fromPhoneNumber($this->phoneNumber)
            ->withTextMessage('João Silva')
            ->withClientPhone('+5511999999999')
            ->build();

        $response3 = $this->chatBotService->processWebhook($nameWebhook);

        // Validar coleta de nome e navegação
        $this->assertTrue($response3['success']);
        $this->assertClientFieldUpdated('name', 'João Silva');
        $this->assertMessageSent('Descreva seu problema em detalhes:');
        $this->assertCurrentStep('collect_details');

        // 5. INTERAÇÃO 3: Usuário descreve problema
        $problemWebhook = WebhookDataBuilder::create()
            ->fromPhoneNumber($this->phoneNumber)
            ->withTextMessage('Não consigo acessar minha conta, aparece erro 500')
            ->withClientPhone('+5511999999999')
            ->build();

        $response4 = $this->chatBotService->processWebhook($problemWebhook);

        // Validar coleta de problema e finalização
        $this->assertTrue($response4['success']);
        $this->assertClientFieldUpdated('problem_description', 'Não consigo acessar minha conta, aparece erro 500');
        $this->assertMessageSent('Obrigado João Silva! Seu chamado foi registrado. Nossa equipe entrará em contato em até 24h.');
        $this->assertConversationFinished();

        // 6. TESTE DE TIMEOUT: Simular nova conversa que expira
        $this->startNewConversation();

        // Avançar tempo para 31 minutos (além do timeout de 30)
        Carbon::setTestNow(now()->addMinutes(31));

        // Executar comando de cleanup
        Artisan::call('chatbot:cleanup-conversations');

        // 7. VALIDAÇÕES DE TIMEOUT
        $this->assertTimeoutMessageSent('Sua sessão de suporte expirou. Digite "suporte" para começar novamente.');
        $this->assertConversationFinishedByTimeout();
        $this->assertTimeoutLogCreated();
    }

    /** @test */
    public function flow_with_conditional_navigation()
    {
        // Teste específico para navegação condicional baseada em botões
        $conditionalFlow = FlowBuilder::create()
            ->withConditionalNavigation([
                'problema_tecnico' => 'technical_support_flow',
                'duvida_produto' => 'product_info_flow',
                'cancelamento' => 'cancellation_flow'
            ])
            ->build();

        // Testar cada caminho condicional
        $this->testConditionalPath('problema_tecnico', 'technical_support_flow');
        $this->testConditionalPath('duvida_produto', 'product_info_flow');
        $this->testConditionalPath('cancelamento', 'cancellation_flow');
    }

    /** @test */
    public function multiple_flows_with_different_timeouts()
    {
        // Criar múltiplos fluxos com timeouts diferentes
        $quickFlow = FlowBuilder::create()->withInactivityMinutes(5)->build();
        $normalFlow = FlowBuilder::create()->withInactivityMinutes(30)->build();
        $longFlow = FlowBuilder::create()->withInactivityMinutes(120)->build();

        // Iniciar conversas em cada fluxo
        $quickConversation = $this->startConversationInFlow($quickFlow);
        $normalConversation = $this->startConversationInFlow($normalFlow);
        $longConversation = $this->startConversationInFlow($longFlow);

        // Avançar tempo para 10 minutos
        Carbon::setTestNow(now()->addMinutes(10));
        Artisan::call('chatbot:cleanup-conversations');

        // Validar que apenas quickFlow expirou
        $this->assertConversationTimedOut($quickConversation);
        $this->assertConversationActive($normalConversation);
        $this->assertConversationActive($longConversation);

        // Avançar para 35 minutos
        Carbon::setTestNow(now()->addMinutes(25)); // Total: 35 min
        Artisan::call('chatbot:cleanup-conversations');

        // Validar que normalFlow também expirou
        $this->assertConversationTimedOut($normalConversation);
        $this->assertConversationActive($longConversation);
    }

    /** @test */
    public function error_recovery_and_fallback()
    {
        // Testar recuperação de erros durante o fluxo
        $this->mockWhatsAppApiFailure();

        $webhook = WebhookDataBuilder::create()
            ->fromPhoneNumber($this->phoneNumber)
            ->withMessage('teste')
            ->build();

        $response = $this->chatBotService->processWebhook($webhook);

        // Validar que erro foi tratado adequadamente
        $this->assertFalse($response['success']);
        $this->assertErrorLogged();
        $this->assertFallbackMessageSent();
    }

    /** @test */
    public function performance_test_multiple_conversations()
    {
        // Teste de performance com múltiplas conversas simultâneas
        $startTime = microtime(true);

        // Criar 100 conversas simultâneas
        for ($i = 0; $i < 100; $i++) {
            $webhook = WebhookDataBuilder::create()
                ->fromPhoneNumber($this->phoneNumber)
                ->withMessage("teste_{$i}")
                ->withClientPhone("+5511999{$i:06d}")
                ->build();

            $this->chatBotService->processWebhook($webhook);
        }

        $processingTime = microtime(true) - $startTime;

        // Validar que processamento foi rápido (< 10 segundos)
        $this->assertLessThan(10, $processingTime);

        // Executar cleanup de todas as conversas
        Carbon::setTestNow(now()->addHours(2));
        
        $cleanupStartTime = microtime(true);
        Artisan::call('chatbot:cleanup-conversations');
        $cleanupTime = microtime(true) - $cleanupStartTime;

        // Validar que cleanup foi rápido (< 30 segundos)
        $this->assertLessThan(30, $cleanupTime);
    }

    // Helper methods
    private function setupTestEnvironment(): void
    {
        // Criar organização, phone number, etc.
        $this->phoneNumber = PhoneNumberFactory::create();
        $this->client = ClientFactory::create();
    }

    private function startNewConversation(): void
    {
        $webhook = WebhookDataBuilder::create()
            ->fromPhoneNumber($this->phoneNumber)
            ->withMessage('oi')
            ->withClientPhone('+5511888888888')
            ->build();

        $this->chatBotService->processWebhook($webhook);
    }

    private function testConditionalPath(string $buttonId, string $expectedStep): void
    {
        // Implementar teste de caminho condicional específico
    }

    private function startConversationInFlow(Flow $flow): Conversation
    {
        // Implementar início de conversa em fluxo específico
    }
}
```

## 🔧 Helpers de Teste

### **ChatBotTestHelper.php**
```php
<?php

namespace Tests\Feature\ChatBot\Helpers;

use App\Domains\ChatBot\Conversation;
use Illuminate\Support\Facades\Log;

trait ChatBotTestHelper
{
    protected function assertMessageSent(string $expectedMessage): void
    {
        // Verificar se mensagem foi enviada via WhatsApp API
        $this->assertTrue(
            $this->wasMessageSent($expectedMessage),
            "Expected message '{$expectedMessage}' was not sent"
        );
    }

    protected function assertButtonsSent(array $expectedButtons): void
    {
        // Verificar se botões foram enviados
        foreach ($expectedButtons as $button) {
            $this->assertTrue(
                $this->wasButtonSent($button),
                "Expected button '{$button}' was not sent"
            );
        }
    }

    protected function assertCurrentStep(string $expectedStep): void
    {
        $conversation = $this->getCurrentConversation();
        $this->assertEquals(
            $expectedStep,
            $conversation->current_step->step,
            "Expected current step to be '{$expectedStep}'"
        );
    }

    protected function assertClientFieldUpdated(string $field, string $value): void
    {
        $client = $this->getCurrentClient();
        $this->assertEquals(
            $value,
            $client->{$field},
            "Expected client field '{$field}' to be '{$value}'"
        );
    }

    protected function assertConversationFinished(): void
    {
        $conversation = $this->getCurrentConversation();
        $this->assertTrue(
            $conversation->is_finished,
            "Expected conversation to be finished"
        );
    }

    protected function assertTimeoutMessageSent(string $expectedMessage): void
    {
        // Verificar se mensagem de timeout foi enviada
        $this->assertMessageSent($expectedMessage);
    }

    protected function assertConversationFinishedByTimeout(): void
    {
        $conversation = $this->getCurrentConversation();
        $this->assertTrue($conversation->is_finished);
        
        $conversationData = json_decode($conversation->json, true);
        $this->assertEquals(
            'conversation_timeout',
            $conversationData['timeout_info']['reason'] ?? null
        );
    }

    protected function assertTimeoutLogCreated(): void
    {
        // Verificar se log de timeout foi criado
        Log::assertLogged('info', function ($message, $context) {
            return str_contains($message, 'Conversation timeout processed');
        });
    }

    // Métodos auxiliares privados
    private function wasMessageSent(string $message): bool
    {
        // Implementar verificação de envio de mensagem
        return true; // Placeholder
    }

    private function getCurrentConversation(): Conversation
    {
        // Implementar busca da conversa atual
        return new Conversation(); // Placeholder
    }
}
```

## 📊 Cobertura de Testes

### **Cenários Obrigatórios**

1. ✅ **Fluxo Completo Básico**
   - Webhook → Processamento → Resposta → Finalização

2. ✅ **Timeout por Flow**
   - Diferentes configurações de `inactivity_minutes`
   - Envio de `ending_conversation_message`

3. ✅ **Navegação Condicional**
   - Botões com `internal_type: 'condition'`
   - Múltiplos caminhos baseados em seleção

4. ✅ **Coleta de Input**
   - Campos `client.field`
   - Validação e persistência

5. ✅ **Cleanup Automático**
   - Cron job funcionando
   - Processamento em lote

6. ✅ **Recovery de Erros**
   - Falhas de API
   - Webhooks malformados
   - Timeouts de rede

7. ✅ **Performance**
   - Múltiplas conversas simultâneas
   - Cleanup de grandes volumes

### **Métricas de Sucesso**

- **Cobertura de Código**: > 90%
- **Tempo de Execução**: < 2 minutos para suíte completa
- **Performance**: 1000+ conversas processadas em < 30s
- **Confiabilidade**: 0% de falsos positivos

---

**Este plano garante que o sistema de chatbot seja testado de forma abrangente e confiável, cobrindo todos os cenários críticos de uso.**
