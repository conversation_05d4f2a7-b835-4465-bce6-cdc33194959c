{"flow": {"id": null, "organization_id": 1, "name": "Fluxo Pizzaria Completo", "description": "Fluxo completo para pedidos de pizza com novos campos expandidos", "is_active": true, "steps_count": 6}, "steps": [{"id": null, "step": "boas_vindas", "type": "interactive", "step_type": "INTERACTIVE", "position": 0, "is_initial_step": true, "is_ending_step": false, "is_input_required": false, "is_button_click_required": true, "is_moving_automatic": false, "component": {"id": null, "type": "interactive", "text": "🍕 Bem-vindo à Pizzaria Deliciosa! Como posso ajudá-lo hoje?", "buttons": [{"id": null, "text": "Fazer Pedido", "type": "reply", "internal_type": "action", "callback_data": "{\"action\": \"start_order\"}", "commands_to_run": ["initialize_order"], "step_to_go": "escolher_tamanho", "value": "new_order"}, {"id": null, "text": "<PERSON><PERSON>", "type": "reply", "internal_type": "action", "callback_data": "{\"action\": \"view_menu\"}", "commands_to_run": ["show_menu"], "step_to_go": "mostrar_cardapio", "value": "menu"}, {"id": null, "text": "Falar com Atendente", "type": "reply", "internal_type": "action", "callback_data": "{\"action\": \"human_support\"}", "commands_to_run": ["transfer_to_human"], "step_to_go": null, "value": "human"}]}}, {"id": null, "step": "escolher_tamanho", "type": "interactive", "step_type": "INTERACTIVE", "position": 1, "is_initial_step": false, "is_ending_step": false, "is_input_required": false, "is_button_click_required": true, "is_moving_automatic": false, "component": {"id": null, "type": "interactive", "text": "Perfeito! Vamos começar seu pedido. Qual tamanho de pizza você gostaria?", "buttons": [{"id": null, "text": "Pequena (25cm)", "type": "reply", "internal_type": "action", "callback_data": "{\"size\": \"small\", \"price\": 25.00}", "commands_to_run": ["set_pizza_size", "calculate_base_price"], "step_to_go": "escolher_sabor", "value": "P"}, {"id": null, "text": "Média (30cm)", "type": "reply", "internal_type": "action", "callback_data": "{\"size\": \"medium\", \"price\": 35.00}", "commands_to_run": ["set_pizza_size", "calculate_base_price"], "step_to_go": "escolher_sabor", "value": "M"}, {"id": null, "text": "Grande (35cm)", "type": "reply", "internal_type": "action", "callback_data": "{\"size\": \"large\", \"price\": 45.00}", "commands_to_run": ["set_pizza_size", "calculate_base_price"], "step_to_go": "escolher_sabor", "value": "G"}]}}, {"id": null, "step": "escolher_sabor", "type": "interactive", "step_type": "INTERACTIVE", "position": 2, "is_initial_step": false, "is_ending_step": false, "is_input_required": false, "is_button_click_required": true, "is_moving_automatic": false, "component": {"id": null, "type": "interactive", "text": "Ótima escolha! Agora escolha o sabor da sua pizza:", "buttons": [{"id": null, "text": "Margh<PERSON><PERSON>", "type": "reply", "internal_type": "action", "callback_data": "{\"flavor\": \"margherita\", \"ingredients\": [\"mozzarella\", \"tomate\", \"manjericão\"]}", "commands_to_run": ["set_pizza_flavor", "add_ingredients"], "step_to_go": "coletar_dados", "value": "marg<PERSON>ita"}, {"id": null, "text": "<PERSON><PERSON>", "type": "reply", "internal_type": "action", "callback_data": "{\"flavor\": \"pepperoni\", \"ingredients\": [\"mozzarella\", \"pepperoni\"]}", "commands_to_run": ["set_pizza_flavor", "add_ingredients"], "step_to_go": "coletar_dados", "value": "pepperoni"}, {"id": null, "text": "Quatro <PERSON>", "type": "reply", "internal_type": "action", "callback_data": "{\"flavor\": \"four_cheese\", \"ingredients\": [\"mozzarella\", \"gorgonzola\", \"parmesão\", \"provolone\"]}", "commands_to_run": ["set_pizza_flavor", "add_ingredients"], "step_to_go": "coletar_dados", "value": "four_cheese"}]}}, {"id": null, "step": "coletar_dados", "type": "input", "step_type": "INPUT", "position": 3, "is_initial_step": false, "is_ending_step": false, "is_input_required": true, "is_button_click_required": false, "is_moving_automatic": false, "input": "client.name", "component": {"id": null, "type": "text", "text": "Perfeito! Para finalizar seu pedido, preciso do seu nome completo:"}}, {"id": null, "step": "coletar_telefone", "type": "input", "step_type": "INPUT", "position": 4, "is_initial_step": false, "is_ending_step": false, "is_input_required": true, "is_button_click_required": false, "is_moving_automatic": false, "input": "client.phone", "component": {"id": null, "type": "text", "text": "<PERSON><PERSON><PERSON> {{client.name}}! Agora preciso do seu telefone para contato:"}}, {"id": null, "step": "confirmacao_pedido", "type": "interactive", "step_type": "INTERACTIVE", "position": 5, "is_initial_step": false, "is_ending_step": true, "is_input_required": false, "is_button_click_required": true, "is_moving_automatic": false, "component": {"id": null, "type": "interactive", "text": "Resumo do seu pedido:\n\n🍕 Pizza {{pizza.flavor}} - <PERSON><PERSON><PERSON> {{pizza.size}}\n💰 Valor: R$ {{pizza.price}}\n👤 Nome: {{client.name}}\n📞 Telefone: {{client.phone}}\n\nConfirma o pedido?", "buttons": [{"id": null, "text": "✅ Confirmar <PERSON>", "type": "reply", "internal_type": "action", "callback_data": "{\"action\": \"confirm_order\"}", "commands_to_run": ["confirm_order", "send_to_kitchen", "notify_customer"], "step_to_go": null, "value": "confirmed"}, {"id": null, "text": "❌ Cancelar", "type": "reply", "internal_type": "action", "callback_data": "{\"action\": \"cancel_order\"}", "commands_to_run": ["cancel_order", "clear_session"], "step_to_go": "boas_vindas", "value": "cancelled"}, {"id": null, "text": "🔄 Alterar Pedido", "type": "reply", "internal_type": "action", "callback_data": "{\"action\": \"modify_order\"}", "commands_to_run": ["reset_order"], "step_to_go": "escolher_tamanho", "value": "modify"}]}}], "conversation_raw_data_example": {"pizza": {"size": "M", "flavor": "marg<PERSON>ita", "price": 35.0, "ingredients": ["mozzarella", "tomate", "manjericão"]}, "client": {"name": "<PERSON>", "phone": "(11) 99999-9999"}, "order": {"status": "confirmed", "created_at": "2025-09-22T10:30:00Z", "estimated_delivery": "45 minutes"}}, "interaction_examples": [{"is_button_click": true, "button_click_id": 1, "is_raw_message": false, "raw_data": "{\"action\": \"start_order\", \"step\": \"boas_vindas\"}"}, {"is_button_click": false, "button_click_id": null, "is_raw_message": true, "raw_data": "{\"input_type\": \"text\", \"field\": \"client.name\", \"value\": \"<PERSON>\"}"}]}