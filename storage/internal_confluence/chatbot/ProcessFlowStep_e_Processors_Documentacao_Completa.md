# Documentação Completa: ProcessFlowStep e Processors

## Visão Geral

O sistema de ChatBot do WhatsApp utiliza uma arquitetura baseada em **Strategy Pattern** para processar diferentes tipos de steps (passos) em um fluxo de conversação. O componente central é o `ProcessFlowStep` que coordena todo o processamento, utilizando diferentes **Processors** especializados para cada tipo de step.

## Arquitetura Principal

### 1. ProcessFlowStep (Use Case Principal)

**Localização**: `app/Services/Meta/WhatsApp/ChatBot/UseCases/ProcessFlowStep.php`

O `ProcessFlowStep` é o **orquestrador principal** que:

1. **Recebe** uma interação do usuário (webhook do WhatsApp)
2. **Identifica** o step atual da conversa
3. **Delega** o processamento para o processor apropriado
4. **Aplica** navegação condicional se necessário
5. **Retorna** o resultado final com próximo step

#### Fluxo de Execução:

```php
public function perform(Conversation $conversation, array $messageData, Flow $flow): array
{
    // 1. Salva a interação
    $interaction = $this->interactionRepository->save(
        $this->interactionFactory->buildFromWebhookData($messageData, $conversation)
    );

    // 2. Obtém o step atual
    $currentStep = $conversation->current_step;

    // 3. Processa o step usando Strategy Pattern
    $stepResult = $this->processStepByType($currentStep, $interaction, $conversation);

    // 4. Aplica navegação condicional
    $stepResult = $this->applyConditionalNavigation($stepResult, $currentStep, $interaction);

    // 5. Salva resultado e retorna
    $interaction->result = json_encode($stepResult);
    $this->interactionRepository->save($interaction);
    
    return $stepResult;
}
```

### 2. processStepByType (Strategy Pattern)

**Método central** que implementa o Strategy Pattern:

```php
protected function processStepByType(Step $step, Interaction $interaction, Conversation $conversation): array
{
    try {
        // Obtém o processor apropriado via Factory
        $processor = $this->stepProcessorFactory->getProcessor($step);
        
        // Delega o processamento
        return $processor->process($step, $interaction, $conversation);
    } catch (\InvalidArgumentException $e) {
        // Fallback para processamento padrão
        return $this->processDefaultStep($step);
    }
}
```

### 3. applyConditionalNavigation (Navegação Inteligente)

**Método CRÍTICO** para navegação entre steps:

```php
protected function applyConditionalNavigation(array $stepResult, Step $currentStep, Interaction $interaction): array
{
    // Só aplica se deve mover para próximo step
    if (!($stepResult['move_to_next'] ?? false)) {
        return $stepResult;
    }

    // Verifica se step tem navegação condicional
    if (!$this->conditionalNavigationService->hasConditionalNavigation($currentStep)) {
        return $stepResult;
    }

    // Obtém próximo step baseado na interação do usuário
    $conditionalNextStep = $this->conditionalNavigationService->getNextStepForInteraction($currentStep, $interaction);

    if ($conditionalNextStep) {
        // Sobrescreve o próximo step com o target condicional
        $stepResult['next_step'] = $conditionalNextStep->id;
        $stepResult['conditional_navigation'] = true;
        $stepResult['target_step_identifier'] = $conditionalNextStep->step;
    }

    return $stepResult;
}
```

## StepProcessorFactory (Factory Pattern)

**Localização**: `app/Services/Meta/WhatsApp/ChatBot/Processors/StepProcessorFactory.php`

### Responsabilidades:

1. **Mapeia** tipos de step para seus processors
2. **Instancia** processors sob demanda
3. **Valida** se um processor pode processar um step
4. **Gerencia** registro de processors customizados

### Mapeamento de Processors:

```php
private array $processors = [
    StepType::MESSAGE->value => MessageStepProcessor::class,
    StepType::INTERACTIVE->value => InteractiveStepProcessor::class,
    StepType::INPUT->value => InputStepProcessor::class,
    StepType::COMMAND->value => CommandStepProcessor::class,
    StepType::CONDITION->value => ConditionStepProcessor::class,
    StepType::WEBHOOK->value => WebhookStepProcessor::class,
    StepType::DELAY->value => DelayStepProcessor::class,
];
```

### Método Principal:

```php
public function getProcessor(Step $step): StepProcessorInterface
{
    // Garante que step_type está definido
    $step->setStepTypeFromLegacyFields();
    
    if (!$step->step_type) {
        throw new \InvalidArgumentException("Step {$step->id} has no step_type defined");
    }
    
    return $this->getProcessorByType($step->step_type->value);
}
```

## Tipos de Step (StepType Enum)

**Localização**: `app/Enums/ChatBot/StepType.php`

### 7 Tipos Disponíveis:

1. **MESSAGE** - Exibir mensagem e avançar automaticamente
2. **INTERACTIVE** - Mostrar botões/listas e aguardar seleção
3. **INPUT** - Solicitar entrada de texto, validar e armazenar
4. **COMMAND** - Executar lógica de negócio (pedidos, cálculos)
5. **CONDITION** - Navegação condicional baseada na entrada do usuário
6. **WEBHOOK** - Chamar APIs ou serviços externos
7. **DELAY** - Aguardar tempo especificado antes de continuar

### Características dos Tipos:

```php
public function requiresUserInteraction(): bool
{
    return match($this) {
        self::INTERACTIVE, self::INPUT => true,
        self::MESSAGE, self::COMMAND, self::CONDITION, self::WEBHOOK, self::DELAY => false,
    };
}

public function canAutoAdvance(): bool
{
    return match($this) {
        self::MESSAGE, self::COMMAND, self::WEBHOOK, self::DELAY => true,
        self::INTERACTIVE, self::INPUT, self::CONDITION => false,
    };
}
```

## Processors Detalhados

### 1. MessageStepProcessor

**Propósito**: Processa steps de mensagem simples que avançam automaticamente.

**Comportamento**:
- Exibe mensagem ao usuário
- Avança automaticamente para próximo step
- Não aguarda interação do usuário

**Resultado**:
```php
return [
    'type' => 'message',
    'step_id' => $step->id,
    'action' => 'send_message',
    'message' => $message,
    'next_step' => $step->next_step,
    'move_to_next' => true, // Avança automaticamente
];
```

### 2. InteractiveStepProcessor

**Propósito**: Processa steps com botões ou listas interativas.

**Comportamento**:
- Mostra opções (botões/listas) ao usuário
- Aguarda seleção do usuário
- Processa a seleção quando recebida

**Resultado**:
```php
return [
    'type' => 'interactive',
    'step_id' => $step->id,
    'action' => 'show_options',
    'message' => $message,
    'next_step' => $step->next_step,
    'move_to_next' => true, // Após seleção
];
```

### 3. InputStepProcessor

**Propósito**: Coleta entrada de texto do usuário.

**Comportamento**:
- Solicita entrada de texto
- Valida entrada recebida
- Atualiza objetos de domínio via DynamicInputService
- Usa padrão `client.field` para atualizar dados

**Resultado (Solicitando)**:
```php
return [
    'type' => 'input',
    'step_id' => $step->id,
    'action' => 'request_input',
    'message' => $processedMessage,
    'move_to_next' => false, // Aguarda entrada
];
```

**Resultado (Processando)**:
```php
return [
    'type' => 'input',
    'step_id' => $step->id,
    'action' => 'input_processed',
    'input_result' => $inputResult,
    'next_step' => $step->next_step,
    'move_to_next' => true, // Após processar
];
```

### 4. CommandStepProcessor

**Propósito**: Executa lógica de negócio complexa.

**Comportamento**:
- Executa comandos via ExecuteCommand UseCase
- Pode atualizar dados do cliente
- Pode processar pedidos
- Pode fazer cálculos

**Resultado (Sucesso)**:
```php
return [
    'type' => 'command',
    'step_id' => $step->id,
    'action' => 'command_executed',
    'result' => $commandResult,
    'next_step' => $step->next_step,
    'move_to_next' => true,
    'success' => true,
];
```

**Resultado (Falha)**:
```php
return [
    'type' => 'command',
    'step_id' => $step->id,
    'action' => 'command_failed',
    'result' => ['success' => false, 'error' => $e->getMessage()],
    'next_step' => $step->next_step,
    'move_to_next' => false, // Não avança em falha
    'success' => false,
];
```
