# Documentação Completa - Sistema de Campanhas ChatBot

## Visão Geral

O sistema de campanhas ChatBot é uma plataforma robusta e flexível para criação, gestão e execução de campanhas de mensagens automatizadas. Suporta múltiplos canais (WhatsApp, Telegram, SMS) com funcionalidades avançadas de categorização, analytics, controle de status e sincronização.

## Arquitetura do Sistema

### Estrutura de Domínios

#### 1. **Campaign Domain** (`app/Domains/ChatBot/Campaign.php`)
Entidade central que representa uma campanha de mensagens.

**Propriedades Principais:**
- **Identificação:** `id`, `organization_id`, `user_id`, `name`, `description`
- **Configuração:** `template_id`, `phone_number_id`, `message_count`
- **Controle de Estado:** `status` (CampaignStatus enum), `is_scheduled`, `is_sent`, `is_sending`
- **Funcionalidades:** `is_direct_message` (mensagens sem template)
- **Timestamps:** `sent_at`, `scheduled_at`, `cancelled_at`, `failed_at`

**Relacionamentos:**
- `template` - Template utilizado na campanha
- `phone_number` - Número remetente
- `messages` - Mensagens individuais geradas
- `clients` - Destinatários da campanha
- `parameters` - Parâmetros dinâmicos
- `categories` - Categorias de organização
- `tags` - Tags de marcação livre
- `status_history` - Histórico de mudanças de status

**Métodos de Negócio:**
```php
getCurrentStatus(): CampaignStatus    // Status atual (enum ou derivado)
canCancel(): bool                     // Pode ser cancelada
canEdit(): bool                       // Pode ser editada
canLaunch(): bool                     // Pode ser lançada
updateStatus(CampaignStatus $status)  // Atualiza com histórico
hasFailedMessages(): bool             // Tem mensagens falhadas
```

#### 2. **Message Domain** (`app/Domains/ChatBot/Message.php`)
Representa uma mensagem individual dentro de uma campanha.

**Propriedades Principais:**
- **Identificação:** `id`, `organization_id`, `campaign_id`, `template_id`, `client_id`
- **Conteúdo:** `message` (texto processado com parâmetros)
- **Status:** `status` (MessageStatus enum), `is_sent`, `is_fail`, `is_read`
- **Retry Logic:** `delivery_attempts`, `max_retries`, `last_attempt_at`, `next_retry_at`
- **Erro:** `last_error_message`
- **Funcionalidades:** `is_direct_message`

**Métodos de Negócio:**
```php
send(): void                          // Marca como enviada
fail(): void                          // Marca como falhada
canRetry(): bool                      // Pode tentar reenvio
scheduleRetry(): void                 // Agenda próxima tentativa
toWhatsAppPayload(): array            // Gera payload para API
```

#### 3. **Template Domain** (`app/Domains/ChatBot/Template.php`)
Representa um template de mensagem com componentes dinâmicos.

**Propriedades Principais:**
- **Identificação:** `id`, `organization_id`, `name`, `category`, `language`
- **WhatsApp:** `library_template_name`, `id_external`, `status`
- **Configuração:** `parameter_format`, `phone_number_id`
- **Componentes:** `components` (array de Component domains)

#### 4. **Component Domain** (`app/Domains/ChatBot/Component.php`)
Representa um componente de template (header, body, footer, buttons).

**Propriedades:**
- `type` - Tipo do componente (HEADER, BODY, FOOTER, BUTTONS)
- `text` - Conteúdo textual
- `parameters` - Parâmetros dinâmicos
- `buttons` - Botões interativos

#### 5. **Category Domain** (`app/Domains/ChatBot/Category.php`)
Sistema de categorização para organização de campanhas.

**Propriedades:**
- `name`, `description`, `color` - Identificação visual
- `type` - Tipo de categoria (campaign, template, client)
- `organization_id` - Escopo organizacional

#### 6. **Tag Domain** (`app/Domains/ChatBot/Tag.php`)
Sistema de tags livres para marcação flexível.

**Propriedades:**
- `name` - Nome da tag
- `usage_count` - Contador de uso
- `organization_id` - Escopo organizacional

### Estrutura de Use Cases

#### Gestão de Campanhas

1. **Store** (`app/UseCases/ChatBot/Campaign/Store.php`)
   - **Função:** Cria nova campanha
   - **Validações:** Dados obrigatórios, template válido, organização
   - **Processo:** Valida → Cria domain → Persiste → Retorna

2. **Get** (`app/UseCases/ChatBot/Campaign/Get.php`)
   - **Função:** Busca campanha específica
   - **Validações:** Permissão de organização
   - **Processo:** Busca → Valida acesso → Retorna

3. **GetAll** (`app/UseCases/ChatBot/Campaign/GetAll.php`)
   - **Função:** Lista campanhas com filtros
   - **Filtros:** Status, categoria, tags, data, texto
   - **Processo:** Aplica filtros → Pagina → Retorna

4. **Update** (`app/UseCases/ChatBot/Campaign/Update.php`)
   - **Função:** Atualiza dados da campanha
   - **Validações:** Pode editar, dados válidos
   - **Processo:** Busca → Valida → Atualiza → Persiste

5. **Delete** (`app/UseCases/ChatBot/Campaign/Delete.php`)
   - **Função:** Remove campanha
   - **Validações:** Pode deletar, sem dependências críticas
   - **Processo:** Busca → Valida → Remove relacionamentos → Deleta

6. **Cancel** (`app/UseCases/ChatBot/Campaign/Cancel.php`)
   - **Função:** Cancela campanha em andamento
   - **Validações:** Pode cancelar, status válido
   - **Processo:** Busca → Valida → Atualiza status → Registra histórico

#### Gestão de Clientes

7. **AddClientsToCampaign** (`app/UseCases/ChatBot/Campaign/AddClientsToCampaign.php`)
   - **Função:** Adiciona clientes à campanha
   - **Validações:** Clientes válidos, sem duplicatas
   - **Processo:** Valida clientes → Adiciona relacionamentos → Atualiza contador

8. **RemoveClientFromCampaign** (`app/UseCases/ChatBot/Campaign/RemoveClientFromCampaign.php`)
   - **Função:** Remove cliente específico
   - **Processo:** Remove relacionamento → Atualiza contador

#### Execução de Campanhas

9. **LaunchCampaign** (`app/UseCases/ChatBot/Campaign/LaunchCampaign.php`)
   - **Função:** Inicia execução da campanha
   - **Validações:** Pode lançar, tem clientes, template válido
   - **Processo:** Valida → Gera mensagens → Atualiza status → Agenda envio

10. **GenerateMessages** (`app/UseCases/ChatBot/Campaign/GenerateMessages.php`)
    - **Função:** Gera mensagens individuais para cada cliente
    - **Processo:** 
      1. Busca template e componentes
      2. Para cada cliente: processa parâmetros dinâmicos
      3. Substitui variáveis ({{client.name}}, {{client.email}})
      4. Cria mensagem individual
      5. Persiste no banco

#### Gestão de Mensagens

11. **Send** (`app/UseCases/ChatBot/Message/Send.php`)
    - **Função:** Envia mensagem individual
    - **Processo:** Gera payload → Chama API → Atualiza status → Registra logs

12. **Resend** (`app/UseCases/ChatBot/Message/Resend.php`)
    - **Função:** Reenvia mensagem falhada
    - **Validações:** Pode reenviar, não excedeu tentativas
    - **Processo:** Verifica retry → Agenda reenvio → Atualiza contadores

13. **GetMessagesAvailableToSent** (`app/UseCases/ChatBot/Message/GetMessagesAvailableToSent.php`)
    - **Função:** Busca mensagens prontas para envio
    - **Filtros:** Status draft, agendamento, retry
    - **Processo:** Aplica filtros → Ordena por prioridade → Limita quantidade

#### Categorização e Tags

14. **Category/Store** (`app/UseCases/ChatBot/Category/Store.php`)
    - **Função:** Cria nova categoria
    - **Validações:** Nome único, tipo válido

15. **Campaign/AssignCategories** (`app/UseCases/ChatBot/Campaign/AssignCategories.php`)
    - **Função:** Atribui categorias à campanha
    - **Processo:** Valida categorias → Remove antigas → Adiciona novas

16. **Campaign/AssignTags** (`app/UseCases/ChatBot/Campaign/AssignTags.php`)
    - **Função:** Atribui tags à campanha
    - **Processo:** Cria tags inexistentes → Atualiza contadores → Associa

### Repositories e Factories

#### CampaignRepository (`app/Repositories/CampaignRepository.php`)

**Métodos CRUD:**
```php
store(Campaign $campaign): Campaign
update(Campaign $campaign, int $organization_id): Campaign
save(Campaign $campaign, int $organization_id): Campaign
fetchById(int $id): Campaign
fetchFullById(int $id): Campaign  // Com relacionamentos
delete(Campaign $campaign): bool
```

**Métodos de Busca:**
```php
getAll(CampaignFilters $filters, OrderBy $orderBy): array
fetchByStatus(CampaignStatus $status, int $organization_id): array
fetchByTemplate(int $template_id): array
fetchByClient(int $client_id): array
```

**Métodos de Estatística:**
```php
countByStatus(int $organization_id): array
getRecentCampaigns(int $organization_id, int $days): array
```

#### CampaignFactory (`app/Factories/ChatBot/CampaignFactory.php`)

**Métodos de Construção:**
```php
buildFromModel(CampaignModel $model, bool $loadTemplate = false, 
               bool $loadMessages = false, bool $loadClients = false): Campaign
buildFromStoreRequest(StoreRequest $request): Campaign
buildFromUpdateRequest(UpdateRequest $request): Campaign
buildCollection(iterable $models): array
```

**Dependências:**
- `TemplateFactory` - Para carregar templates
- `MessageFactory` - Para carregar mensagens
- `ClientFactory` - Para carregar clientes
- `CategoryFactory` - Para carregar categorias
- `TagFactory` - Para carregar tags

#### MessageRepository (`app/Repositories/MessageRepository.php`)

**Métodos Principais:**
```php
store(Message $message): Message
fetchById(int $id): Message
fetchByCampaign(int $campaign_id): array
fetchAvailableToSend(int $limit): array
fetchFailedMessages(int $campaign_id): array
updateStatus(Message $message): void
```

### API Endpoints

#### Campanhas - CRUD Base
```http
GET    /api/campaigns                 # Lista campanhas com filtros
POST   /api/campaigns                 # Cria nova campanha
GET    /api/campaigns/{id}            # Busca campanha específica
PUT    /api/campaigns/{id}            # Atualiza campanha
DELETE /api/campaigns/{id}            # Remove campanha
```

#### Campanhas - Gestão Avançada
```http
POST   /api/campaign/add-clients/{id}     # Adiciona clientes
POST   /api/campaign/remove-client/{id}   # Remove cliente
POST   /api/campaign/launch/{id}          # Lança campanha
GET    /api/campaign/{id}/clients         # Lista clientes
POST   /api/campaign/{id}/cancel          # Cancela campanha
GET    /api/campaign/{id}/status-history  # Histórico de status
```

#### Mensagens
```http
POST   /api/message/generate-messages/{campaign_id}  # Gera mensagens
GET    /api/campaign/{id}/messages                   # Lista mensagens
GET    /api/campaign/{id}/messages/failed            # Mensagens falhadas
GET    /api/campaign/{id}/messages/statistics        # Estatísticas
POST   /api/campaign/{id}/messages/resend-failed     # Reenvia falhadas
POST   /api/message/{id}/resend                      # Reenvia específica
GET    /api/message/{id}/delivery-status             # Status de entrega
```

#### Categorização
```http
GET    /api/categories                    # Lista categorias
POST   /api/categories                    # Cria categoria
PUT    /api/categories/{id}               # Atualiza categoria
DELETE /api/categories/{id}               # Remove categoria
POST   /api/campaign/{id}/assign-categories  # Atribui categorias
POST   /api/campaign/{id}/assign-tags        # Atribui tags
```

#### Tags
```http
GET    /api/tags                          # Lista tags
GET    /api/tags/most-used               # Tags mais usadas
GET    /api/tags/suggestions             # Sugestões de tags
```

### Fluxos de Negócio

#### 1. Criação Completa de Campanha
```
1. Usuário cria/seleciona template
2. Template é validado/publicado (se WhatsApp)
3. Usuário cria campanha:
   - Define nome, descrição
   - Seleciona template
   - Escolhe número remetente
   - Atribui categorias/tags
4. Sistema valida e persiste campanha
5. Usuário adiciona clientes destinatários
6. Sistema valida clientes e cria relacionamentos
```

#### 2. Lançamento e Execução
```
1. Usuário dispara lançamento
2. Sistema valida pré-condições:
   - Campanha pode ser lançada
   - Tem clientes associados
   - Template está válido
3. Sistema gera mensagens individuais:
   - Para cada cliente
   - Processa parâmetros dinâmicos
   - Substitui variáveis do template
4. Atualiza status para SENDING
5. Mensagens ficam prontas para envio
```

#### 3. Envio Automatizado
```
1. Cron job executa a cada minuto
2. Busca mensagens prontas para envio
3. Para cada mensagem:
   - Gera payload específico do canal
   - Chama API externa (WhatsApp/Telegram)
   - Atualiza status baseado na resposta
   - Registra logs de sucesso/erro
4. Aplica rate limiting por canal
5. Agenda retries para mensagens falhadas
```

#### 4. Sistema de Retry
```
1. Job identifica mensagens para retry
2. Verifica se não excedeu máximo de tentativas
3. Aplica backoff exponencial:
   - 1ª tentativa: 5 minutos
   - 2ª tentativa: 15 minutos  
   - 3ª tentativa: 1 hora
4. Reenvia mensagem
5. Marca como permanentemente falhada se esgotar tentativas
```

#### 5. Sincronização de Status
```
1. Job proativo identifica mensagens para verificação
2. Consulta APIs externas para status real
3. Compara com status local
4. Atualiza se houver divergência
5. Registra logs de sincronização
6. Atualiza métricas de campanha
```

### Regras de Negócio

#### Status de Campanhas
- **DRAFT:** Criada, pode ser editada livremente
- **SCHEDULED:** Agendada para envio futuro
- **SENDING:** Envio em andamento, não pode ser editada
- **COMPLETED:** Todas as mensagens processadas
- **FAILED:** Falha geral na execução
- **CANCELLED:** Cancelada pelo usuário

#### Transições de Status Permitidas
```
DRAFT → SCHEDULED → SENDING → COMPLETED
DRAFT → SENDING → COMPLETED
DRAFT → CANCELLED
SCHEDULED → CANCELLED
SENDING → CANCELLED
SENDING → FAILED
```

#### Sistema de Parâmetros Dinâmicos
- **Formato:** `{{client.campo}}` ou `{{parameter.nome}}`
- **Campos de Cliente:** name, email, phone, custom_fields
- **Parâmetros Customizados:** Definidos por campanha
- **Processamento:** Na geração das mensagens individuais

#### Controle de Acesso
- **Organização:** Todas as entidades são scoped por organization_id
- **Usuário:** Campanhas têm user_id do criador
- **Permissões:** Verificadas em todos os use cases

### Monitoramento e Analytics

#### Métricas Calculadas
- **Taxa de Entrega:** (enviadas / total) * 100
- **Taxa de Falha:** (falhadas / total) * 100
- **Taxa de Leitura:** (lidas / entregues) * 100
- **Taxa de Resposta:** (respostas / entregues) * 100
- **Tempo Médio de Entrega:** Média entre criação e entrega

#### Eventos de Engajamento
- **DELIVERED:** Mensagem entregue
- **READ:** Mensagem lida
- **REPLIED:** Usuário respondeu
- **CLICKED_BUTTON:** Clicou em botão
- **CLICKED_URL:** Clicou em link
- **FORWARDED:** Encaminhou mensagem

#### Snapshots Históricos
- **Frequência:** Diários
- **Conteúdo:** Métricas consolidadas por data
- **Uso:** Análise de evolução temporal

### Considerações Técnicas

#### Performance
- **Índices:** Otimizados para consultas frequentes
- **Paginação:** Implementada em todas as listagens
- **Cache:** Templates e configurações frequentes
- **Rate Limiting:** Por canal e organização

#### Escalabilidade
- **Jobs Assíncronos:** Processamento não-bloqueante
- **Batching:** Processamento em lotes
- **Timeout:** Controle de tempo de execução
- **Retry Logic:** Recuperação automática de falhas

#### Segurança
- **Validação:** Todos os inputs validados
- **Autorização:** Verificação de permissões
- **Sanitização:** Limpeza de dados de entrada
- **Logs:** Auditoria completa de ações

### Controllers e Requests

#### CampaignController (`app/Http/Controllers/ChatBot/CampaignController.php`)
Controller principal para gestão de campanhas.

**Métodos Principais:**
```php
index(Request $request): JsonResponse           // Lista campanhas
store(StoreRequest $request): JsonResponse      // Cria campanha
show(int $id, Request $request): JsonResponse   // Exibe campanha
update(int $id, UpdateRequest $request): JsonResponse // Atualiza
destroy(int $id, Request $request): JsonResponse // Remove
```

**Métodos Específicos:**
```php
addClients(int $id, AddClientsRequest $request): JsonResponse
removeClient(int $id, RemoveClientRequest $request): JsonResponse
launch(int $id, Request $request): JsonResponse
cancel(int $id, CancelRequest $request): JsonResponse
getStatusHistory(int $id, Request $request): JsonResponse
```

#### MessageController (`app/Http/Controllers/ChatBot/MessageController.php`)
Controller para gestão de mensagens.

**Métodos Principais:**
```php
getCampaignMessages(int $id, Request $request): JsonResponse
getFailedMessages(int $id, Request $request): JsonResponse
getStatistics(int $id, Request $request): JsonResponse
resendFailedByCampaign(int $id, ResendRequest $request): JsonResponse
resend(int $id, ResendRequest $request): JsonResponse
getDeliveryStatus(int $id, Request $request): JsonResponse
```

#### CategoryController (`app/Http/Controllers/ChatBot/CategoryController.php`)
Controller para gestão de categorias.

#### TagController (`app/Http/Controllers/ChatBot/TagController.php`)
Controller para gestão de tags.

### Request Classes

#### StoreRequest (`app/Http/Requests/Campaign/StoreRequest.php`)
Validação para criação de campanhas.

**Regras de Validação:**
```php
'name' => 'required|string|max:255'
'description' => 'nullable|string|max:1000'
'template_id' => 'required|exists:templates,id'
'phone_number_id' => 'required|exists:phone_numbers,id'
'is_direct_message' => 'boolean'
'scheduled_at' => 'nullable|date|after:now'
```

#### UpdateRequest (`app/Http/Requests/Campaign/UpdateRequest.php`)
Validação para atualização de campanhas.

#### AddClientsRequest (`app/Http/Requests/Campaign/AddClientsRequest.php`)
Validação para adição de clientes.

**Regras:**
```php
'client_ids' => 'required|array|min:1'
'client_ids.*' => 'required|integer|exists:clients,id'
```

### Middleware e Autenticação

#### OrganizationScope
Middleware que garante que usuários só acessem dados de sua organização.

#### CampaignPermissions
Middleware específico para verificar permissões de campanha.

### Eventos e Listeners

#### CampaignLaunched Event
Disparado quando uma campanha é lançada.

**Listeners:**
- `GenerateMessagesListener` - Gera mensagens automaticamente
- `NotifyUsersListener` - Notifica usuários sobre o lançamento
- `UpdateAnalyticsListener` - Atualiza métricas

#### MessageSent Event
Disparado quando uma mensagem é enviada.

#### CampaignCompleted Event
Disparado quando uma campanha é finalizada.

### Testes

#### Estrutura de Testes
```
tests/
├── Feature/
│   ├── ChatBot/
│   │   ├── CampaignTest.php
│   │   ├── MessageTest.php
│   │   ├── CategoryTest.php
│   │   └── TagTest.php
│   └── Services/
│       └── Meta/
│           └── WhatsApp/
│               └── CampaignFlowTest.php
├── Unit/
│   ├── Domains/
│   │   ├── CampaignTest.php
│   │   └── MessageTest.php
│   ├── UseCases/
│   │   └── Campaign/
│   │       ├── StoreTest.php
│   │       └── LaunchTest.php
│   └── Enums/
│       ├── CampaignStatusTest.php
│       └── MessageStatusTest.php
```

#### Factories para Testes
```php
CampaignFactory::class
MessageFactory::class
TemplateFactory::class
ClientFactory::class
CategoryFactory::class
TagFactory::class
```

### Comandos Artisan

#### Gestão de Campanhas
```bash
php artisan campaign:launch {id}              # Lança campanha
php artisan campaign:cancel {id}              # Cancela campanha
php artisan campaign:cleanup-old              # Limpa campanhas antigas
```

#### Envio de Mensagens
```bash
php artisan whatsapp:send-messages            # Envia mensagens pendentes
php artisan whatsapp:sync-status              # Sincroniza status
php artisan whatsapp:publish-templates        # Publica templates
```

#### Analytics
```bash
php artisan analytics:calculate               # Calcula métricas
php artisan analytics:create-snapshots        # Cria snapshots
php artisan analytics:export {campaign_id}    # Exporta relatório
```

### Configurações

#### Config Files
```php
config/whatsapp.php                           # Configurações WhatsApp
config/campaigns.php                          # Configurações de campanhas
config/analytics.php                          # Configurações de analytics
```

#### Exemplo de Configuração
```php
// config/campaigns.php
return [
    'default_max_retries' => 3,
    'retry_delays' => [5, 15, 60], // minutos
    'rate_limits' => [
        'whatsapp' => 500, // mensagens por minuto
        'telegram' => 30,
        'sms' => 100,
    ],
    'analytics' => [
        'snapshot_frequency' => 'daily',
        'retention_days' => 90,
    ],
];
```

Esta documentação serve como referência completa para o sistema de campanhas, cobrindo todos os aspectos arquiteturais, funcionais e técnicos necessários para desenvolvimento, manutenção e evolução da plataforma.
