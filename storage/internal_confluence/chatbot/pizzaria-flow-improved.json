{"flow": {"name": "Fluxo Pizzaria Melhorado", "description": "Fluxo completo para pedidos de pizza com navegação padronizada", "is_default_flow": false, "inactivity_minutes": 30, "ending_conversation_message": "O<PERSON><PERSON> por escolher nossa pizzaria! Seu pedido foi registrado.", "version": "2.0", "status": "active", "variables": {"pizza_size": null, "pizza_size_price": 0, "pizza_flavors": [], "total_price": 0, "client_name": null, "client_address": null}}, "steps": [{"step": "welcome", "step_type": "interactive", "position": 0, "next_step": 1, "is_initial_step": true, "is_ending_step": false, "component": {"name": "Welcome Component", "type": "interactive", "text": "🍕 Bem-vindo à nossa Pizzaria! {{client.name}}, vamos fazer seu pedido?", "format": "TEXT", "buttons": [{"text": "Fazer Pedido", "type": "reply", "internal_type": "NAVIGATION", "internal_data": "{\"type\":\"NAVIGATION\",\"target\":\"choose_size\"}", "callback_data": "{\"button_id\":\"start_order\",\"action_type\":\"NAVIGATION\",\"target\":\"choose_size\"}"}]}}, {"step": "choose_size", "step_type": "interactive", "position": 1, "next_step": 2, "earlier_step": 0, "is_initial_step": false, "is_ending_step": false, "component": {"name": "Size Selection Component", "type": "interactive", "text": "Escolha o tamanho da sua pizza:", "format": "TEXT", "buttons": [{"text": "P - R$ 25,00", "type": "reply", "internal_type": "VARIABLE", "internal_data": "{\"type\":\"VARIABLE\",\"target\":\"pizza_size\",\"data\":{\"size\":\"P\",\"price\":25}}", "callback_data": "{\"button_id\":\"size_p\",\"action_type\":\"VARIABLE\",\"target\":\"pizza_size\",\"variables\":{\"pizza_size\":\"P\",\"pizza_size_price\":25,\"total_price\":25}}"}, {"text": "M - R$ 35,00", "type": "reply", "internal_type": "VARIABLE", "internal_data": "{\"type\":\"VARIABLE\",\"target\":\"pizza_size\",\"data\":{\"size\":\"M\",\"price\":35}}", "callback_data": "{\"button_id\":\"size_m\",\"action_type\":\"VARIABLE\",\"target\":\"pizza_size\",\"variables\":{\"pizza_size\":\"M\",\"pizza_size_price\":35,\"total_price\":35}}"}, {"text": "G - R$ 45,00", "type": "reply", "internal_type": "VARIABLE", "internal_data": "{\"type\":\"VARIABLE\",\"target\":\"pizza_size\",\"data\":{\"size\":\"G\",\"price\":45}}", "callback_data": "{\"button_id\":\"size_g\",\"action_type\":\"VARIABLE\",\"target\":\"pizza_size\",\"variables\":{\"pizza_size\":\"G\",\"pizza_size_price\":45,\"total_price\":45}}"}, {"text": "F - R$ 65,00", "type": "reply", "internal_type": "VARIABLE", "internal_data": "{\"type\":\"VARIABLE\",\"target\":\"pizza_size\",\"data\":{\"size\":\"F\",\"price\":65}}", "callback_data": "{\"button_id\":\"size_f\",\"action_type\":\"VARIABLE\",\"target\":\"pizza_size\",\"variables\":{\"pizza_size\":\"F\",\"pizza_size_price\":65,\"total_price\":65}}"}]}}, {"step": "confirm_size", "step_type": "interactive", "position": 2, "next_step": 3, "earlier_step": 1, "is_initial_step": false, "is_ending_step": false, "component": {"name": "Confirm Size Component", "type": "interactive", "text": "Você escolheu pizza tamanho {{pizza.size}} por R$ {{pizza.price}}. Confirma?", "format": "TEXT", "buttons": [{"text": "Confirmar", "type": "reply", "internal_type": "NAVIGATION", "internal_data": "{\"type\":\"NAVIGATION\",\"target\":\"choose_first_flavor\"}", "callback_data": "{\"button_id\":\"confirm_size\",\"action_type\":\"NAVIGATION\",\"target\":\"choose_first_flavor\"}"}, {"text": "Voltar", "type": "reply", "internal_type": "NAVIGATION", "internal_data": "{\"type\":\"NAVIGATION\",\"target\":\"choose_size\"}", "callback_data": "{\"button_id\":\"back_to_size\",\"action_type\":\"NAVIGATION\",\"target\":\"choose_size\"}"}]}}, {"step": "choose_first_flavor", "step_type": "interactive", "position": 3, "next_step": 4, "earlier_step": 2, "is_initial_step": false, "is_ending_step": false, "component": {"name": "First Flavor Component", "type": "interactive", "text": "Escolha o primeiro sabor:", "format": "TEXT", "buttons": [{"text": "Calabresa", "type": "reply", "internal_type": "VARIABLE", "internal_data": "{\"type\":\"VARIABLE\",\"target\":\"pizza_flavors\",\"data\":{\"flavor\":\"Calabresa\"}}", "callback_data": "{\"button_id\":\"flavor_calabresa\",\"action_type\":\"VARIABLE\",\"target\":\"pizza_flavors\",\"variables\":{\"pizza_flavors\":[\"Calabresa\"]}}"}, {"text": "Portuguesa", "type": "reply", "internal_type": "VARIABLE", "internal_data": "{\"type\":\"VARIABLE\",\"target\":\"pizza_flavors\",\"data\":{\"flavor\":\"Portuguesa\"}}", "callback_data": "{\"button_id\":\"flavor_portuguesa\",\"action_type\":\"VARIABLE\",\"target\":\"pizza_flavors\",\"variables\":{\"pizza_flavors\":[\"Portuguesa\"]}}"}, {"text": "Frango", "type": "reply", "internal_type": "VARIABLE", "internal_data": "{\"type\":\"VARIABLE\",\"target\":\"pizza_flavors\",\"data\":{\"flavor\":\"Frango\"}}", "callback_data": "{\"button_id\":\"flavor_frango\",\"action_type\":\"VARIABLE\",\"target\":\"pizza_flavors\",\"variables\":{\"pizza_flavors\":[\"Frango\"]}}"}, {"text": "Chocolate", "type": "reply", "internal_type": "VARIABLE", "internal_data": "{\"type\":\"VARIABLE\",\"target\":\"pizza_flavors\",\"data\":{\"flavor\":\"Chocolate\"}}", "callback_data": "{\"button_id\":\"flavor_chocolate\",\"action_type\":\"VARIABLE\",\"target\":\"pizza_flavors\",\"variables\":{\"pizza_flavors\":[\"Chocolate\"]}}"}]}}, {"step": "choose_second_flavor_option", "step_type": "interactive", "position": 4, "next_step": 5, "earlier_step": 3, "is_initial_step": false, "is_ending_step": false, "component": {"name": "Second Flavor Option Component", "type": "interactive", "text": "Deseja adicionar um segundo sabor? (Opcional)", "format": "TEXT", "buttons": [{"text": "<PERSON><PERSON>, escolher outro sabor", "type": "reply", "internal_type": "NAVIGATION", "internal_data": "{\"type\":\"NAVIGATION\",\"target\":\"choose_second_flavor\"}", "callback_data": "{\"button_id\":\"add_second_flavor\",\"action_type\":\"NAVIGATION\",\"target\":\"choose_second_flavor\"}"}, {"text": "Não, continuar", "type": "reply", "internal_type": "NAVIGATION", "internal_data": "{\"type\":\"NAVIGATION\",\"target\":\"order_summary\"}", "callback_data": "{\"button_id\":\"skip_second_flavor\",\"action_type\":\"NAVIGATION\",\"target\":\"order_summary\"}"}]}}]}