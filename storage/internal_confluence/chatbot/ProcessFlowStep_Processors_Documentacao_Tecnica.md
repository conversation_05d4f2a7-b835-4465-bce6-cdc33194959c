# Documentação Técnica: ProcessFlowStep e Processors

## Visão Geral do Sistema

O sistema de ChatBot do WhatsApp utiliza uma arquitetura baseada no **Strategy Pattern** para processar diferentes tipos de steps (passos) em fluxos conversacionais. O componente central é o `ProcessFlowStep` que orquestra todo o processamento, delegando para **Processors** especializados.

## 1. ProcessFlowStep - Orquestrador Principal

**Localização**: `app/Services/Meta/WhatsApp/ChatBot/UseCases/ProcessFlowStep.php`

### Responsabilidades:
1. **Recebe** interações do webhook WhatsApp
2. **Identifica** o step atual da conversa
3. **Delega** processamento para processor apropriado
4. **Aplica** navegação condicional
5. **Retorna** resultado com próximo step

### Fluxo Principal:
```php
public function perform(Conversation $conversation, array $messageData, Flow $flow): array
{
    // 1. Salva interação
    $interaction = $this->interactionRepository->save(
        $this->interactionFactory->buildFromWebhookData($messageData, $conversation)
    );

    // 2. Obtém step atual
    $currentStep = $conversation->current_step;

    // 3. Processa via Strategy Pattern
    $stepResult = $this->processStepByType($currentStep, $interaction, $conversation);

    // 4. Aplica navegação condicional
    $stepResult = $this->applyConditionalNavigation($stepResult, $currentStep, $interaction);

    // 5. Salva e retorna resultado
    $interaction->result = json_encode($stepResult);
    $this->interactionRepository->save($interaction);
    
    return $stepResult;
}
```

## 2. processStepByType - Strategy Pattern

**Método central** que implementa o padrão Strategy:

```php
protected function processStepByType(Step $step, Interaction $interaction, Conversation $conversation): array
{
    try {
        // Obtém processor via Factory
        $processor = $this->stepProcessorFactory->getProcessor($step);
        
        // Delega processamento
        return $processor->process($step, $interaction, $conversation);
    } catch (\InvalidArgumentException $e) {
        // Fallback para processamento padrão
        return $this->processDefaultStep($step);
    }
}
```

## 3. applyConditionalNavigation - Navegação Inteligente

**Método CRÍTICO** para navegação entre steps:

```php
protected function applyConditionalNavigation(array $stepResult, Step $currentStep, Interaction $interaction): array
{
    // Só aplica se deve mover para próximo step
    if (!($stepResult['move_to_next'] ?? false)) {
        return $stepResult;
    }

    // Verifica se step tem navegação condicional
    if (!$this->conditionalNavigationService->hasConditionalNavigation($currentStep)) {
        return $stepResult;
    }

    // Obtém próximo step baseado na interação
    $conditionalNextStep = $this->conditionalNavigationService->getNextStepForInteraction($currentStep, $interaction);

    if ($conditionalNextStep) {
        // Sobrescreve próximo step
        $stepResult['next_step'] = $conditionalNextStep->id;
        $stepResult['conditional_navigation'] = true;
        $stepResult['target_step_identifier'] = $conditionalNextStep->step;
    }

    return $stepResult;
}
```

## 4. StepProcessorFactory - Factory Pattern

**Localização**: `app/Services/Meta/WhatsApp/ChatBot/Processors/StepProcessorFactory.php`

### Mapeamento de Processors:
```php
private array $processors = [
    StepType::MESSAGE->value => MessageStepProcessor::class,
    StepType::INTERACTIVE->value => InteractiveStepProcessor::class,
    StepType::INPUT->value => InputStepProcessor::class,
    StepType::COMMAND->value => CommandStepProcessor::class,
    StepType::CONDITION->value => ConditionStepProcessor::class,
    StepType::WEBHOOK->value => WebhookStepProcessor::class,
    StepType::DELAY->value => DelayStepProcessor::class,
];
```

### Método Principal:
```php
public function getProcessor(Step $step): StepProcessorInterface
{
    // Garante compatibilidade com sistema legado
    $step->setStepTypeFromLegacyFields();
    
    if (!$step->step_type) {
        throw new \InvalidArgumentException("Step {$step->id} has no step_type defined");
    }
    
    return $this->getProcessorByType($step->step_type->value);
}
```

## 5. Tipos de Step (StepType Enum)

**Localização**: `app/Enums/ChatBot/StepType.php`

### 7 Tipos Disponíveis:

1. **MESSAGE** - Exibir mensagem e avançar automaticamente
2. **INTERACTIVE** - Mostrar botões/listas e aguardar seleção  
3. **INPUT** - Solicitar entrada de texto, validar e armazenar
4. **COMMAND** - Executar lógica de negócio (pedidos, cálculos)
5. **CONDITION** - Navegação condicional baseada na entrada do usuário
6. **WEBHOOK** - Chamar APIs ou serviços externos
7. **DELAY** - Aguardar tempo especificado antes de continuar

### Características dos Tipos:
```php
public function requiresUserInteraction(): bool
{
    return match($this) {
        self::INTERACTIVE, self::INPUT => true,
        self::MESSAGE, self::COMMAND, self::CONDITION, self::WEBHOOK, self::DELAY => false,
    };
}

public function canAutoAdvance(): bool
{
    return match($this) {
        self::MESSAGE, self::COMMAND, self::WEBHOOK, self::DELAY => true,
        self::INTERACTIVE, self::INPUT, self::CONDITION => false,
    };
}
```

## 6. Processors Detalhados

### 6.1 MessageStepProcessor

**Propósito**: Processa steps de mensagem simples que avançam automaticamente.

**Comportamento**:
- Exibe mensagem ao usuário
- Avança automaticamente para próximo step
- Não aguarda interação do usuário

**Resultado**:
```php
return [
    'type' => 'message',
    'step_id' => $step->id,
    'action' => 'send_message',
    'message' => $message,
    'next_step' => $step->next_step,
    'move_to_next' => true, // Avança automaticamente
];
```

### 6.2 InteractiveStepProcessor

**Propósito**: Processa steps com botões ou listas interativas.

**Comportamento**:
- Mostra opções (botões/listas) ao usuário
- Aguarda seleção do usuário
- Processa a seleção quando recebida

**Resultado**:
```php
return [
    'type' => 'interactive',
    'step_id' => $step->id,
    'action' => 'show_options',
    'message' => $message,
    'next_step' => $step->next_step,
    'move_to_next' => true, // Após seleção
];
```

### 6.3 InputStepProcessor

**Propósito**: Coleta entrada de texto do usuário.

**Comportamento**:
- Solicita entrada de texto
- Valida entrada recebida
- Atualiza objetos de domínio via DynamicInputService
- Usa padrão `client.field` para atualizar dados

**Resultado (Solicitando)**:
```php
return [
    'type' => 'input',
    'step_id' => $step->id,
    'action' => 'request_input',
    'message' => $processedMessage,
    'move_to_next' => false, // Aguarda entrada
];
```

**Resultado (Processando)**:
```php
return [
    'type' => 'input',
    'step_id' => $step->id,
    'action' => 'input_processed',
    'input_result' => $inputResult,
    'next_step' => $step->next_step,
    'move_to_next' => true, // Após processar
];
```

### 6.4 CommandStepProcessor

**Propósito**: Executa lógica de negócio complexa.

**Comportamento**:
- Executa comandos via ExecuteCommand UseCase
- Pode atualizar dados do cliente
- Pode processar pedidos
- Pode fazer cálculos

**Resultado (Sucesso)**:
```php
return [
    'type' => 'command',
    'step_id' => $step->id,
    'action' => 'command_executed',
    'result' => $commandResult,
    'next_step' => $step->next_step,
    'move_to_next' => true,
    'success' => true,
];
```

**Resultado (Falha)**:
```php
return [
    'type' => 'command',
    'step_id' => $step->id,
    'action' => 'command_failed',
    'result' => ['success' => false, 'error' => $e->getMessage()],
    'next_step' => $step->next_step,
    'move_to_next' => false, // Não avança em falha
    'success' => false,
];
```

### 6.5 ConditionStepProcessor

**Propósito**: Processa steps com navegação condicional.

**Comportamento**:
- Exibe mensagem/botões condicionais
- Permite navegação baseada em regras
- **CRÍTICO**: `move_to_next => true` para permitir navegação condicional
- Trabalha em conjunto com ConditionalNavigationService

**Resultado**:
```php
return [
    'type' => 'condition',
    'step_id' => $step->id,
    'action' => 'show_condition_message',
    'message' => $message,
    'next_step' => $step->next_step, // Fallback padrão
    'move_to_next' => true, // CRÍTICO: Permite navegação condicional
    'has_buttons' => $this->stepHasButtons($step),
    'has_navigation_rules' => !empty($step->navigation_rules),
];
```

### 6.6 WebhookStepProcessor

**Propósito**: Faz chamadas HTTP para APIs externas.

**Comportamento**:
- Obtém configuração do webhook do step
- Faz chamada HTTP para endpoint externo
- Processa resposta
- Pode atualizar estado da conversa

**Resultado (Sucesso)**:
```php
return [
    'type' => 'webhook',
    'step_id' => $step->id,
    'action' => 'webhook_executed',
    'webhook_response' => $response,
    'next_step' => $step->next_step,
    'move_to_next' => true,
    'success' => true,
];
```

**Resultado (Falha)**:
```php
return [
    'type' => 'webhook',
    'step_id' => $step->id,
    'action' => 'webhook_failed',
    'error' => $e->getMessage(),
    'next_step' => $step->next_step,
    'move_to_next' => false, // Não avança em falha
    'success' => false,
];
```

### 6.7 DelayStepProcessor

**Propósito**: Adiciona delays temporais no fluxo.

**Comportamento**:
- Obtém duração do delay da configuração JSON
- Agenda processamento futuro
- Avança automaticamente após delay

**Configuração JSON**:
```json
{
    "duration_seconds": 30,
    "message": "Aguarde um momento..."
}
```

**Resultado**:
```php
return [
    'type' => 'delay',
    'step_id' => $step->id,
    'action' => 'delay_scheduled',
    'delay_seconds' => $delayConfig['duration_seconds'],
    'next_step' => $step->next_step,
    'move_to_next' => true, // Após delay
    'scheduled_at' => now()->addSeconds($delayConfig['duration_seconds']),
];
```
