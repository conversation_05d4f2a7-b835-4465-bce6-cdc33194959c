# 📧 Resend Email Integration - Release Guide

## ✅ Checklist de Deploy

### **1. Configuração da Conta Resend**
- [ ] Criar conta no [Resend](https://resend.com)
- [ ] Verificar domínio de email na plataforma Resend
- [ ] Gerar API Key no dashboard do Resend
- [ ] Configurar SPF/DKIM records no DNS (se necessário)

### **2. Configuração do Servidor**
- [ ] Adicionar variáveis de ambiente no `.env`:
  ```env
  RESEND_API_KEY="re_xxxxxxxxx_your_api_key_here"
  RESEND_FROM_EMAIL="<EMAIL>"
  RESEND_FROM_NAME="Obvio"
  RESEND_SUPPORT_EMAIL="<EMAIL>"
  RESEND_SANDBOX_MODE=false
  ```
- [ ] Verificar se as migrations estão atualizadas (`php artisan migrate:status`)
- [ ] Executar migrations se necessário (`php artisan migrate`)

### **3. Testes de Configuração**
- [ ] Executar testes unitários: `php artisan test tests/Unit/Services/Resend/`
- [ ] Executar testes de feature: `php artisan test tests/Feature/Services/Resend/`
- [ ] Verificar logs de aplicação para erros

### **4. Testes Manuais em Produção**
- [ ] Testar email de redefinição de senha
- [ ] Testar email de boas-vindas
- [ ] Testar email de relatório de campanha
- [ ] Testar email de fatura
- [ ] Verificar se emails chegam na caixa de entrada (não spam)
- [ ] Verificar se templates estão renderizando corretamente
- [ ] Verificar se logs estão sendo criados no banco

### **5. Monitoramento**
- [ ] Configurar alertas para falhas de email
- [ ] Monitorar logs de erro relacionados ao Resend
- [ ] Verificar métricas de entrega no dashboard do Resend
- [ ] Configurar backup de API key

### **6. Documentação**
- [ ] Atualizar documentação interna com novos endpoints
- [ ] Treinar equipe sobre novos tipos de email
- [ ] Documentar processo de troubleshooting

## 🚨 Rollback Plan

Se houver problemas:

1. **Desabilitar temporariamente**:
   ```env
   RESEND_SANDBOX_MODE=true
   ```

2. **Reverter para sistema anterior** (se aplicável):
   - Comentar código que usa Resend
   - Usar sistema de email anterior

3. **Verificar logs**:
   ```bash
   tail -f storage/logs/laravel.log | grep -i resend
   ```

## 📞 Contatos de Suporte

- **Resend Support**: [<EMAIL>](mailto:<EMAIL>)
- **Documentação**: https://resend.com/docs
- **Status Page**: https://status.resend.com

## 🔗 Links Úteis

- [Guia de Testes Manuais](./manual-testing-guide.md)
- [Troubleshooting](./troubleshooting-guide.md)
- [Configuração de DNS](./dns-configuration-guide.md)
