# 🚀 Password Reset System - Deployment Checklist

## 📋 Pre-Deployment Checklist

### ✅ Code Review
- [ ] **Todos os testes passando** (10/10 tests)
- [ ] **Code review aprovado** por pelo menos 2 desenvolvedores
- [ ] **Documentação atualizada** (README, API docs)
- [ ] **Logs de auditoria verificados** (DBLog funcionando)
- [ ] **Rate limiting testado** (3 tentativas/min)

### ✅ Dependencies
- [ ] **Resend Email Service** funcionando em produção
- [ ] **Database migrations** testadas em staging
- [ ] **Laravel Sanctum** funcionando corretamente
- [ ] **Queue system** configurado (se aplicável)
- [ ] **Cache system** funcionando (Redis/Memcached)

### ✅ Environment Configuration
- [ ] **RESEND_API_KEY** configurada
- [ ] **RESEND_FROM_EMAIL** configurada
- [ ] **APP_URL** correta para links de reset
- [ ] **Database** com estrutura atualizada
- [ ] **Logs** configurados corretamente

## 🔧 Staging Deployment

### 1. Database Migration
```bash
# Backup do banco atual
mysqldump -u user -p database > backup_pre_password_reset.sql

# Executar migrations
php artisan migrate --force

# Verificar estrutura
php artisan migrate:status
SHOW CREATE TABLE password_reset_tokens;
```

### 2. Configuration Verification
```bash
# Verificar configurações
php artisan config:cache
php artisan route:cache

# Testar Resend connection
php artisan tinker
>>> config('resend.api_key')
>>> app()->make(\App\Services\Resend\ResendService::class)
```

### 3. Functional Testing
```bash
# Executar todos os testes
php artisan test tests/Feature/Auth/PasswordResetTest.php

# Teste manual via API
curl -X POST https://staging.domain.com/api/auth/password/forgot \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>"}'
```

### 4. Performance Testing
```bash
# Testar rate limiting
for i in {1..5}; do
  curl -X POST https://staging.domain.com/api/auth/password/forgot \
    -H "Content-Type: application/json" \
    -d '{"email":"<EMAIL>"}'
done

# Verificar logs
tail -f storage/logs/laravel.log
```

## 🌐 Production Deployment

### Phase 1: Database Migration (Maintenance Window)
```bash
# 1. Ativar modo de manutenção
php artisan down --message="Atualizando sistema de recuperação de senha"

# 2. Backup completo
mysqldump -u user -p database > backup_$(date +%Y%m%d_%H%M%S).sql

# 3. Executar migrations
php artisan migrate --force

# 4. Verificar integridade
php artisan migrate:status
```

### Phase 2: Code Deployment
```bash
# 1. Deploy do código
git pull origin main

# 2. Instalar dependências
composer install --no-dev --optimize-autoloader

# 3. Cache optimization
php artisan config:cache
php artisan route:cache
php artisan view:cache

# 4. Verificar permissions
chmod -R 755 storage/
chmod -R 755 bootstrap/cache/
```

### Phase 3: Verification & Activation
```bash
# 1. Smoke tests
php artisan test tests/Feature/Auth/PasswordResetTest.php

# 2. Verificar agendamento
php artisan schedule:list

# 3. Testar command de limpeza
php artisan auth:clean-expired-tokens

# 4. Desativar modo de manutenção
php artisan up
```

## 📊 Post-Deployment Monitoring

### Immediate Checks (0-2 hours)
- [ ] **API endpoints** respondendo corretamente
- [ ] **Rate limiting** funcionando (testar 4+ requests)
- [ ] **Email sending** funcionando via Resend
- [ ] **Database** sem erros de conexão
- [ ] **Logs** sendo gerados corretamente

### Short-term Monitoring (2-24 hours)
- [ ] **Performance** - tempo de resposta < 500ms
- [ ] **Error rate** - < 1% de erros 5xx
- [ ] **Email delivery** - taxa de entrega > 95%
- [ ] **Rate limiting** - bloqueios funcionando
- [ ] **Memory usage** - sem vazamentos

### Medium-term Monitoring (1-7 days)
- [ ] **Token cleanup** - command executando diariamente
- [ ] **Database growth** - tabela não crescendo excessivamente
- [ ] **Security logs** - sem tentativas suspeitas
- [ ] **User feedback** - sem reclamações de funcionalidade
- [ ] **Integration** - Resend API funcionando estável

## 🚨 Rollback Plan

### Automatic Rollback Triggers
- **Error rate > 5%** por mais de 5 minutos
- **Response time > 2s** por mais de 10 minutos
- **Email delivery < 80%** por mais de 15 minutos
- **Database errors** relacionados à nova tabela

### Rollback Procedure
```bash
# 1. Ativar modo de manutenção
php artisan down --message="Revertendo atualizações"

# 2. Reverter código
git checkout previous_stable_commit
composer install --no-dev --optimize-autoloader

# 3. Reverter database (se necessário)
# CUIDADO: Pode perder dados de tokens criados
php artisan migrate:rollback --step=1

# 4. Limpar caches
php artisan config:clear
php artisan route:clear
php artisan view:clear

# 5. Verificar funcionalidade básica
php artisan test tests/Feature/Auth/LoginTest.php

# 6. Reativar sistema
php artisan up
```

## 📈 Success Metrics

### Technical Metrics
- **Uptime:** > 99.9%
- **Response Time:** < 500ms (95th percentile)
- **Error Rate:** < 0.5%
- **Email Delivery:** > 98%
- **Test Coverage:** 100% (mantido)

### Business Metrics
- **Password Reset Success Rate:** > 90%
- **User Completion Rate:** > 80%
- **Support Tickets:** Redução de 50%
- **User Satisfaction:** > 4.5/5

### Security Metrics
- **Rate Limiting Effectiveness:** 100% de bloqueios corretos
- **Token Security:** 0 tokens comprometidos
- **Audit Trail:** 100% de eventos logados
- **Attack Detection:** < 1 minuto para detectar padrões suspeitos

## 🔧 Emergency Contacts

### Technical Team
- **Lead Developer:** [Nome] - [email] - [telefone]
- **DevOps Engineer:** [Nome] - [email] - [telefone]
- **Security Engineer:** [Nome] - [email] - [telefone]

### Business Team
- **Product Owner:** [Nome] - [email]
- **Customer Support:** [Nome] - [email]

### External Services
- **Resend Support:** <EMAIL>
- **Hosting Provider:** [contato]
- **Database Provider:** [contato]

## 📝 Post-Deployment Tasks

### Documentation
- [ ] Atualizar documentação da API
- [ ] Criar guias para suporte ao cliente
- [ ] Documentar novos logs e métricas
- [ ] Atualizar runbooks de operação

### Training
- [ ] Treinar equipe de suporte sobre nova funcionalidade
- [ ] Documentar troubleshooting comum
- [ ] Criar scripts de diagnóstico
- [ ] Estabelecer procedimentos de escalação

### Optimization
- [ ] Analisar performance após 1 semana
- [ ] Otimizar queries se necessário
- [ ] Ajustar rate limiting baseado em uso real
- [ ] Configurar alertas personalizados

---

**Deployment Date:** [DATA]  
**Deployed By:** [NOME]  
**Approved By:** [NOME]  
**Rollback Deadline:** [DATA + 24h]
