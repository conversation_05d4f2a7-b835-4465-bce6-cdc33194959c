# 🔧 Password Reset System - Troubleshooting Guide

## 🚨 Problemas Comuns e Soluções

### **1. "Resend API key is not configured"**

**Causa**: API key do Resend não configurada ou vazia

**Solução**:
```bash
# Verificar se variável existe
echo $RESEND_API_KEY

# Adicionar no .env
RESEND_API_KEY="re_xxxxxxxxx_your_api_key_here"

# Limpar cache de configuração
php artisan config:clear

# Testar configuração
php artisan tinker
>>> config('resend.api_key')
```

### **2. "Table 'password_reset_tokens' doesn't exist"**

**Causa**: Migration não foi executada

**Solução**:
```bash
# Verificar status das migrations
php artisan migrate:status

# Executar migrations pendentes
php artisan migrate

# Verificar se tabela foi criada
php artisan tinker
>>> Schema::hasTable('password_reset_tokens')
>>> Schema::getColumnListing('password_reset_tokens')
```

### **3. "Too Many Requests" (429) muito frequente**

**Causa**: Rate limiting muito restritivo

**Solução**:
```php
// Em app/Providers/RouteServiceProvider.php
RateLimiter::for('password-reset', function (Request $request) {
    return Limit::perMinute(5)->by($request->ip()); // Aumentar de 3 para 5
});
```

```bash
# Limpar cache de rotas
php artisan route:clear
php artisan config:clear
```

### **4. "Invalid or expired token" para tokens válidos**

**Causa**: Problema com hash do token ou timezone

**Diagnóstico**:
```sql
-- Verificar tokens na base
SELECT id, email, token, hashed_token, expires_at, used_at, created_at 
FROM password_reset_tokens 
WHERE email = '<EMAIL>' 
ORDER BY created_at DESC;

-- Verificar timezone
SELECT NOW(), UTC_TIMESTAMP();
```

**Solução**:
```bash
# Verificar timezone da aplicação
php artisan tinker
>>> config('app.timezone')
>>> now()->toDateTimeString()

# Verificar hash do token
>>> \App\Domains\Auth\PasswordResetToken::hashToken('test-token')
```

### **5. Emails não sendo enviados**

**Causa**: Problema com integração Resend

**Diagnóstico**:
```bash
# Verificar logs
tail -f storage/logs/laravel.log | grep -i resend

# Testar Resend diretamente
php artisan tinker
>>> $service = app()->make(\App\Services\Resend\ResendService::class)
>>> $email = new \App\Services\Resend\Domains\PasswordResetEmail($user, 'test-token')
>>> $service->send($email)
```

**Solução**:
```bash
# Verificar configurações Resend
cat .env | grep RESEND

# Testar em sandbox mode
RESEND_SANDBOX_MODE=true
```

### **6. "User not found" para usuários existentes**

**Causa**: Problema no UserRepository

**Diagnóstico**:
```bash
php artisan tinker
>>> $repo = app()->make(\App\Repositories\UserRepository::class)
>>> $user = $repo->fetchByEmail('<EMAIL>')
>>> var_dump($user)
```

**Solução**:
```php
// Verificar se UserRepository::fetchByEmail retorna ?UserDomain
public function fetchByEmail(string $email, ?int $organization_id = null): ?UserDomain
```

## 🔍 Diagnóstico Avançado

### **Verificar Integridade do Sistema**

```bash
# 1. Verificar todas as tabelas necessárias
php artisan tinker
>>> Schema::hasTable('users')
>>> Schema::hasTable('password_reset_tokens')
>>> Schema::hasTable('logs')

# 2. Verificar se classes existem
>>> class_exists(\App\Domains\Auth\PasswordResetToken::class)
>>> class_exists(\App\UseCases\Auth\RequestPasswordReset::class)
>>> class_exists(\App\Services\Resend\ResendService::class)

# 3. Verificar rotas
php artisan route:list | grep password
```

### **Debug de Token Generation**

```bash
php artisan tinker
>>> $factory = app()->make(\App\Factories\Auth\PasswordResetTokenFactory::class)
>>> $token = $factory->createForPasswordReset('<EMAIL>', 1, '127.0.0.1', 'Test Agent')
>>> echo "Token: " . $token->token
>>> echo "Hash: " . $token->hashed_token
>>> echo "Expires: " . $token->expires_at
```

### **Debug de Email Sending**

```bash
php artisan tinker
>>> $user = \App\Models\User::where('email', '<EMAIL>')->first()
>>> $userDomain = app()->make(\App\Factories\UserFactory::class)->buildFromModel($user)
>>> $email = new \App\Services\Resend\Domains\PasswordResetEmail($userDomain, 'test-token')
>>> $payload = $email->toResendPayload()
>>> print_r($payload)
```

## 📊 Monitoramento e Logs

### **Logs Importantes**

```bash
# Logs de password reset
tail -f storage/logs/laravel.log | grep -i "password\|reset\|token"

# Logs de rate limiting
tail -f storage/logs/laravel.log | grep -i "rate\|throttle"

# Logs de email
tail -f storage/logs/laravel.log | grep -i "resend\|email"

# Logs de erro
tail -f storage/logs/laravel.log | grep -i "error\|exception"
```

### **Queries de Monitoramento**

```sql
-- Tokens criados nas últimas 24h
SELECT COUNT(*) as total_tokens, 
       COUNT(CASE WHEN used_at IS NOT NULL THEN 1 END) as used_tokens,
       COUNT(CASE WHEN expires_at < NOW() THEN 1 END) as expired_tokens
FROM password_reset_tokens 
WHERE created_at >= NOW() - INTERVAL 24 HOUR;

-- Top IPs solicitando reset
SELECT ip_address, COUNT(*) as requests, MAX(created_at) as last_request
FROM password_reset_tokens 
WHERE created_at >= NOW() - INTERVAL 24 HOUR
GROUP BY ip_address 
ORDER BY requests DESC 
LIMIT 10;

-- Emails com múltiplas tentativas
SELECT email, COUNT(*) as attempts, MAX(created_at) as last_attempt
FROM password_reset_tokens 
WHERE created_at >= NOW() - INTERVAL 24 HOUR
GROUP BY email 
HAVING attempts > 3
ORDER BY attempts DESC;

-- Tokens não utilizados (possível problema)
SELECT email, created_at, expires_at
FROM password_reset_tokens 
WHERE used_at IS NULL 
  AND expires_at > NOW()
  AND created_at < NOW() - INTERVAL 1 HOUR
ORDER BY created_at DESC;
```

## 🛠️ Ferramentas de Debug

### **Script de Diagnóstico Completo**

```bash
#!/bin/bash
# password-reset-diagnostic.sh

echo "=== Password Reset System Diagnostic ==="
echo

echo "1. Environment Check:"
echo "RESEND_API_KEY: $(echo $RESEND_API_KEY | cut -c1-10)..."
echo "APP_URL: $APP_URL"
echo "DB_CONNECTION: $DB_CONNECTION"
echo

echo "2. Database Check:"
php artisan tinker --execute="
echo 'Users table: ' . (Schema::hasTable('users') ? 'OK' : 'MISSING') . PHP_EOL;
echo 'Password reset tokens table: ' . (Schema::hasTable('password_reset_tokens') ? 'OK' : 'MISSING') . PHP_EOL;
echo 'Logs table: ' . (Schema::hasTable('logs') ? 'OK' : 'MISSING') . PHP_EOL;
"

echo "3. Routes Check:"
php artisan route:list | grep -E "(password|reset)" | head -5

echo "4. Recent Tokens:"
php artisan tinker --execute="
\$count = \App\Models\PasswordResetToken::where('created_at', '>=', now()->subDay())->count();
echo 'Tokens created in last 24h: ' . \$count . PHP_EOL;
"

echo "5. Command Check:"
php artisan auth:clean-expired-tokens --dry-run 2>/dev/null || echo "Command not working"

echo
echo "=== Diagnostic Complete ==="
```

### **Test Script Rápido**

```bash
#!/bin/bash
# quick-test.sh

echo "Testing password reset endpoints..."

# Test forgot password
echo "1. Testing forgot password:"
curl -s -X POST http://localhost:8000/api/auth/password/forgot \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>"}' | jq .

# Test rate limiting
echo "2. Testing rate limiting (should fail on 4th request):"
for i in {1..4}; do
  echo "Request $i:"
  curl -s -X POST http://localhost:8000/api/auth/password/forgot \
    -H "Content-Type: application/json" \
    -d '{"email":"<EMAIL>"}' | jq .status
done

echo "Test complete."
```

## 🚨 Problemas Críticos

### **Sistema Completamente Inoperante**

**Sintomas**: Todos os endpoints retornam 500

**Diagnóstico**:
```bash
# Verificar logs de erro
tail -n 50 storage/logs/laravel.log

# Verificar configuração básica
php artisan config:show | grep -A 10 -B 10 resend

# Testar conexão com banco
php artisan tinker --execute="DB::connection()->getPdo()"
```

**Solução de Emergência**:
```bash
# 1. Reverter para versão anterior
git checkout HEAD~1

# 2. Ou desabilitar temporariamente
# Comentar rotas em routes/api.php
# Route::post('auth/password/forgot', [AuthController::class, 'forgotPassword']);
```

### **Performance Degradada**

**Sintomas**: Endpoints muito lentos (> 2s)

**Diagnóstico**:
```bash
# Verificar queries lentas
php artisan tinker --execute="
DB::enableQueryLog();
// Executar operação lenta
\$logs = DB::getQueryLog();
print_r(\$logs);
"

# Verificar índices
mysql -e "SHOW INDEX FROM password_reset_tokens"
```

**Solução**:
```sql
-- Adicionar índices se necessário
CREATE INDEX idx_email_expires ON password_reset_tokens(email, expires_at);
CREATE INDEX idx_ip_created ON password_reset_tokens(ip_address, created_at);
```

### **Vazamento de Memória**

**Sintomas**: Uso de memória crescendo continuamente

**Diagnóstico**:
```bash
# Monitorar uso de memória
watch -n 1 'ps aux | grep php | grep -v grep'

# Verificar logs de memória
grep -i "memory\|fatal" storage/logs/laravel.log
```

**Solução**:
```php
// Verificar se há loops infinitos ou objetos não liberados
// Em UseCases, garantir que não há referências circulares
```

## 📞 Escalação

### **Nível 1 - Suporte Técnico**
- Problemas de configuração
- Questões de usuário
- Rate limiting

### **Nível 2 - Desenvolvimento**
- Bugs de código
- Problemas de integração
- Performance

### **Nível 3 - Arquitetura**
- Problemas de design
- Questões de segurança
- Escalabilidade

### **Contatos de Emergência**
- **Lead Developer**: [email]
- **DevOps**: [email]  
- **Security Team**: [email]

---

**Última atualização**: 2025-07-29  
**Versão**: 1.0.0  
**Suporte**: Sistema de Autenticação
