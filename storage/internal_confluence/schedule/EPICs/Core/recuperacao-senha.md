# Epic: Sistema de Recuperação de Senha

## 📋 Overview

Sistema seguro e eficiente para recuperação de senhas de usuários, integrado com o **Resend Email Service** já implementado. O sistema oferece recuperação via email com tokens seguros, validação robusta e proteção contra ataques, seguindo os padrões de segurança OWASP e a arquitetura existente do sistema.

**Pré-requisito:** Esta Epic assume que a [Epic: Integração com Serviço de Email - Resend](./integracao-servico-email.md) já está implementada.

## 🔍 Funcionalidades Atuais Identificadas

| Status | Funcionalidade |
|--------|----------------|
| ❌ | Sistema de recuperação de senha via email |
| ❌ | Tokens seguros com expiração automática |
| ❌ | Validação de identidade e rate limiting |
| ❌ | Histórico de tentativas de recuperação |
| ❌ | Interface de reset de senha |
| ✅ | Sistema de autenticação com Sanctum |
| ✅ | Modelo User com hash de senha |
| ✅ | Controllers de Auth existentes |
| ✅ | Middleware de autenticação |
| ✅ | Sistema de email via Resend (pré-requisito) |

## 🚀 Melhorias Propostas

### 1. Sistema de tokens de recuperação
Implementar geração de tokens seguros com expiração automática, armazenados em tabela dedicada com hash para segurança adicional.

### 2. Integração com Resend Email
Utilizar o `PasswordResetEmail` domain já implementado no Resend para envio de emails de recuperação com templates profissionais.

### 3. Rate limiting e proteção contra ataques
Implementar limitação de tentativas por IP e por usuário, com bloqueio temporário para prevenir ataques de força bruta.

### 4. Validação robusta de tokens
Sistema de validação que verifica expiração, integridade do token e associação correta com o usuário.

### 5. Auditoria e logging
Registrar todas as tentativas de recuperação via `DBLog` para auditoria e detecção de atividades suspeitas.

## 📅 Resumo do Plano de Implementação

- **Fase 1:** Criação de migrations e models para tokens de recuperação
- **Fase 2:** Implementação de domains e use cases de recuperação
- **Fase 3:** Controllers e rotas de API para recuperação
- **Fase 4:** Rate limiting e proteções de segurança
- **Fase 5:** Testes unitários e de segurança

## 🔧 Plano de Implementação Detalhado

### 1. Database e Models

**Migration:**
- `database/migrations/xxxx_create_password_reset_tokens_table.php`

**Campos da tabela `password_reset_tokens`:**
```sql
id (bigint, primary key)
email (string, index)
token (string, hashed)
organization_id (bigint, nullable, index)
expires_at (timestamp)
used_at (timestamp, nullable)
ip_address (string)
user_agent (text, nullable)
created_at (timestamp)
updated_at (timestamp)
```

**Model:**
- `app/Models/PasswordResetToken.php` - Model Eloquent para tokens

### 2. Domains

**Password Reset Token Domain:**
- `app/Domains/Auth/PasswordResetToken.php` - Encapsula lógica de token

**Propriedades do Domain:**
```php
public ?int $id;
public string $email;
public string $token;           // Token em texto plano (para envio)
public string $hashedToken;     // Token hasheado (para armazenamento)
public ?int $organization_id;
public Carbon $expires_at;
public ?Carbon $used_at;
public string $ip_address;
public ?string $user_agent;
```

**Métodos do Domain:**
- `generateToken(): string` - Gera token seguro
- `hashToken(string $token): string` - Hash do token
- `isExpired(): bool` - Verifica se expirou
- `isUsed(): bool` - Verifica se foi usado
- `markAsUsed(): void` - Marca como usado

### 3. UseCases

**RequestPasswordReset:**
- `app/UseCases/Auth/RequestPasswordReset.php`
- Valida email, cria token, envia email via Resend
- Rate limiting por IP e email

**ValidateResetToken:**
- `app/UseCases/Auth/ValidateResetToken.php`
- Valida token, verifica expiração e uso

**ResetPassword:**
- `app/UseCases/Auth/ResetPassword.php`
- Valida token, atualiza senha, marca token como usado

**CleanExpiredTokens:**
- `app/UseCases/Auth/CleanExpiredTokens.php`
- Remove tokens expirados (para command agendado)

### 4. Controllers e Rotas

**Controller:**
- Integrar métodos no `app/Http/Controllers/AuthController.php` existente

**Novos métodos no AuthController:**
```php
public function forgotPassword(ForgotPasswordRequest $request): JsonResponse
public function resetPassword(ResetPasswordRequest $request): JsonResponse
public function validateToken(ValidateTokenRequest $request): JsonResponse
```

**Rotas API:**
```php
// Adicionar em routes/api.php
Route::prefix('auth')->group(function () {
    Route::post('password/forgot', [AuthController::class, 'forgotPassword']);
    Route::post('password/reset', [AuthController::class, 'resetPassword']);
    Route::post('password/validate-token', [AuthController::class, 'validateToken']);
});
```

### 5. Requests de Validação

**ForgotPasswordRequest:**
- `app/Http/Requests/Auth/ForgotPasswordRequest.php`
- Validação: email obrigatório e formato válido

**ResetPasswordRequest:**
- `app/Http/Requests/Auth/ResetPasswordRequest.php`
- Validação: token, email, password (confirmed, min:6)

**ValidateTokenRequest:**
- `app/Http/Requests/Auth/ValidateTokenRequest.php`
- Validação: token e email obrigatórios

### 6. Rate Limiting e Segurança

**Middleware de Rate Limiting:**
- Usar middleware `throttle` do Laravel
- Configurar limites específicos para recuperação de senha

**Configuração de Rate Limits:**
```php
// Em RouteServiceProvider ou routes/api.php
Route::middleware(['throttle:password-reset'])->group(function () {
    Route::post('auth/password/forgot', [AuthController::class, 'forgotPassword']);
});

// Configurar em app/Providers/RouteServiceProvider.php
RateLimiter::for('password-reset', function (Request $request) {
    return Limit::perMinute(3)->by($request->ip());
});
```

### 7. Integração com Resend Email

**Uso do PasswordResetEmail:**
```php
// No UseCase RequestPasswordReset
use App\Services\Resend\Domains\PasswordResetEmail;
use App\Services\Resend\UseCases\Send as SendEmail;

$passwordResetEmail = new PasswordResetEmail($user, $token->token);
$sendEmailUseCase = app()->make(SendEmail::class);
$sendEmailUseCase->perform($passwordResetEmail);
```

### 8. Commands Agendados

**CleanExpiredTokensCommand:**
- `app/Console/Commands/Auth/CleanExpiredTokens.php`
- Execução diária para limpeza de tokens expirados

**Agendamento no Kernel:**
```php
// Em app/Console/Kernel.php
protected function schedule(Schedule $schedule)
{
    $schedule->command('auth:clean-expired-tokens')->daily();
}
```

## 📦 Previsão de Arquivos do PR

### Migrations
```
database/migrations/xxxx_create_password_reset_tokens_table.php (novo)
```

### Models
```
app/Models/PasswordResetToken.php (novo)
```

### Domains
```
app/Domains/Auth/PasswordResetToken.php (novo)
```

### Factories
```
app/Factories/Auth/PasswordResetTokenFactory.php (novo)
```

### Repositories
```
app/Repositories/PasswordResetTokenRepository.php (novo)
```

### UseCases
```
app/UseCases/Auth/RequestPasswordReset.php (novo)
app/UseCases/Auth/ValidateResetToken.php (novo)
app/UseCases/Auth/ResetPassword.php (novo)
app/UseCases/Auth/CleanExpiredTokens.php (novo)
```

### Controllers (modificados)
```
app/Http/Controllers/AuthController.php (modificado - adicionar métodos de recuperação)
```

### Requests
```
app/Http/Requests/Auth/ForgotPasswordRequest.php (novo)
app/Http/Requests/Auth/ResetPasswordRequest.php (novo)
app/Http/Requests/Auth/ValidateTokenRequest.php (novo)
```

### Commands
```
app/Console/Commands/Auth/CleanExpiredTokens.php (novo)
```

### Routes (modificados)
```
routes/api.php (modificado - adicionar rotas de recuperação)
```

### Providers (modificados)
```
app/Providers/RouteServiceProvider.php (modificado - rate limiting)
```

### Console (modificados)
```
app/Console/Kernel.php (modificado - agendamento de limpeza)
```

### Tests
```
tests/Feature/Auth/PasswordResetTest.php (novo)
tests/Feature/Auth/RateLimitingTest.php (novo)
tests/Unit/Domains/Auth/PasswordResetTokenTest.php (novo)
tests/Unit/UseCases/Auth/RequestPasswordResetTest.php (novo)
tests/Unit/UseCases/Auth/ResetPasswordTest.php (novo)
```

**Total estimado:** ~20 arquivos (16 novos + 4 modificados)

## 🔍 Melhorias Específicas Identificadas

### Problema 1: Ausência de sistema de recuperação de senha

| Aspecto | Descrição |
|---------|-----------|
| **Situação Atual** | Sistema sem capacidade de recuperação de senha |
| **Impacto** | Usuários não conseguem recuperar acesso quando esquecem a senha |
| **Solução** | Sistema completo de recuperação via email integrado com Resend |

### Problema 2: Falta de proteção contra ataques de força bruta

| Aspecto | Descrição |
|---------|-----------|
| **Situação Atual** | Sem limitação de tentativas de recuperação |
| **Impacto** | Vulnerabilidade a ataques automatizados |
| **Solução** | Rate limiting por IP e email com bloqueio temporário |

### Problema 3: Ausência de auditoria de tentativas de recuperação

| Aspecto | Descrição |
|---------|-----------|
| **Situação Atual** | Sem logs de tentativas de recuperação |
| **Impacto** | Impossível detectar atividades suspeitas |
| **Solução** | Logging via `DBLog` de todas as tentativas e ações |

## 🧪 Plano de Testes

### Testes Unitários
- ✅ Geração e validação de tokens seguros
- ✅ Verificação de expiração de tokens
- ✅ Hash e comparação de tokens
- ✅ Validação de domains e use cases
- ✅ Integração com PasswordResetEmail do Resend

### Testes de Integração
- ✅ Fluxo completo: solicitação → email → reset
- ✅ Integração com Resend para envio de emails
- ✅ Validação de rate limiting
- ✅ Limpeza automática de tokens expirados

### Testes de Segurança
- ✅ Proteção contra ataques de força bruta
- ✅ Validação de tokens malformados
- ✅ Tentativas de reutilização de tokens
- ✅ Verificação de expiração de tokens
- ✅ Rate limiting por IP e email

### Testes de Regressão
- ✅ Sistema de autenticação existente não afetado
- ✅ Controllers de Auth mantêm funcionalidade
- ✅ Middleware de autenticação compatível
- ✅ Sanctum tokens continuam funcionando

## 🎯 Conclusão

O sistema de recuperação de senha integra perfeitamente com a arquitetura existente e o Resend Email Service, oferecendo uma solução segura, robusta e user-friendly. A implementação segue todos os padrões de segurança OWASP e mantém compatibilidade total com o sistema de autenticação atual.

## 📈 Benefícios Esperados

### Técnicos
- ✅ Integração nativa com Resend Email Service
- ✅ Tokens seguros com hash e expiração
- ✅ Rate limiting robusto contra ataques
- ✅ Logging padronizado via `DBLog`
- ✅ Compatibilidade com arquitetura existente
- ✅ Limpeza automática de tokens expirados

### De Negócio
- 🔐 Redução de tickets de suporte para recuperação de senha
- 📧 Emails profissionais via templates Resend
- 🛡️ Maior segurança contra ataques automatizados
- ⚡ Processo de recuperação automatizado

### De Usuário
- 🔑 Recuperação fácil e rápida de senha
- 📱 Emails bem formatados e profissionais
- 🔒 Maior confiança na segurança do sistema
- ⏱️ Processo intuitivo e sem fricção

## 💼 Impacto no Negócio

- 🏢 Reduz abandono de usuários por problemas de acesso
- 📊 Melhora métricas de retenção de usuários
- 🤖 Reduz carga de suporte técnico
- 🔄 Base sólida para futuras funcionalidades de segurança

## 📚 Referências

- [OWASP Password Reset Guidelines](https://cheatsheetseries.owasp.org/cheatsheets/Forgot_Password_Cheat_Sheet.html)
- [Laravel Password Reset](https://laravel.com/docs/passwords)
- [Laravel Rate Limiting](https://laravel.com/docs/rate-limiting)
- [Sanctum Authentication](https://laravel.com/docs/sanctum)
- [Epic: Integração com Serviço de Email - Resend](./integracao-servico-email.md)

---

**Próximos Passos:** Após a implementação desta Epic, será possível expandir para funcionalidades como autenticação de dois fatores, notificações de login suspeito e outras melhorias de segurança.
