# Epic: Integração com Serviço de Email - Resend

## 📋 Overview

Esta Epic trata da integração exclusiva com o provedor [Resend](https://resend.com/) para envio de emails transacionais de forma rápida, segura e desacoplada. A implementação será feita com uma camada de serviço base reutilizável e simples de usar, utilizando **interfaces para diferentes tipos de email** (PasswordReset, CampaignReport, WelcomeEmail, etc.), permitindo invocação via use cases como `Send(EmailInterface $email)`.

Diferente de outras soluções complexas e multi-provedor, esta integração foca exclusivamente no Resend, utilizando sua API REST e adotando boas práticas de desacoplamento, rastreabilidade e controle, seguindo os padrões SOLID do sistema.

## 🔍 Funcionalidades Atuais Identificadas

| Status | Funcionalidade |
|--------|----------------|
| ❌ | Integração com API do Resend |
| ❌ | Camada de serviço para envio de emails |
| ❌ | Interface `EmailInterface` e implementações específicas |
| ❌ | UseCase `Send` para envio programático de email |
| ❌ | Domínios específicos (PasswordReset, CampaignReport, etc.) |
| ✅ | Sistema de logs e exceptions reutilizáveis (`DBLog`, etc.) |
| ✅ | Padrões de Services em `app/Services/` |
| ✅ | Padrões de UseCases e Domains estabelecidos |

## 🚀 Melhorias Propostas

### 1. Interface `EmailInterface` e implementações específicas
Criar uma interface que define o contrato para todos os tipos de email, implementada por domínios específicos como `PasswordResetEmail`, `CampaignReportEmail`, `WelcomeEmail`, etc. Cada implementação encapsula sua lógica específica de conteúdo e destinatários.

### 2. Serviço base `ResendService`
Classe responsável por encapsular chamadas HTTP à API do Resend, seguindo o padrão dos serviços existentes (`WhatsAppService`, `AsaasService`). Deve lidar com token de autenticação, headers, parsing de resposta e logging via `DBLog`.

### 3. UseCase `Send(EmailInterface $email)`
UseCase simples e reutilizável que recebe qualquer implementação de `EmailInterface` e invoca o serviço base para enviar o email, seguindo o padrão dos UseCases existentes no sistema.

### 4. Tratamento de falhas e rastreamento
Logs no padrão `DBLog` para erros na entrega ou falhas da API. Respostas relevantes da API podem ser salvas para rastreabilidade futura, seguindo o padrão estabelecido nos outros serviços.

## 📅 Resumo do Plano de Implementação

- **Fase 1:** Criação da interface `EmailInterface` e implementações específicas
- **Fase 2:** Implementação do serviço base `ResendService`
- **Fase 3:** UseCase `Send` para envio de emails
- **Fase 4:** Tratamento de erros e logging
- **Fase 5:** Testes unitários e integração

## 🔧 Plano de Implementação Detalhado

### 1. Interface e Implementações de Email

**Interface `EmailInterface`:**
```php
// app/Services/Resend/Contracts/EmailInterface.php
interface EmailInterface
{
    public function getTo(): array;                    // ['<EMAIL>', 'name' => 'John Doe']
    public function getFrom(): string;                 // '<EMAIL>'
    public function getSubject(): string;              // 'Reset Your Password'
    public function getTemplatePath(): string;         // 'emails.password-reset'
    public function getTemplateData(): array;          // ['user' => $user, 'token' => $token]
    public function getTags(): array;                  // ['type' => 'auth', 'priority' => 'high']
    public function getReplyTo(): ?string;             // '<EMAIL>' ou null
    public function toResendPayload(): array;          // Payload final para API Resend
}
```

**Implementações específicas:**

#### `PasswordResetEmail`
- **Arquivo:** `app/Services/Resend/Domains/PasswordResetEmail.php`
- **Template:** `resources/views/emails/password-reset.blade.php`
- **Dados:** `['user' => User, 'token' => string, 'expiry_minutes' => int]`
- **Tags:** `['type' => 'auth', 'priority' => 'high']`

#### `CampaignReportEmail`
- **Arquivo:** `app/Services/Resend/Domains/CampaignReportEmail.php`
- **Template:** `resources/views/emails/campaign-report.blade.php`
- **Dados:** `['campaign' => Campaign, 'metrics' => array, 'period' => string]`
- **Tags:** `['type' => 'report', 'priority' => 'medium']`

#### `WelcomeEmail`
- **Arquivo:** `app/Services/Resend/Domains/WelcomeEmail.php`
- **Template:** `resources/views/emails/welcome.blade.php`
- **Dados:** `['user' => User, 'organization' => Organization, 'next_steps' => array]`
- **Tags:** `['type' => 'onboarding', 'priority' => 'medium']`

#### `InvoiceEmail`
- **Arquivo:** `app/Services/Resend/Domains/InvoiceEmail.php`
- **Template:** `resources/views/emails/invoice.blade.php`
- **Dados:** `['invoice' => Invoice, 'client' => Client, 'due_date' => Carbon, 'payment_link' => string]`
- **Tags:** `['type' => 'billing', 'priority' => 'high']`

### 2. Templates Blade e Estrutura de Views

**Localização dos Templates:**
- `resources/views/emails/` - Diretório base para todos os templates de email

**Estrutura de Templates:**

#### Template Base (`resources/views/emails/layout.blade.php`)
```html
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>@yield('title', 'Obvio')</title>
    <style>
        /* CSS inline para compatibilidade com clientes de email */
        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; }
        .container { max-width: 600px; margin: 0 auto; }
        .header { background: #f8f9fa; padding: 20px; text-align: center; }
        .content { padding: 20px; }
        .footer { background: #f8f9fa; padding: 10px; text-align: center; font-size: 12px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <img src="{{ asset('images/logo.png') }}" alt="Obvio" style="height: 40px;">
        </div>
        <div class="content">
            @yield('content')
        </div>
        <div class="footer">
            @yield('footer', '© ' . date('Y') . ' Obvio. Todos os direitos reservados.')
        </div>
    </div>
</body>
</html>
```

#### Templates Específicos:

**Password Reset (`resources/views/emails/password-reset.blade.php`)**
```html
@extends('emails.layout')

@section('title', 'Reset Your Password')

@section('content')
    <h2>Olá, {{ $user->name }}!</h2>
    <p>Você solicitou a redefinição de sua senha. Clique no botão abaixo para criar uma nova senha:</p>

    <div style="text-align: center; margin: 30px 0;">
        <a href="{{ $reset_url }}" style="background: #007bff; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px;">
            Redefinir Senha
        </a>
    </div>

    <p><strong>Este link expira em {{ $expiry_minutes }} minutos.</strong></p>
    <p>Se você não solicitou esta redefinição, ignore este email.</p>
@endsection
```

**Campaign Report (`resources/views/emails/campaign-report.blade.php`)**
```html
@extends('emails.layout')

@section('title', 'Relatório de Campanha')

@section('content')
    <h2>Relatório da Campanha: {{ $campaign->name }}</h2>
    <p>Período: {{ $period }}</p>

    <table style="width: 100%; border-collapse: collapse; margin: 20px 0;">
        <tr style="background: #f8f9fa;">
            <th style="padding: 10px; border: 1px solid #ddd;">Métrica</th>
            <th style="padding: 10px; border: 1px solid #ddd;">Valor</th>
        </tr>
        @foreach($metrics as $metric => $value)
        <tr>
            <td style="padding: 10px; border: 1px solid #ddd;">{{ $metric }}</td>
            <td style="padding: 10px; border: 1px solid #ddd;">{{ $value }}</td>
        </tr>
        @endforeach
    </table>
@endsection
```

### 3. Serviço base `ResendService`

**Localização:**
- `app/Services/Resend/ResendService.php` - Seguindo padrão dos serviços existentes

**Responsabilidades:**
- Configuração de token e base URL via config
- Métodos `send(EmailInterface $email)` para envio via API
- Renderização de templates Blade via `view()->render()`
- Tratamento de respostas e exceções
- Logging via `DBLog` seguindo padrão existente

### 4. UseCase `Send`

**Localização:**
- `app/Services/Resend/UseCases/Send.php` - Seguindo padrão dos UseCases existentes

**Responsabilidades:**
- Receber `EmailInterface` (qualquer implementação)
- Validar dados obrigatórios via interface
- Renderizar template Blade com dados específicos
- Invocar `ResendService` com HTML renderizado
- Retornar resultado ou lançar exceção
- Log de auditoria via `DBLog`

**Fluxo de Processamento:**
1. Recebe implementação de `EmailInterface`
2. Obtém caminho do template via `getTemplatePath()`
3. Obtém dados via `getTemplateData()`
4. Renderiza template: `view($templatePath, $data)->render()`
5. Monta payload final via `toResendPayload()`
6. Envia via `ResendService`
7. Registra log de sucesso/erro

### 4. Tratamento de erros

**Exceptions:**
- `app/Services/Resend/Exceptions/ResendException.php` - Exceção específica do Resend

**Logging:**
- Usar `DBLog` para registrar falhas e sucessos seguindo padrão existente
- Logs estruturados para facilitar debugging

### 5. Testes

**Testes Unitários:**
- Mock para `ResendService`
- Validação de todas as implementações de `EmailInterface`
- Casos de erro de autenticação, bad request, etc.
- Testes de interface e polimorfismo
- Renderização de templates Blade com dados específicos

**Testes de Integração:**
- Envio real para sandbox/domínio de teste Resend
- Logs e rastreamento de resposta
- Teste de diferentes tipos de email
- Validação de templates renderizados

## 📦 Previsão de Arquivos do PR

### Contracts
```
app/Services/Resend/Contracts/EmailInterface.php (novo)
```

### Services
```
app/Services/Resend/ResendService.php (novo)
app/Services/Resend/Exceptions/ResendException.php (novo)
```

### Domains
```
app/Services/Resend/Domains/PasswordResetEmail.php (novo)
app/Services/Resend/Domains/CampaignReportEmail.php (novo)
app/Services/Resend/Domains/WelcomeEmail.php (novo)
app/Services/Resend/Domains/InvoiceEmail.php (novo)
```

### UseCases
```
app/Services/Resend/UseCases/Send.php (novo)
```

### Templates Blade
```
resources/views/emails/layout.blade.php (novo)
resources/views/emails/password-reset.blade.php (novo)
resources/views/emails/campaign-report.blade.php (novo)
resources/views/emails/welcome.blade.php (novo)
resources/views/emails/invoice.blade.php (novo)
```

### Configuration
```
config/resend.php (novo)
.env (modificado - adicionar RESEND_API_KEY)
```

### Tests
```
tests/Feature/Services/Resend/SendEmailTest.php (novo)
tests/Feature/Services/Resend/EmailTypesTest.php (novo)
tests/Feature/Services/Resend/TemplateRenderingTest.php (novo)
tests/Unit/Services/Resend/EmailInterfaceTest.php (novo)
tests/Unit/Services/Resend/ResendServiceTest.php (novo)
```

**Total estimado:** ~20 arquivos (19 novos + 1 modificado)

## 📋 Estrutura Detalhada de Dados por Tipo de Email

### PasswordResetEmail
```php
// Dados necessários
[
    'user' => User,                    // Objeto User completo
    'token' => string,                 // Token de reset único
    'reset_url' => string,             // URL completa para reset
    'expiry_minutes' => int,           // Tempo de expiração (ex: 60)
    'app_name' => string               // Nome da aplicação
]

// Template: resources/views/emails/password-reset.blade.php
// Tags: ['type' => 'auth', 'priority' => 'high', 'user_id' => $user->id]
```

### CampaignReportEmail
```php
// Dados necessários
[
    'campaign' => Campaign,            // Objeto Campaign
    'metrics' => [                     // Array de métricas
        'Mensagens Enviadas' => 1500,
        'Taxa de Entrega' => '98.5%',
        'Respostas Recebidas' => 45,
        'Taxa de Conversão' => '3.2%'
    ],
    'period' => string,                // Ex: "01/01/2024 - 31/01/2024"
    'organization' => Organization,     // Organização da campanha
    'generated_at' => Carbon           // Data/hora de geração
]

// Template: resources/views/emails/campaign-report.blade.php
// Tags: ['type' => 'report', 'priority' => 'medium', 'campaign_id' => $campaign->id]
```

### WelcomeEmail
```php
// Dados necessários
[
    'user' => User,                    // Novo usuário
    'organization' => Organization,     // Organização do usuário
    'next_steps' => [                  // Próximos passos sugeridos
        'Configurar perfil',
        'Criar primeiro projeto',
        'Convidar equipe'
    ],
    'support_email' => string,         // Email de suporte
    'getting_started_url' => string    // URL do guia inicial
]

// Template: resources/views/emails/welcome.blade.php
// Tags: ['type' => 'onboarding', 'priority' => 'medium', 'user_id' => $user->id]
```

### InvoiceEmail
```php
// Dados necessários
[
    'invoice' => Invoice,              // Objeto Invoice completo
    'client' => Client,                // Cliente da fatura
    'due_date' => Carbon,              // Data de vencimento
    'payment_link' => string,          // Link para pagamento
    'items' => array,                  // Itens da fatura
    'total_amount' => float,           // Valor total
    'organization' => Organization     // Organização emissora
]

// Template: resources/views/emails/invoice.blade.php
// Tags: ['type' => 'billing', 'priority' => 'high', 'invoice_id' => $invoice->id]
```

## 💻 Exemplo de Implementação de Domain

### PasswordResetEmail - Implementação Completa
```php
<?php

namespace App\Services\Resend\Domains;

use App\Services\Resend\Contracts\EmailInterface;
use App\Domains\User;
use Carbon\Carbon;

class PasswordResetEmail implements EmailInterface
{
    private User $user;
    private string $token;
    private int $expiryMinutes;

    public function __construct(User $user, string $token, int $expiryMinutes = 60)
    {
        $this->user = $user;
        $this->token = $token;
        $this->expiryMinutes = $expiryMinutes;
    }

    public function getTo(): array
    {
        return [
            'email' => $this->user->email,
            'name' => $this->user->name
        ];
    }

    public function getFrom(): string
    {
        return config('resend.from_email', '<EMAIL>');
    }

    public function getSubject(): string
    {
        return 'Redefinir sua senha - ' . config('app.name');
    }

    public function getTemplatePath(): string
    {
        return 'emails.password-reset';
    }

    public function getTemplateData(): array
    {
        return [
            'user' => $this->user,
            'token' => $this->token,
            'reset_url' => $this->generateResetUrl(),
            'expiry_minutes' => $this->expiryMinutes,
            'app_name' => config('app.name')
        ];
    }

    public function getTags(): array
    {
        return [
            'type' => 'auth',
            'priority' => 'high',
            'user_id' => $this->user->id
        ];
    }

    public function getReplyTo(): ?string
    {
        return config('resend.support_email', '<EMAIL>');
    }

    public function toResendPayload(): array
    {
        $templateData = $this->getTemplateData();
        $htmlContent = view($this->getTemplatePath(), $templateData)->render();

        return [
            'from' => $this->getFrom(),
            'to' => [$this->getTo()],
            'subject' => $this->getSubject(),
            'html' => $htmlContent,
            'tags' => $this->getTags(),
            'reply_to' => $this->getReplyTo()
        ];
    }

    private function generateResetUrl(): string
    {
        return url('/password/reset/' . $this->token . '?email=' . urlencode($this->user->email));
    }
}
```

## 🔍 Melhorias Específicas Identificadas

### Problema 1: Ausência de sistema de email transacional

| Aspecto | Descrição |
|---------|-----------|
| **Situação Atual** | Sistema sem capacidade de envio de emails programáticos |
| **Impacto** | Impossível enviar notificações, relatórios ou emails de sistema |
| **Solução** | Integração com Resend via interface padronizada e implementações específicas |

### Problema 2: Falta de padronização para diferentes tipos de email

| Aspecto | Descrição |
|---------|-----------|
| **Situação Atual** | Sem estrutura para diferentes tipos de email |
| **Impacto** | Dificuldade para manter consistência e reutilização |
| **Solução** | Interface `EmailInterface` com implementações específicas (PasswordReset, CampaignReport, etc.) |

### Problema 3: Ausência de rastreabilidade de emails

| Aspecto | Descrição |
|---------|-----------|
| **Situação Atual** | Sem logs ou auditoria de emails enviados |
| **Impacto** | Impossível debuggar problemas de entrega |
| **Solução** | Logging via `DBLog` seguindo padrão existente do sistema |

## 🧪 Plano de Testes

### Testes Unitários
- ✅ Validação de todas as implementações de `EmailInterface`
- ✅ Mock para `ResendService` com diferentes cenários
- ✅ Casos de erro de autenticação, bad request, etc.
- ✅ Testes de polimorfismo da interface
- ✅ Validação de payload gerado por cada tipo de email

### Testes de Integração
- ✅ Envio real para sandbox/domínio de teste Resend
- ✅ Logs e rastreamento de resposta
- ✅ Teste de diferentes tipos de email em ambiente controlado
- ✅ Integração com `DBLog` para auditoria

### Testes de Regressão
- ✅ Verificar que não afeta outros serviços
- ✅ Compatibilidade com estrutura existente
- ✅ Performance de envio em lote (se aplicável)

## 🎯 Conclusão

A integração com o Resend via interface padronizada tornará possível o envio programático e confiável de emails de diferentes tipos, seguindo os padrões SOLID e a arquitetura existente do sistema. A abordagem com interfaces permite fácil extensão para novos tipos de email e futura migração para outros provedores se necessário.

## 📈 Benefícios Esperados

### Técnicos
- ✅ Interface padronizada seguindo princípios SOLID
- ✅ Reutilização de código via implementações específicas
- ✅ Logging padronizado via `DBLog`
- ✅ Fácil extensão para novos tipos de email
- ✅ Compatibilidade com arquitetura existente

### De Negócio
- 📧 Capacidade de envio de emails transacionais
- 📊 Relatórios automáticos via email
- 🔐 Emails de segurança (reset de senha, etc.)
- 📈 Notificações de sistema automatizadas

### De Usuário
- 🔔 Notificações importantes via email
- 📋 Relatórios personalizados
- 🔒 Maior segurança com emails de confirmação
- 📱 Melhor experiência com comunicação automatizada

## 💼 Impacto no Negócio

- 🏢 Habilita comunicação automatizada com clientes
- ⚡ Reduz necessidade de intervenção manual
- 📊 Melhora rastreabilidade de comunicações
- 🔄 Base sólida para expansão de funcionalidades de email

## 📚 Referências

- [Resend API Documentation](https://resend.com/docs)
- [PSR-4 Autoloading Standard](https://www.php-fig.org/psr/psr-4/)
- [Laravel HTTP Client](https://laravel.com/docs/http-client)
- [RFC 5322 Email Standard](https://datatracker.ietf.org/doc/html/rfc5322)
- [SOLID Principles](https://en.wikipedia.org/wiki/SOLID)

---

**Próximos Passos:** Após a implementação desta Epic, será possível desenvolver funcionalidades que dependem de email como notificações automáticas, relatórios agendados e comunicações de sistema.
