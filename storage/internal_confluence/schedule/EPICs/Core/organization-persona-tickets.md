# Organization Persona System - Tickets Jira

## Ticket 1: Criar Entidade Persona e Infraestrutura Base

### Título
**OPS-001: Implementar Entidade Persona com Sistema de Controle de Acesso por Entidades**

### Descrição
Criar uma nova entidade Persona para implementar sistema de controle de acesso baseado em entidades (CRUDs) para organizações. Esta entidade permitirá definir pacotes de funcionalidades através de conjuntos de entidades que uma organização pode acessar.

### Componentes a Implementar
- **Migration**: `xxxx_create_personas_table.php`
- **Migration**: `xxxx_create_organizations_personas_table.php`
- **Model**: `app/Models/Persona.php`
- **Domain**: `app/Domains/Persona.php`
- **Factory**: `app/Factories/PersonaFactory.php`
- **Repository**: `app/Repositories/PersonaRepository.php`
- **Constants**: `app/Helpers/Constants.php`

### Acceptance Criteria
- [ ] Migration cria tabela `personas` com campos: id, name, description, entities (JSON), created_at, updated_at, deleted_at
- [ ] Migration cria tabela pivot `organizations_personas` com organization_id, persona_id, created_at, updated_at
- [ ] Model Persona com soft deletes, cast entities como array, relacionamento belongsToMany com Organization
- [ ] Model Organization atualizado com relacionamento belongsToMany com Persona
- [ ] Domain Persona com métodos toArray(), toStoreArray(), toUpdateArray(), getEntities()
- [ ] Factory com métodos buildFromModel(), buildFromStoreRequest(), buildFromUpdateRequest()
- [ ] Repository com métodos fetchAll(), fetchById(), store(), update(), delete()
- [ ] Constants.php com constante ALL_ENTITIES contendo lista de todas as entidades do sistema
- [ ] Validação que apenas super_admin pode criar/editar/deletar personas
- [ ] Soft delete implementado corretamente para personas

---

## Ticket 2: Implementar Lógica de Negócio no Domain Organization

### Título
**OPS-002: Adicionar Métodos de Controle de Acesso a Entidades no Domain Organization**

### Descrição
Estender o Domain Organization para incluir lógica de controle de acesso baseado em personas, implementando métodos para verificar acesso a entidades específicas e retornar lista de entidades disponíveis.

### Componentes a Modificar/Criar
- **Domain**: `app/Domains/Organization.php` (modificar)
- **Factory**: `app/Factories/OrganizationFactory.php` (modificar)
- **Repository**: `app/Repositories/OrganizationRepository.php` (modificar)
- **Use Cases**:
  - `app/UseCases/Organization/GetOrganizationEntities.php`
  - `app/UseCases/Organization/CheckEntityAccess.php`

### Acceptance Criteria
- [ ] Domain Organization com atributo personas (array de Persona domains)
- [ ] Método personaEntities() retorna array único de todas as entidades das personas
- [ ] Método hasEntityAccess($entity) verifica se organização tem acesso à entidade
- [ ] Método getAvailableEntities() retorna ALL_ENTITIES se não tem personas, ou personaEntities() se tem
- [ ] Factory atualizado para incluir personas nos métodos buildFromModel()
- [ ] Repository com métodos para buscar organizações com suas personas
- [ ] Use Case GetOrganizationEntities retorna entidades disponíveis para organização
- [ ] Use Case CheckEntityAccess verifica acesso a entidade específica
- [ ] Lógica que retorna todas as entidades quando organização não tem personas
- [ ] Validação que conjunto de entidades é único (sem duplicatas)

---

## Ticket 3: Criar Use Cases para Gestão de Personas

### Título
**OPS-003: Implementar Use Cases Completos para CRUD de Personas**

### Descrição
Criar todos os use cases necessários para gestão completa de personas, incluindo operações CRUD e validações de negócio específicas.

### Componentes a Implementar
- **Use Cases**:
  - `app/UseCases/Persona/CreatePersona.php`
  - `app/UseCases/Persona/UpdatePersona.php`
  - `app/UseCases/Persona/DeletePersona.php`
  - `app/UseCases/Persona/GetPersona.php`
  - `app/UseCases/Persona/GetAllPersonas.php`

### Acceptance Criteria
- [ ] Use Case CreatePersona cria nova persona com validações apropriadas
- [ ] Use Case UpdatePersona atualiza persona existente mantendo integridade
- [ ] Use Case DeletePersona realiza soft delete e remove associações com organizações
- [ ] Use Case GetPersona busca persona por ID com tratamento de não encontrado
- [ ] Use Case GetAllPersonas lista todas as personas (apenas para super_admin)
- [ ] Validação que entities contém apenas entidades válidas da constante ALL_ENTITIES
- [ ] Validação que name é obrigatório e único
- [ ] Validação que description é obrigatória
- [ ] Tratamento de erro quando persona não existe
- [ ] Logs de auditoria para operações de criação, atualização e deleção

---

## Ticket 4: Criar Use Cases para Gestão Organization-Persona

### Título
**OPS-004: Implementar Use Cases para Associação/Desassociação de Personas com Organizações**

### Descrição
Criar use cases específicos para gerenciar o relacionamento many-to-many entre organizações e personas, permitindo adicionar e remover personas de organizações.

### Componentes a Implementar
- **Use Cases**:
  - `app/UseCases/Organization/AddPersonaToOrganization.php`
  - `app/UseCases/Organization/RemovePersonaFromOrganization.php`
  - `app/UseCases/Organization/GetOrganizationPersonas.php`

### Acceptance Criteria
- [ ] Use Case AddPersonaToOrganization associa persona à organização
- [ ] Use Case RemovePersonaFromOrganization remove associação persona-organização
- [ ] Use Case GetOrganizationPersonas lista personas de uma organização específica
- [ ] Validação que organização existe antes de adicionar/remover persona
- [ ] Validação que persona existe e não está soft deleted
- [ ] Prevenção de duplicação ao adicionar persona já associada
- [ ] Tratamento de erro ao remover persona não associada
- [ ] Validação que apenas super_admin pode gerenciar associações
- [ ] Transações para garantir consistência nas operações
- [ ] Logs de auditoria para mudanças de associações
- [ ] Resposta indicando sucesso/falha da operação

---

## Ticket 5: Implementar Controllers e Endpoints para Personas

### Título
**OPS-005: Criar API Completa para Gerenciamento de Personas**

### Descrição
Implementar controllers e endpoints completos para gestão de personas, incluindo CRUD operations e validações de acesso apropriadas.

### Componentes a Implementar
- **Controller**: `app/Http/Controllers/PersonaController.php`
- **Requests**:
  - `app/Http/Requests/Persona/CreatePersonaRequest.php`
  - `app/Http/Requests/Persona/UpdatePersonaRequest.php`
- **Resource**: `app/Http/Resources/PersonaResource.php`
- **Routes**: Adicionar rotas em `routes/api.php`

### Endpoints a Implementar
- `GET /api/personas` - Listar todas as personas (super_admin)
- `POST /api/personas` - Criar nova persona (super_admin)
- `GET /api/personas/{id}` - Buscar persona específica (super_admin)
- `PUT /api/personas/{id}` - Atualizar persona (super_admin)
- `DELETE /api/personas/{id}` - Soft delete persona (super_admin)

### Acceptance Criteria
- [ ] Todos os endpoints restritos apenas a super_admin
- [ ] Endpoint GET /personas retorna lista paginada de personas
- [ ] Endpoint POST /personas cria persona com validações apropriadas
- [ ] Endpoint GET /personas/{id} retorna persona específica ou 404
- [ ] Endpoint PUT /personas/{id} atualiza persona com validações
- [ ] Endpoint DELETE /personas/{id} realiza soft delete
- [ ] Requests com validações: name obrigatório e único, description obrigatória, entities array válido
- [ ] Resource formatando resposta com todos os campos necessários
- [ ] Tratamento de erro 403 para usuários não super_admin
- [ ] Resposta consistente em formato JSON
- [ ] Logs de auditoria para todas as operações

---

## Ticket 6: Implementar Controllers para Organization-Persona

### Título
**OPS-006: Criar Endpoints para Gestão de Relacionamento Organization-Persona**

### Descrição
Implementar endpoints específicos para gerenciar o relacionamento entre organizações e personas, incluindo associação, desassociação e consulta de entidades disponíveis.

### Componentes a Implementar
- **Controller**: `app/Http/Controllers/OrganizationPersonaController.php`
- **Requests**:
  - `app/Http/Requests/Organization/AddPersonaRequest.php`
- **Resources**:
  - `app/Http/Resources/OrganizationPersonaResource.php`
  - `app/Http/Resources/EntityAccessResource.php`
- **Routes**: Adicionar rotas em `routes/api.php`

### Endpoints a Implementar
- `GET /api/organizations/{id}/personas` - Listar personas da organização
- `POST /api/organizations/{id}/personas` - Adicionar persona à organização (super_admin)
- `DELETE /api/organizations/{id}/personas/{persona_id}` - Remover persona da organização (super_admin)
- `GET /api/organizations/{id}/entities` - Listar entidades disponíveis para organização
- `GET /api/organizations/{id}/entities/{entity}/check` - Verificar acesso a entidade específica

### Acceptance Criteria
- [ ] Endpoint GET /organizations/{id}/personas lista personas da organização
- [ ] Endpoint POST /organizations/{id}/personas adiciona persona (apenas super_admin)
- [ ] Endpoint DELETE /organizations/{id}/personas/{persona_id} remove persona (apenas super_admin)
- [ ] Endpoint GET /organizations/{id}/entities retorna entidades disponíveis
- [ ] Endpoint GET /organizations/{id}/entities/{entity}/check retorna boolean de acesso
- [ ] Validação que organização existe em todos os endpoints
- [ ] Validação que usuário autenticado pertence à organização (exceto super_admin)
- [ ] Request AddPersonaRequest com validação de persona_id obrigatório
- [ ] Resources formatando respostas apropriadamente
- [ ] Tratamento de erro 404 para organização/persona não encontrada
- [ ] Resposta de entidades inclui todas se organização não tem personas

---

## Ticket 7: Criar Testes Unitários e de Integração

### Título
**OPS-007: Implementar Cobertura Completa de Testes para Sistema de Organization Persona**

### Descrição
Criar testes unitários e de integração completos para todo o sistema de organization persona, garantindo qualidade e confiabilidade do código.

### Componentes de Teste a Criar
- **Unit Tests**:
  - `tests/Unit/Domains/PersonaTest.php`
  - `tests/Unit/Domains/OrganizationTest.php`
  - `tests/Unit/Factories/PersonaFactoryTest.php`
  - `tests/Unit/Repositories/PersonaRepositoryTest.php`
  - `tests/Unit/Helpers/ConstantsTest.php`
- **Feature Tests**:
  - `tests/Feature/Controllers/PersonaControllerTest.php`
  - `tests/Feature/Controllers/OrganizationPersonaControllerTest.php`
  - `tests/Feature/UseCases/Persona/CreatePersonaTest.php`
  - `tests/Feature/UseCases/Organization/GetOrganizationEntitiesTest.php`
  - `tests/Feature/UseCases/Organization/CheckEntityAccessTest.php`

### Acceptance Criteria
- [ ] Testes unitários para todos os métodos dos Domains (personaEntities(), hasEntityAccess(), getAvailableEntities())
- [ ] Testes unitários para todos os métodos das Factories
- [ ] Testes unitários para todos os métodos dos Repositories
- [ ] Testes de integração para todos os endpoints da API
- [ ] Testes de integração para todos os Use Cases
- [ ] Testes verificando que apenas super_admin pode gerenciar personas
- [ ] Testes verificando comportamento quando organização não tem personas
- [ ] Testes verificando que entidades retornadas são únicas
- [ ] Testes de soft delete de personas e impacto nas organizações
- [ ] Testes de validação de entidades válidas na constante ALL_ENTITIES
- [ ] Testes usando factories sem dependências circulares (app()->make())
- [ ] Cobertura de testes > 90% para todos os componentes críticos
- [ ] Mocks apenas para dependências externas, objetos reais para lógica interna

---

## Ticket 8: Implementar Middleware e Validações de Acesso

### Título
**OPS-008: Criar Sistema de Validação Automática de Acesso a Entidades**

### Descrição
Implementar middleware e helpers para validação automática de acesso a entidades baseado nas personas da organização, facilitando a proteção de endpoints por entidade.

### Componentes a Implementar
- **Middleware**: `app/Http/Middleware/CheckEntityAccess.php`
- **Helper**: `app/Helpers/EntityAccessHelper.php`
- **Provider**: Registrar middleware em `app/Http/Kernel.php`

### Acceptance Criteria
- [ ] Middleware CheckEntityAccess verifica acesso à entidade via parâmetro
- [ ] Helper EntityAccessHelper com métodos estáticos para verificação de acesso
- [ ] Middleware registrado e disponível para uso em rotas
- [ ] Resposta 403 Forbidden para organizações sem acesso à entidade
- [ ] Resposta 401 Unauthorized para usuários não autenticados
- [ ] Super_admin sempre tem acesso a todas as entidades
- [ ] Organizações sem personas têm acesso a todas as entidades
- [ ] Logs de tentativas de acesso negado
- [ ] Testes para middleware e helper
- [ ] Documentação de uso do middleware
- [ ] Aplicação do middleware em rotas que precisam de controle de entidade
