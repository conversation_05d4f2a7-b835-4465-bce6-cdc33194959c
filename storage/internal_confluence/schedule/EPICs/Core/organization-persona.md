# Epic: Organization Persona - Sistema de Controle de Acesso por Entidades

## Overview

Esta Epic implementa um sistema de controle de acesso baseado em personas para organizações, permitindo definir quais entidades (CRUDs) cada organização pode acessar com base nas personas contratadas. O sistema funciona de forma similar ao sistema de permissões existente, mas opera no nível organizacional para controlar o acesso a funcionalidades específicas do sistema.

### Funcionalidades Atuais Identificadas:
- ✅ Sistema de permissões existente para usuários
- ✅ Modelo Organization funcional
- ✅ Sistema de autenticação e autorização
- ❌ Controle de acesso por entidades/CRUDs no nível organizacional
- ❌ Sistema de personas para organizações
- ❌ Relacionamento many-to-many entre Organization e Persona
- ❌ Constantes centralizadas de entidades do sistema

### Melhorias Propostas:

#### 1. **Sistema de Personas**
Criação de uma entidade Persona que define conjuntos de entidades (CRUDs) que uma organização pode acessar, permitindo pacotes de funcionalidades personalizados por tipo de negócio.

#### 2. **Relacionamento Organization-Persona**
Implementação de relacionamento many-to-many entre Organization e Persona, permitindo que uma organização tenha múltiplas personas e acesso ao conjunto unificado de suas entidades.

#### 3. **Controle de Acesso Dinâmico**
Sistema que retorna todas as entidades disponíveis quando a organização não possui personas, ou o conjunto específico de entidades baseado nas personas contratadas.

#### 4. **Gestão de Personas**
Endpoints completos para criação, edição, soft delete e associação/desassociação de personas às organizações, restrito a super_admins.

## Resumo do Plano de Implementação

A Epic será implementada em 4 fases sequenciais, começando pela infraestrutura base e evoluindo para funcionalidades avançadas de gestão.

**Fase 1**: Infraestrutura Base - Criação do modelo Persona, migrations e relacionamentos
**Fase 2**: Domínios e Lógica de Negócio - Implementação dos domínios e constantes de entidades
**Fase 3**: Use Cases e Repositories - Criação dos casos de uso para gestão de personas
**Fase 4**: Controllers e Endpoints - Implementação da API completa para gestão de personas

## Plano de Implementação Detalhado

### 1. Infraestrutura Base

#### Database:
- **Tabela `personas`**: Armazena as personas do sistema. Campos: id, name, description, entities (JSON), created_at, updated_at, deleted_at
- **Tabela `organizations_personas`**: Relacionamento many-to-many. Campos: id, organization_id, persona_id, created_at, updated_at

#### Models:
- **Persona**: Model com soft deletes, cast entities como array, relacionamento belongsToMany com Organization
- **Organization**: Adicionar relacionamento belongsToMany com Persona

### 2. Domínios e Lógica de Negócio

#### Domínios:
- **Persona**: Responsável pela lógica de personas, validações e transformações
- **Organization**: Adicionar atributo personas e método personaEntities()

#### Constants:
- **Helper/Constants**: Classe com constante ALL_ENTITIES contendo lista de todas as entidades do sistema

#### Usecases:
- **Persona/GetPersona**: Buscar persona por ID
- **Persona/GetAllPersonas**: Listar todas as personas
- **Organization/GetOrganizationEntities**: Retornar entidades disponíveis para organização

### 3. Use Cases e Repositories

#### Repositories:
- **PersonaRepository**: CRUD completo para personas
- **OrganizationRepository**: Adicionar métodos para gestão de personas

#### Usecases:
- **Persona/CreatePersona**: Criar nova persona
- **Persona/UpdatePersona**: Atualizar persona existente
- **Persona/DeletePersona**: Soft delete de persona
- **Organization/AddPersonaToOrganization**: Associar persona à organização
- **Organization/RemovePersonaFromOrganization**: Desassociar persona da organização
- **Organization/CheckEntityAccess**: Verificar se organização tem acesso a entidade específica

### 4. Controllers e Endpoints

#### Rotas/Endpoints Necessários:
- `GET /api/personas` - Listar todas as personas (super_admin)
- `POST /api/personas` - Criar nova persona (super_admin)
- `GET /api/personas/{id}` - Buscar persona específica (super_admin)
- `PUT /api/personas/{id}` - Atualizar persona (super_admin)
- `DELETE /api/personas/{id}` - Soft delete persona (super_admin)
- `GET /api/organizations/{id}/personas` - Listar personas da organização
- `POST /api/organizations/{id}/personas` - Adicionar persona à organização (super_admin)
- `DELETE /api/organizations/{id}/personas/{persona_id}` - Remover persona da organização (super_admin)
- `GET /api/organizations/{id}/entities` - Listar entidades disponíveis para organização
- `GET /api/organizations/{id}/entities/{entity}/check` - Verificar acesso a entidade específica

#### Controllers:
- **PersonaController**: CRUD completo para personas
- **OrganizationPersonaController**: Gestão de relacionamento organization-persona

## Previsão de PR

### Models
```
app/Models/Persona.php (novo)
app/Models/Organization.php (modificado)
```

### Domains
```
app/Domains/Persona.php (novo)
app/Domains/Organization.php (modificado)
```

### Factories
```
app/Factories/PersonaFactory.php (novo)
app/Factories/OrganizationFactory.php (modificado)
```

### Repositories
```
app/Repositories/PersonaRepository.php (novo)
app/Repositories/OrganizationRepository.php (modificado)
```

### Use Cases
```
app/UseCases/Persona/CreatePersona.php (novo)
app/UseCases/Persona/UpdatePersona.php (novo)
app/UseCases/Persona/DeletePersona.php (novo)
app/UseCases/Persona/GetPersona.php (novo)
app/UseCases/Persona/GetAllPersonas.php (novo)
app/UseCases/Organization/AddPersonaToOrganization.php (novo)
app/UseCases/Organization/RemovePersonaFromOrganization.php (novo)
app/UseCases/Organization/GetOrganizationEntities.php (novo)
app/UseCases/Organization/CheckEntityAccess.php (novo)
```

### Controllers
```
app/Http/Controllers/PersonaController.php (novo)
app/Http/Controllers/OrganizationPersonaController.php (novo)
```

### Requests
```
app/Http/Requests/Persona/CreatePersonaRequest.php (novo)
app/Http/Requests/Persona/UpdatePersonaRequest.php (novo)
app/Http/Requests/Organization/AddPersonaRequest.php (novo)
```

### Migrations
```
database/migrations/xxxx_create_personas_table.php (novo)
database/migrations/xxxx_create_organizations_personas_table.php (novo)
```

### Constants
```
app/Helpers/Constants.php (novo)
```

### Routes
```
routes/api.php (modificado - adicionar rotas de personas)
```

**Total Estimado: ~20 arquivos (18 novos + 2 modificados)**

## Melhorias Específicas Identificadas

### Problema 1: Falta de Controle de Acesso por Funcionalidades
**Situação Atual**: Todas as organizações têm acesso a todas as funcionalidades do sistema, independente do que contrataram
**Impacto**: Impossibilidade de criar pacotes diferenciados de funcionalidades, dificultando estratégias comerciais e personalização
**Solução**: Sistema de personas que define exatamente quais entidades cada organização pode acessar

### Problema 2: Ausência de Pacotes de Funcionalidades
**Situação Atual**: Não existe forma de agrupar funcionalidades relacionadas para diferentes tipos de negócio
**Impacto**: Clientes pagam por funcionalidades que não usam ou precisam de funcionalidades específicas não disponíveis
**Solução**: Personas pré-definidas como "Supermercado", "Depósito" com conjuntos específicos de entidades

### Problema 3: Gestão Manual de Acesso
**Situação Atual**: Não há forma sistemática de controlar quais CRUDs uma organização pode usar
**Impacto**: Dificuldade para implementar diferentes planos comerciais e controlar acesso granular
**Solução**: API completa para gestão de personas e associação dinâmica com organizações

## Plano de Testes

### Testes Unitários:
- Validação de criação e edição de personas
- Lógica de personaEntities() no domínio Organization
- Verificação de acesso a entidades específicas
- Soft delete de personas e impacto nas organizações

### Testes de Integração:
- Fluxo completo de associação persona-organização
- Verificação de acesso via endpoints
- Comportamento quando organização não tem personas
- Validação de permissões super_admin

### Testes de Regressão:
- Funcionalidades existentes de Organization não afetadas
- Sistema de permissões de usuários mantido
- APIs existentes continuam funcionando
- Dados existentes preservados

## Conclusão

Esta Epic estabelece um sistema robusto de controle de acesso baseado em personas, permitindo que o negócio ofereça pacotes diferenciados de funcionalidades para diferentes tipos de clientes. O sistema é flexível, permitindo que organizações tenham múltiplas personas e acesso ao conjunto unificado de suas entidades.

### Benefícios Esperados:
- **Flexibilidade Comercial**: Possibilidade de criar diferentes pacotes de funcionalidades
- **Controle Granular**: Acesso preciso a entidades específicas por organização
- **Escalabilidade**: Sistema preparado para crescimento e novos tipos de persona
- **Manutenibilidade**: Gestão centralizada de entidades e acesso via API

### Impacto no Negócio:
- Possibilidade de criar planos comerciais diferenciados
- Melhor adequação às necessidades específicas de cada cliente
- Controle mais preciso sobre funcionalidades disponíveis
- Base para futuras estratégias de monetização

## Referências

- Sistema de Permissões Existente - Padrão arquitetural do projeto
- Laravel Eloquent Relationships - Documentação oficial
- Soft Deletes Pattern - Best practices Laravel
- API Design Patterns - RESTful conventions
