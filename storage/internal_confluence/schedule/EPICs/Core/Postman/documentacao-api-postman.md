# 📋 EPIC OBVIO-8: Documentação Completa da API no Postman

## 🎯 Objetivo

Criar documentação completa e consistente de TODOS os endpoints do sistema no Postman, garantindo que cada endpoint esteja devidamente documentado, testado e organizado para facilitar o desenvolvimento, testes e integração.

## 🔍 Por que é importante?

- **Padronização**: Garantir que todos os endpoints sigam o mesmo padrão de documentação
- **Facilitar Desenvolvimento**: Desenvolvedores podem rapidamente entender e testar APIs
- **Integração**: Facilitar integrações com sistemas externos
- **Qualidade**: Garantir que todos os endpoints estão funcionando corretamente
- **Manutenção**: Facilitar a manutenção e evolução da API

## 📊 Mapeamento Completo dos Endpoints

### 🔐 **Authentication (9 endpoints)**
```
POST   /api/login
POST   /api/register
POST   /api/auth/password/forgot
POST   /api/auth/password/reset
POST   /api/auth/password/validate-token
GET    /api/user
POST   /api/logout
POST   /api/logout_all_sessions
DELETE /api/user/delete
```

### 👥 **User Management (19 endpoints)**

#### Users (5 + 4 notifications)
```
GET    /api/users
POST   /api/users
GET    /api/users/{id}
PUT    /api/users/{id}
DELETE /api/users/{id}
GET    /api/notifications/unread
GET    /api/notifications/all
POST   /api/notifications/read
POST   /api/notifications/read-one/{id}
```

#### Organizations (5 + 2 specific)
```
GET    /api/organizations
POST   /api/organizations
GET    /api/organizations/{id}
PUT    /api/organizations/{id}
DELETE /api/organizations/{id}
GET    /api/organization/{id}/check-access
GET    /api/organization/{id}/get-to-asaas-payload
```

#### Profiles (5)
```
GET    /api/profiles
POST   /api/profiles
GET    /api/profiles/{id}
PUT    /api/profiles/{id}
DELETE /api/profiles/{id}
```

#### Departments (5)
```
GET    /api/departments
POST   /api/departments
GET    /api/departments/{id}
PUT    /api/departments/{id}
DELETE /api/departments/{id}
```

#### Department Users (5)
```
GET    /api/department_users
POST   /api/department_users
GET    /api/department_users/{id}
PUT    /api/department_users/{id}
DELETE /api/department_users/{id}
```

### 📦 **Inventory (50+ endpoints)**

#### Brands (5)
```
GET    /api/brands
POST   /api/brands
GET    /api/brands/{id}
PUT    /api/brands/{id}
DELETE /api/brands/{id}
```

#### Products (5)
```
GET    /api/products
POST   /api/products
GET    /api/products/{id}
PUT    /api/products/{id}
DELETE /api/products/{id}
```

#### Product Histories (5)
```
GET    /api/products_histories
POST   /api/products_histories
GET    /api/products_histories/{id}
PUT    /api/products_histories/{id}
DELETE /api/products_histories/{id}
```

#### Custom Products (5)
```
GET    /api/custom_products
POST   /api/custom_products
GET    /api/custom_products/{id}
PUT    /api/custom_products/{id}
DELETE /api/custom_products/{id}
```

#### Clients (5)
```
GET    /api/clients
POST   /api/clients
GET    /api/clients/{id}
PUT    /api/clients/{id}
DELETE /api/clients/{id}
```

#### Projects (5 + 2 specific)
```
GET    /api/projects
POST   /api/projects
GET    /api/projects/{id}
PUT    /api/projects/{id}
DELETE /api/projects/{id}
POST   /api/project/budget/{budget_id}
POST   /api/project/{id}/products/
```

#### Project Products (5)
```
GET    /api/projects_products
POST   /api/projects_products
GET    /api/projects_products/{id}
PUT    /api/projects_products/{id}
DELETE /api/projects_products/{id}
```

#### Budgets (5 + 1 specific)
```
GET    /api/budgets
POST   /api/budgets
GET    /api/budgets/{id}
PUT    /api/budgets/{id}
DELETE /api/budgets/{id}
POST   /api/budget/{id}/products/
```

#### Budget Products (5)
```
GET    /api/budgets_products
POST   /api/budgets_products
GET    /api/budgets_products/{id}
PUT    /api/budgets_products/{id}
DELETE /api/budgets_products/{id}
```

#### Batches (5 + 1 specific)
```
GET    /api/batches
POST   /api/batches
GET    /api/batches/{id}
PUT    /api/batches/{id}
DELETE /api/batches/{id}
POST   /api/batch/{id}/process-at-stock/
```

#### Stocks (5)
```
GET    /api/stocks
POST   /api/stocks
GET    /api/stocks/{id}
PUT    /api/stocks/{id}
DELETE /api/stocks/{id}
```

#### Stock Entries (5)
```
GET    /api/stock_entries
POST   /api/stock_entries
GET    /api/stock_entries/{id}
PUT    /api/stock_entries/{id}
DELETE /api/stock_entries/{id}
```

#### Stock Exits (5)
```
GET    /api/stock_exits
POST   /api/stock_exits
GET    /api/stock_exits/{id}
PUT    /api/stock_exits/{id}
DELETE /api/stock_exits/{id}
```

#### Groups (5)
```
GET    /api/groups
POST   /api/groups
GET    /api/groups/{id}
PUT    /api/groups/{id}
DELETE /api/groups/{id}
```

#### Group Products (5)
```
GET    /api/groups_products
POST   /api/groups_products
GET    /api/groups_products/{id}
PUT    /api/groups_products/{id}
DELETE /api/groups_products/{id}
```

#### Shops (5)
```
GET    /api/shops
POST   /api/shops
GET    /api/shops/{id}
PUT    /api/shops/{id}
DELETE /api/shops/{id}
```

#### Sales (5)
```
GET    /api/sales
POST   /api/sales
GET    /api/sales/{id}
PUT    /api/sales/{id}
DELETE /api/sales/{id}
```

#### Items (5)
```
GET    /api/items
POST   /api/items
GET    /api/items/{id}
PUT    /api/items/{id}
DELETE /api/items/{id}
```

### 🤖 **ChatBot (92+ endpoints)**

#### Flows (5 + 1 specific)
```
GET    /api/flows
POST   /api/flows
GET    /api/flows/{id}
PUT    /api/flows/{id}
DELETE /api/flows/{id}
POST   /api/flow/save
```

#### Steps (5)
```
GET    /api/steps
POST   /api/steps
GET    /api/steps/{id}
PUT    /api/steps/{id}
DELETE /api/steps/{id}
```

#### Components (5 + 1 specific)
```
GET    /api/components
POST   /api/components
GET    /api/components/{id}
PUT    /api/components/{id}
DELETE /api/components/{id}
GET    /api/component/get-to-whatsapp-payload/{id}
```

#### Buttons (5)
```
GET    /api/buttons
POST   /api/buttons
GET    /api/buttons/{id}
PUT    /api/buttons/{id}
DELETE /api/buttons/{id}
```

#### Campaigns (5 + 8 specific)
```
GET    /api/campaigns
POST   /api/campaigns
GET    /api/campaigns/{id}
PUT    /api/campaigns/{id}
DELETE /api/campaigns/{id}
POST   /api/campaign/add-clients/{id}
POST   /api/campaign/remove-client/{id}
POST   /api/campaign/launch/{id}
GET    /api/campaign/{id}/clients
POST   /api/campaign/{id}/categories
POST   /api/campaign/{id}/tags
POST   /api/campaign/{id}/cancel
GET    /api/campaign/{id}/status-history
GET    /api/campaign/{id}/status-timeline
```

#### Messages (5 + 8 specific)
```
GET    /api/messages
POST   /api/messages
GET    /api/messages/{id}
PUT    /api/messages/{id}
DELETE /api/messages/{id}
POST   /api/message/generate-messages/{campaign_id}
GET    /api/message/get-to-whatsapp-payload/{id}
GET    /api/campaign/{id}/messages
GET    /api/campaign/{id}/messages/failed
GET    /api/campaign/{id}/messages/statistics
POST   /api/campaign/{id}/messages/resend-failed
POST   /api/message/{id}/resend
GET    /api/message/{id}/delivery-status
GET    /api/debug/message/getMessagesAvailableToSent
```

#### Templates (5 + 4 specific)
```
GET    /api/templates
POST   /api/templates
GET    /api/templates/{id}
PUT    /api/templates/{id}
DELETE /api/templates/{id}
POST   /api/template/save
POST   /api/template/publish/whatsapp/{id}
POST   /api/template/republish/whatsapp/{id}
GET    /api/template/get-to-whatsapp-payload/{id}
```

#### Parameters (5)
```
GET    /api/parameters
POST   /api/parameters
GET    /api/parameters/{id}
PUT    /api/parameters/{id}
DELETE /api/parameters/{id}
```

#### Phone Numbers (5 + 1 specific)
```
GET    /api/phone_numbers
POST   /api/phone_numbers
GET    /api/phone_numbers/{id}
PUT    /api/phone_numbers/{id}
DELETE /api/phone_numbers/{id}
GET    /api/whatsapp/testWhatsAppToken/{phone_number_id}
```

#### Conversations (5)
```
GET    /api/conversations
POST   /api/conversations
GET    /api/conversations/{id}
PUT    /api/conversations/{id}
DELETE /api/conversations/{id}
```

#### Interactions (5)
```
GET    /api/interactions
POST   /api/interactions
GET    /api/interactions/{id}
PUT    /api/interactions/{id}
DELETE /api/interactions/{id}
```

#### Categories (5)
```
GET    /api/categories
POST   /api/categories
GET    /api/categories/{id}
PUT    /api/categories/{id}
DELETE /api/categories/{id}
```

#### Tags (3 specific)
```
GET    /api/tags
GET    /api/tags/most-used
GET    /api/tags/suggestions
```

#### WhatsApp Messages (5)
```
GET    /api/whatsapp_messages
POST   /api/whatsapp_messages
GET    /api/whatsapp_messages/{id}
PUT    /api/whatsapp_messages/{id}
DELETE /api/whatsapp_messages/{id}
```

#### WhatsApp Webhook Entries (2)
```
GET    /api/whatsapp_webhook_entries
GET    /api/whatsapp_webhook_entries/{id}
```

#### WhatsApp Sync (7)
```
POST   /api/whatsapp/sync/message/{id}
POST   /api/whatsapp/sync/campaign/{id}
GET    /api/whatsapp/sync/logs
GET    /api/whatsapp/sync/entity-logs
GET    /api/whatsapp/sync/trends
GET    /api/whatsapp/sync/status-overview
POST   /api/whatsapp/sync/trigger-proactive
```

#### Analytics (8)
```
GET    /api/analytics/dashboard
GET    /api/analytics/campaign/{id}
POST   /api/analytics/campaigns/multiple
POST   /api/analytics/campaigns/compare
POST   /api/analytics/engagement/record
POST   /api/analytics/engagement/bulk
GET    /api/analytics/message/{id}/engagement
POST   /api/analytics/trigger-calculation
```

### 💳 **ASAAS Integration (50+ endpoints)**

#### ASAAS Account (12)
```
GET    /api/asaas/account/my-account
PUT    /api/asaas/account/my-account
GET    /api/asaas/account/balance
GET    /api/asaas/account/statistics
POST   /api/asaas/account/subaccount
GET    /api/asaas/account/subaccounts
GET    /api/asaas/account/subaccount
GET    /api/asaas/account/subaccount/search
GET    /api/asaas/account/organization/sync
PUT    /api/asaas/account/subaccount
DELETE /api/asaas/account/subaccount
```

#### ASAAS Customer (8)
```
POST   /api/asaas/customer
GET    /api/asaas/customers
GET    /api/asaas/customer/{client_id}
PUT    /api/asaas/customer/{client_id}
DELETE /api/asaas/customer/{client_id}
GET    /api/asaas/customer/search/email
GET    /api/asaas/customer/search/document
GET    /api/asaas/customer/{client_id}/notifications
```

#### ASAAS Payment (5)
```
POST   /api/asaas/payment
GET    /api/asaas/payments
GET    /api/asaas/payment/{payment_id}
PUT    /api/asaas/payment/{payment_id}
DELETE /api/asaas/payment/{payment_id}
```

#### ASAAS Subscription (6)
```
POST   /api/asaas/subscription
GET    /api/asaas/subscriptions
GET    /api/asaas/subscription/{subscription_id}
PUT    /api/asaas/subscription/{subscription_id}
DELETE /api/asaas/subscription/{subscription_id}
GET    /api/asaas/subscription/{subscription_id}/payments
```

#### ASAAS Organization (3)
```
POST   /api/asaas/organization/create-subaccount
GET    /api/asaas/organization/{id}/status
GET    /api/asaas/organization/{id}/access-check
```

#### ASAAS Client (2)
```
POST   /api/asaas/client/create-customer
GET    /api/asaas/client/{id}/status
```

#### ASAAS Sale (2)
```
POST   /api/asaas/sale/create-payment
GET    /api/asaas/sale/{id}/status
```

#### ASAAS Old Subscription (3)
```
POST   /api/asaas/subscription/create-asaas-subscription
POST   /api/asaas/subscription/sync-subscription/{id}
GET    /api/asaas/subscription/{id}/status
```

### 🗄️ **ASAAS Resources (25 endpoints)**

#### ASAAS Organization Resources (5)
```
GET    /api/asaas-resources/organizations
POST   /api/asaas-resources/organizations
GET    /api/asaas-resources/organizations/{id}
PUT    /api/asaas-resources/organizations/{id}
DELETE /api/asaas-resources/organizations/{id}
```

#### ASAAS Organization Customer Resources (5)
```
GET    /api/asaas-resources/organization-customers
POST   /api/asaas-resources/organization-customers
GET    /api/asaas-resources/organization-customers/{id}
PUT    /api/asaas-resources/organization-customers/{id}
DELETE /api/asaas-resources/organization-customers/{id}
```

#### ASAAS Client Resources (5)
```
GET    /api/asaas-resources/clients
POST   /api/asaas-resources/clients
GET    /api/asaas-resources/clients/{id}
PUT    /api/asaas-resources/clients/{id}
DELETE /api/asaas-resources/clients/{id}
```

#### ASAAS Sale Resources (5)
```
GET    /api/asaas-resources/sales
POST   /api/asaas-resources/sales
GET    /api/asaas-resources/sales/{id}
PUT    /api/asaas-resources/sales/{id}
DELETE /api/asaas-resources/sales/{id}
```

#### ASAAS Subscription Resources (5)
```
GET    /api/asaas-resources/subscriptions
POST   /api/asaas-resources/subscriptions
GET    /api/asaas-resources/subscriptions/{id}
PUT    /api/asaas-resources/subscriptions/{id}
DELETE /api/asaas-resources/subscriptions/{id}
```

### 📱 **Telegram (20+ endpoints)**

#### Telegram Users (5)
```
GET    /api/telegram_users
POST   /api/telegram_users
GET    /api/telegram_users/{id}
PUT    /api/telegram_users/{id}
DELETE /api/telegram_users/{id}
```

#### Telegram Bots (5)
```
GET    /api/telegram_bots
POST   /api/telegram_bots
GET    /api/telegram_bots/{id}
PUT    /api/telegram_bots/{id}
DELETE /api/telegram_bots/{id}
```

#### Telegram Chats (5)
```
GET    /api/telegram_chats
POST   /api/telegram_chats
GET    /api/telegram_chats/{id}
PUT    /api/telegram_chats/{id}
DELETE /api/telegram_chats/{id}
```

#### Telegram Messages (5)
```
GET    /api/telegram_messages
POST   /api/telegram_messages
GET    /api/telegram_messages/{id}
PUT    /api/telegram_messages/{id}
DELETE /api/telegram_messages/{id}
```

#### Telegram Webhooks (2)
```
POST   /api/telegram/receive-message
POST   /api/telegram/{bot_id}/receive
```

### 🔧 **Utilities & Reports (15+ endpoints)**

#### Imports (5 + 1 specific)
```
GET    /api/imports
POST   /api/imports
GET    /api/imports/{id}
PUT    /api/imports/{id}
DELETE /api/imports/{id}
POST   /api/import/{id}/process
```

#### Logs (3 specific)
```
GET    /api/logs/fetch/{id}
GET    /api/logs/fetch_from_organization/{organization_id}
GET    /api/logs/fetch_all
```

#### Reports (4)
```
GET    /api/reports/stock_entries
GET    /api/reports/stock_exits
GET    /api/reports/{model}/count
GET    /api/reports/{model}/sum/{column}
```

#### Subscriptions (6)
```
POST   /api/subscriptions
GET    /api/subscriptions/{id}
PUT    /api/subscriptions/{id}
GET    /api/subscriptions/organization/{organizationId}
POST   /api/subscriptions/grant-courtesy
DELETE /api/subscriptions/revoke-courtesy/{organizationId}
```

### 🖼️ **Tesseract/OCR (1 endpoint)**
```
POST   /api/ocr/image-reader/
```

### 🔗 **Webhooks (4 endpoints)**

#### WhatsApp Webhooks (2)
```
GET    /api/whatsapp/webhook
POST   /api/whatsapp/webhook
```

#### WhatsApp Webhook (Alternative) (2)
```
GET    /api/whatsapp/webhook/
POST   /api/whatsapp/webhook/
```

### 🛠️ **System Utilities (2 endpoints)**
```
GET    /api/give-my-php-info
GET    /api/give-my-php-v
```

## 📊 **Resumo Completo**

### **Total de Endpoints Mapeados: 300+**

- 🔐 **Authentication**: 9 endpoints
- 👥 **User Management**: 19 endpoints
- 📦 **Inventory**: 50+ endpoints
- 🤖 **ChatBot**: 92+ endpoints
- 💳 **ASAAS Integration**: 50+ endpoints
- 🗄️ **ASAAS Resources**: 25 endpoints
- 📱 **Telegram**: 22 endpoints
- 🔧 **Utilities & Reports**: 15+ endpoints
- 🖼️ **Tesseract/OCR**: 1 endpoint
- 🔗 **Webhooks**: 4 endpoints
- 🛠️ **System**: 2 endpoints

## 🎫 **Organização no Postman**

### **Estrutura de Pastas Sugerida:**

```
📁 Obvio API Collection
├── 🔐 Authentication
│   ├── Login & Register
│   ├── Password Reset
│   ├── User Session
│   └── Account Management
├── 👥 User Management
│   ├── Users
│   ├── Organizations
│   ├── Profiles
│   ├── Departments
│   └── Notifications
├── 📦 Inventory
│   ├── Products & Brands
│   ├── Clients & Projects
│   ├── Budgets & Sales
│   ├── Stock Management
│   └── Groups & Shops
├── 🤖 ChatBot
│   ├── Flows & Steps
│   ├── Templates & Components
│   ├── Campaigns
│   ├── Messages
│   ├── WhatsApp Integration
│   └── Analytics
├── 💳 Subscription
│   ├── ASAAS Integration
│   ├── ASAAS Resources
│   └── Core Subscriptions
├── 🔗 Webhooks
│   ├── WhatsApp
│   └── Telegram
├── 📥 Import & Reports
│   ├── Imports
│   ├── Reports
│   └── Logs
└── 🔧 Utilities
    ├── Tesseract/OCR
    └── System Info
```

## 🎯 **Estratégia de Implementação**

### **Fase 1 - Core Essencial (Semana 1-2)**
**Prioridade: CRÍTICA - 50 endpoints**
- 🔐 Authentication (9)
- 👥 User Management básico (15)
- 📦 Inventory básico (26)

### **Fase 2 - ChatBot Principal (Semana 3-4)**
**Prioridade: ALTA - 60 endpoints**
- 🤖 ChatBot CRUD (40)
- 🤖 ChatBot específicos (20)

### **Fase 3 - Integrações (Semana 5-6)**
**Prioridade: ALTA - 80 endpoints**
- 💳 ASAAS completo (50)
- 🗄️ ASAAS Resources (25)
- 📱 Telegram (22)

### **Fase 4 - Finalização (Semana 7-8)**
**Prioridade: MÉDIA - 30 endpoints**
- 🔗 Webhooks (4)
- 📥 Import & Reports (15)
- 🔧 Utilities (11)

## ✅ **Critérios de Aceite do EPIC**

- [ ] Todos os 300+ endpoints documentados no Postman
- [ ] Estrutura de pastas organizada e consistente
- [ ] Variáveis de ambiente configuradas ({{URL}}, {{TOKEN}})
- [ ] Exemplos de request/response para cada endpoint
- [ ] Testes automatizados funcionando
- [ ] Documentação técnica atualizada
- [ ] Collection exportável e versionada

## 🚀 **Próximos Passos**

1. **Criar Epic OBVIO-8** no Jira com 4 fases
2. **Criar subtasks** para cada grupo de endpoints
3. **Definir responsáveis** por fase
4. **Configurar ambiente** Postman
5. **Iniciar Fase 1** com endpoints críticos

## 📝 Template para Documentação de Entidade

```markdown
# 📌 Documentar entidade {{EntityName}}

## Descrição
Documentar e revisar a entidade {{EntityName}} no sistema, incluindo controller, model, domain, factory, use cases e rotas.
A documentação deve estar refletida no Postman e nos códigos do repositório.

## 🔹 Controller
- **Nome**: {{EntityName}}Controller
- **Postman path**: {{PostmanFolder}}
- **Variáveis utilizadas**: {{URL}}, {{TOKEN}}

## 🔹 Model
- **Fillable (campos atualizáveis)**: [Lista dos campos]
- **Eloquent Filters (campos filtráveis)**: [Lista dos filtros]

## 🔹 Endpoints / Use Cases
- **getAll** → GET /{{entity}}
- **get** → GET /{{entity}}/{id}
- **store** → POST /{{entity}}
- **update** → PUT /{{entity}}/{id}
- **delete** → DELETE /{{entity}}/{id}

## 🔹 Postman
- **Pastas e requests** revisados em {{PostmanFolder}}
- **Variáveis de ambiente** confirmadas ({{URL}}, {{TOKEN}})
- **Exemplo de body e response** atualizados

## 🔹 Domain / Factory
- **Classes**: {{EntityName}}Domain, {{EntityName}}Factory revisadas e atualizadas
- **Verificar** se estão consistentes com endpoints

## ✅ Critérios de Aceite
- [ ] Todos os endpoints da entidade estão documentados no Postman
- [ ] Model tem todos os campos atualizados em fillable e filtros revisados
- [ ] Controller e use cases possuem endpoints consistentes
- [ ] Factory e Domain refletem corretamente as regras de negócio
- [ ] Checklist acima está marcada como completa
```

## 🎫 Lista de Tickets/Tasks

### 📋 **Core/Management (5 entidades)**
1. 📌 Documentar entidade User
2. 📌 Documentar entidade Organization
3. 📌 Documentar entidade Profile
4. 📌 Documentar entidade Department
5. 📌 Documentar entidade DepartmentUser

### 📦 **Inventory (19 entidades)**
6. 📌 Documentar entidade Brand
7. 📌 Documentar entidade Product
8. 📌 Documentar entidade ProductHistory
9. 📌 Documentar entidade CustomProduct
10. 📌 Documentar entidade Client
11. 📌 Documentar entidade Project
12. 📌 Documentar entidade ProjectProduct
13. 📌 Documentar entidade Budget
14. 📌 Documentar entidade BudgetProduct
15. 📌 Documentar entidade Batch
16. 📌 Documentar entidade Stock
17. 📌 Documentar entidade StockEntry
18. 📌 Documentar entidade StockExit
19. 📌 Documentar entidade Group
20. 📌 Documentar entidade GroupProduct
21. 📌 Documentar entidade Shop
22. 📌 Documentar entidade Sale
23. 📌 Documentar entidade Item

### 🤖 **ChatBot (13 entidades)**
24. 📌 Documentar entidade Flow
25. 📌 Documentar entidade Step
26. 📌 Documentar entidade Component
27. 📌 Documentar entidade Button
28. 📌 Documentar entidade Campaign
29. 📌 Documentar entidade Message
30. 📌 Documentar entidade Template
31. 📌 Documentar entidade Parameter
32. 📌 Documentar entidade PhoneNumber
33. 📌 Documentar entidade Conversation
34. 📌 Documentar entidade Interaction
35. 📌 Documentar entidade Category
36. 📌 Documentar entidade Tag

### 💳 **ASAAS Integration (5 entidades)**
37. 📌 Documentar entidade AsaasAccount
38. 📌 Documentar entidade AsaasCustomer
39. 📌 Documentar entidade AsaasPayment
40. 📌 Documentar entidade AsaasSubscription
41. 📌 Documentar entidade AsaasOrganization

### 📱 **Telegram (4 entidades)**
42. 📌 Documentar entidade TelegramUser
43. 📌 Documentar entidade TelegramBot
44. 📌 Documentar entidade TelegramChat
45. 📌 Documentar entidade TelegramMessage

### 🔧 **Utilities (4 entidades)**
46. 📌 Documentar entidade Import
47. 📌 Documentar entidade Log
48. 📌 Documentar entidade Report
49. 📌 Documentar entidade Subscription

### 🖼️ **Tesseract/OCR (1 entidade)**
50. 📌 Documentar entidade RawImage

## 📈 Resumo do Projeto

- **Total de entidades**: 50
- **Estimativa por entidade**: 2-4 horas
- **Estimativa total**: 100-200 horas
- **Prioridade**: Alta (necessário para padronização)

## 🔄 Fluxo de Trabalho

1. **Análise**: Revisar controller, model, routes
2. **Documentação**: Criar/atualizar requests no Postman
3. **Testes**: Validar todos os endpoints
4. **Revisão**: Verificar consistência com domain/factory
5. **Validação**: Marcar critérios de aceite como completos

## 🎯 Critérios de Aceite do EPIC

- [ ] Todas as 50 entidades documentadas no Postman
- [ ] Estrutura de pastas organizada e consistente
- [ ] Variáveis de ambiente configuradas
- [ ] Exemplos de request/response atualizados
- [ ] Testes automatizados funcionando
- [ ] Documentação técnica atualizada

---

# 📋 TICKETS DETALHADOS POR ENTIDADE

## 🏢 Core/Management

### 📌 Ticket #1: Documentar entidade User

**Descrição**: Documentar e revisar a entidade User no sistema, incluindo controller, model, domain, factory, use cases e rotas.

#### 🔹 Controller
- **Nome**: UserController
- **Postman path**: User Management/Users
- **Variáveis utilizadas**: {{URL}}, {{TOKEN}}

#### 🔹 Model
**Fillable (campos atualizáveis)**:
- id: int
- name: string
- email: string
- email_verified_at: timestamp
- password: string
- organization_id: int
- profile_id: int
- created_at: timestamp
- updated_at: timestamp

**Eloquent Filters (campos filtráveis)**:
- id, name, email, organization_id, profile_id

#### 🔹 Endpoints / Use Cases
- **getAll** → GET /users
- **get** → GET /users/{id}
- **store** → POST /users
- **update** → PUT /users/{id}
- **delete** → DELETE /users/{id}

#### 🔹 Postman
- **Pastas e requests** revisados em User Management/Users
- **Variáveis de ambiente** confirmadas ({{URL}}, {{TOKEN}})
- **Exemplo de body e response** atualizados

#### 🔹 Domain / Factory
- **Classes**: UserDomain, UserFactory revisadas e atualizadas
- **Verificar** se estão consistentes com endpoints

#### ✅ Critérios de Aceite
- [ ] Todos os endpoints da entidade estão documentados no Postman
- [ ] Model tem todos os campos atualizados em fillable e filtros revisados
- [ ] Controller e use cases possuem endpoints consistentes
- [ ] Factory e Domain refletem corretamente as regras de negócio
- [ ] Checklist acima está marcada como completa

---

### 📌 Ticket #2: Documentar entidade Organization

**Descrição**: Documentar e revisar a entidade Organization no sistema, incluindo controller, model, domain, factory, use cases e rotas.

#### 🔹 Controller
- **Nome**: OrganizationController
- **Postman path**: User Management/Organizations
- **Variáveis utilizadas**: {{URL}}, {{TOKEN}}

#### 🔹 Model
**Fillable (campos atualizáveis)**:
- id: int
- name: string
- document: string
- email: string
- phone: string
- address: string
- city: string
- state: string
- zip_code: string
- created_at: timestamp
- updated_at: timestamp

**Eloquent Filters (campos filtráveis)**:
- id, name, document, email, city, state

#### 🔹 Endpoints / Use Cases
- **getAll** → GET /organizations
- **get** → GET /organizations/{id}
- **store** → POST /organizations
- **update** → PUT /organizations/{id}
- **delete** → DELETE /organizations/{id}

#### 🔹 Postman
- **Pastas e requests** revisados em User Management/Organizations
- **Variáveis de ambiente** confirmadas ({{URL}}, {{TOKEN}})
- **Exemplo de body e response** atualizados

#### 🔹 Domain / Factory
- **Classes**: OrganizationDomain, OrganizationFactory revisadas e atualizadas
- **Verificar** se estão consistentes com endpoints

#### ✅ Critérios de Aceite
- [ ] Todos os endpoints da entidade estão documentados no Postman
- [ ] Model tem todos os campos atualizados em fillable e filtros revisados
- [ ] Controller e use cases possuem endpoints consistentes
- [ ] Factory e Domain refletem corretamente as regras de negócio
- [ ] Checklist acima está marcada como completa

---

### 📌 Ticket #3: Documentar entidade Brand

**Descrição**: Documentar e revisar a entidade Brand no sistema, incluindo controller, model, domain, factory, use cases e rotas.

#### 🔹 Controller
- **Nome**: BrandController
- **Postman path**: Inventory/Brands
- **Variáveis utilizadas**: {{URL}}, {{TOKEN}}

#### 🔹 Model
**Fillable (campos atualizáveis)**:
- id: int
- name: string
- description: string
- organization_id: int
- created_at: timestamp
- updated_at: timestamp

**Eloquent Filters (campos filtráveis)**:
- id, name, organization_id

#### 🔹 Endpoints / Use Cases
- **getAll** → GET /brands
- **get** → GET /brands/{id}
- **store** → POST /brands
- **update** → PUT /brands/{id}
- **delete** → DELETE /brands/{id}

#### 🔹 Postman
- **Pastas e requests** revisados em Inventory/Brands
- **Variáveis de ambiente** confirmadas ({{URL}}, {{TOKEN}})
- **Exemplo de body e response** atualizados

#### 🔹 Domain / Factory
- **Classes**: BrandDomain, BrandFactory revisadas e atualizadas
- **Verificar** se estão consistentes com endpoints

#### ✅ Critérios de Aceite
- [ ] Todos os endpoints da entidade estão documentados no Postman
- [ ] Model tem todos os campos atualizados em fillable e filtros revisados
- [ ] Controller e use cases possuem endpoints consistentes
- [ ] Factory e Domain refletem corretamente as regras de negócio
- [ ] Checklist acima está marcada como completa

---

### 📌 Ticket #4: Documentar entidade Product

**Descrição**: Documentar e revisar a entidade Product no sistema, incluindo controller, model, domain, factory, use cases e rotas.

#### 🔹 Controller
- **Nome**: ProductController
- **Postman path**: Inventory/Products
- **Variáveis utilizadas**: {{URL}}, {{TOKEN}}

#### 🔹 Model
**Fillable (campos atualizáveis)**:
- id: int
- name: string
- description: string
- sku: string
- price: decimal
- cost: decimal
- brand_id: int
- organization_id: int
- created_at: timestamp
- updated_at: timestamp

**Eloquent Filters (campos filtráveis)**:
- id, name, sku, brand_id, organization_id

#### 🔹 Endpoints / Use Cases
- **getAll** → GET /products
- **get** → GET /products/{id}
- **store** → POST /products
- **update** → PUT /products/{id}
- **delete** → DELETE /products/{id}

#### 🔹 Postman
- **Pastas e requests** revisados em Inventory/Products
- **Variáveis de ambiente** confirmadas ({{URL}}, {{TOKEN}})
- **Exemplo de body e response** atualizados

#### 🔹 Domain / Factory
- **Classes**: ProductDomain, ProductFactory revisadas e atualizadas
- **Verificar** se estão consistentes com endpoints

#### ✅ Critérios de Aceite
- [ ] Todos os endpoints da entidade estão documentados no Postman
- [ ] Model tem todos os campos atualizados em fillable e filtros revisados
- [ ] Controller e use cases possuem endpoints consistentes
- [ ] Factory e Domain refletem corretamente as regras de negócio
- [ ] Checklist acima está marcada como completa

---

### 📌 Ticket #5: Documentar entidade Client

**Descrição**: Documentar e revisar a entidade Client no sistema, incluindo controller, model, domain, factory, use cases e rotas.

#### 🔹 Controller
- **Nome**: ClientController
- **Postman path**: Inventory/Clients
- **Variáveis utilizadas**: {{URL}}, {{TOKEN}}

#### 🔹 Model
**Fillable (campos atualizáveis)**:
- id: int
- name: string
- email: string
- phone: string
- document: string
- address: string
- city: string
- state: string
- zip_code: string
- organization_id: int
- created_at: timestamp
- updated_at: timestamp

**Eloquent Filters (campos filtráveis)**:
- id, name, email, document, organization_id

#### 🔹 Endpoints / Use Cases
- **getAll** → GET /clients
- **get** → GET /clients/{id}
- **store** → POST /clients
- **update** → PUT /clients/{id}
- **delete** → DELETE /clients/{id}

#### 🔹 Postman
- **Pastas e requests** revisados em Inventory/Clients
- **Variáveis de ambiente** confirmadas ({{URL}}, {{TOKEN}})
- **Exemplo de body e response** atualizados

#### 🔹 Domain / Factory
- **Classes**: ClientDomain, ClientFactory revisadas e atualizadas
- **Verificar** se estão consistentes com endpoints

#### ✅ Critérios de Aceite
- [ ] Todos os endpoints da entidade estão documentados no Postman
- [ ] Model tem todos os campos atualizados em fillable e filtros revisados
- [ ] Controller e use cases possuem endpoints consistentes
- [ ] Factory e Domain refletem corretamente as regras de negócio
- [ ] Checklist acima está marcada como completa

---

## 🤖 ChatBot

### 📌 Ticket #6: Documentar entidade Campaign

**Descrição**: Documentar e revisar a entidade Campaign no sistema, incluindo controller, model, domain, factory, use cases e rotas.

#### 🔹 Controller
- **Nome**: CampaignController
- **Postman path**: ChatBot/Campaigns
- **Variáveis utilizadas**: {{URL}}, {{TOKEN}}

#### 🔹 Model
**Fillable (campos atualizáveis)**:
- id: int
- name: string
- description: string
- status: string
- template_id: int
- phone_number_id: int
- organization_id: int
- is_direct_message: boolean
- created_at: timestamp
- updated_at: timestamp

**Eloquent Filters (campos filtráveis)**:
- id, name, status, template_id, phone_number_id, organization_id

#### 🔹 Endpoints / Use Cases
- **getAll** → GET /campaigns
- **get** → GET /campaigns/{id}
- **store** → POST /campaigns
- **update** → PUT /campaigns/{id}
- **delete** → DELETE /campaigns/{id}
- **launch** → POST /campaigns/{id}/launch
- **addClients** → POST /campaigns/{id}/clients
- **removeClient** → DELETE /campaigns/{id}/clients/{client_id}

#### 🔹 Postman
- **Pastas e requests** revisados em ChatBot/Campaigns
- **Variáveis de ambiente** confirmadas ({{URL}}, {{TOKEN}})
- **Exemplo de body e response** atualizados

#### 🔹 Domain / Factory
- **Classes**: CampaignDomain, CampaignFactory revisadas e atualizadas
- **Verificar** se estão consistentes com endpoints

#### ✅ Critérios de Aceite
- [ ] Todos os endpoints da entidade estão documentados no Postman
- [ ] Model tem todos os campos atualizados em fillable e filtros revisados
- [ ] Controller e use cases possuem endpoints consistentes
- [ ] Factory e Domain refletem corretamente as regras de negócio
- [ ] Checklist acima está marcada como completa

---

### 📌 Ticket #7: Documentar entidade Flow

**Descrição**: Documentar e revisar a entidade Flow no sistema, incluindo controller, model, domain, factory, use cases e rotas.

#### 🔹 Controller
- **Nome**: FlowController
- **Postman path**: ChatBot/Flows
- **Variáveis utilizadas**: {{URL}}, {{TOKEN}}

#### 🔹 Model
**Fillable (campos atualizáveis)**:
- id: int
- name: string
- description: string
- is_active: boolean
- organization_id: int
- created_at: timestamp
- updated_at: timestamp

**Eloquent Filters (campos filtráveis)**:
- id, name, is_active, organization_id

#### 🔹 Endpoints / Use Cases
- **getAll** → GET /flows
- **get** → GET /flows/{id}
- **store** → POST /flows
- **update** → PUT /flows/{id}
- **delete** → DELETE /flows/{id}

#### 🔹 Postman
- **Pastas e requests** revisados em ChatBot/Flows
- **Variáveis de ambiente** confirmadas ({{URL}}, {{TOKEN}})
- **Exemplo de body e response** atualizados

#### 🔹 Domain / Factory
- **Classes**: FlowDomain, FlowFactory revisadas e atualizadas
- **Verificar** se estão consistentes com endpoints

#### ✅ Critérios de Aceite
- [ ] Todos os endpoints da entidade estão documentados no Postman
- [ ] Model tem todos os campos atualizados em fillable e filtros revisados
- [ ] Controller e use cases possuem endpoints consistentes
- [ ] Factory e Domain refletem corretamente as regras de negócio
- [ ] Checklist acima está marcada como completa

---

## 💳 ASAAS Integration

### 📌 Ticket #8: Documentar entidade AsaasCustomer

**Descrição**: Documentar e revisar a entidade AsaasCustomer no sistema, incluindo controller, model, domain, factory, use cases e rotas.

#### 🔹 Controller
- **Nome**: CustomerController (ASAAS)
- **Postman path**: Subscription/ASAAS/Customers
- **Variáveis utilizadas**: {{URL}}, {{TOKEN}}

#### 🔹 Model
**Fillable (campos atualizáveis)**:
- id: int
- asaas_id: string
- name: string
- email: string
- phone: string
- document: string
- organization_id: int
- created_at: timestamp
- updated_at: timestamp

**Eloquent Filters (campos filtráveis)**:
- id, asaas_id, name, email, document, organization_id

#### 🔹 Endpoints / Use Cases
- **getAll** → GET /asaas/customers
- **get** → GET /asaas/customers/{id}
- **store** → POST /asaas/customers
- **update** → PUT /asaas/customers/{id}
- **delete** → DELETE /asaas/customers/{id}
- **searchByEmail** → GET /asaas/customers/search/email
- **searchByDocument** → GET /asaas/customers/search/document

#### 🔹 Postman
- **Pastas e requests** revisados em Subscription/ASAAS/Customers
- **Variáveis de ambiente** confirmadas ({{URL}}, {{TOKEN}})
- **Exemplo de body e response** atualizados

#### 🔹 Domain / Factory
- **Classes**: AsaasCustomerDomain, AsaasCustomerFactory revisadas e atualizadas
- **Verificar** se estão consistentes com endpoints

#### ✅ Critérios de Aceite
- [ ] Todos os endpoints da entidade estão documentados no Postman
- [ ] Model tem todos os campos atualizados em fillable e filtros revisados
- [ ] Controller e use cases possuem endpoints consistentes
- [ ] Factory e Domain refletem corretamente as regras de negócio
- [ ] Checklist acima está marcada como completa

---

## 📋 PRÓXIMAS AÇÕES

### 🎯 Priorização Sugerida

**Fase 1 - Core Essencial (Semana 1-2)**
1. User, Organization, Profile (Base do sistema)
2. Brand, Product, Client (Inventory principal)
3. Campaign, Flow, Message (ChatBot principal)

**Fase 2 - Inventory Completo (Semana 3-4)**
4. Budget, Project, Stock, Sale
5. Batch, Item, Shop, Group
6. Relacionamentos (BudgetProduct, ProjectProduct, etc.)

**Fase 3 - ChatBot Completo (Semana 5-6)**
7. Template, Step, Component, Button
8. Parameter, PhoneNumber, Conversation
9. Interaction, Category, Tag

**Fase 4 - Integrações (Semana 7-8)**
10. ASAAS (Customer, Payment, Subscription, Account)
11. Telegram (User, Bot, Chat, Message)
12. Utilities (Import, Log, Report, Tesseract)

### 🔧 Ferramentas e Comandos

```bash
# Gerar collection do Postman
php artisan postman:build

# Executar testes da API
php artisan test --filter=Api

# Verificar rotas
php artisan route:list --name=api
```

### 📊 Métricas de Sucesso

- **Cobertura**: 100% das entidades documentadas
- **Consistência**: Todos os endpoints seguem o mesmo padrão
- **Funcionalidade**: Todos os endpoints testados e funcionando
- **Organização**: Estrutura de pastas clara no Postman
- **Manutenibilidade**: Documentação fácil de atualizar

### 🎯 Resultado Esperado

Ao final deste EPIC, teremos:
- ✅ 50 entidades completamente documentadas
- ✅ Collection Postman organizada e funcional
- ✅ Padrão consistente para futuras entidades
- ✅ Base sólida para integrações externas
- ✅ Facilidade para onboarding de novos desenvolvedores
