# 📋 TICKETS ORGANIZADOS POR ENDPOINTS - OBVIO-8

## 🎯 **Estratégia de Tickets**

Baseado no mapeamento completo de **325+ endpoints**, organizamos os tickets por funcionalidade e complexidade, priorizando endpoints críticos e agrupando por contexto de uso.

## 📋 **Lista de Tickets (50 tickets)**

### **FASE 1 - CORE ESSENCIAL (12 tickets)**

#### **Ticket #1: Authentication (9 endpoints)**
**Pasta Postman**: `🔐 Authentication`
- POST /api/login
- POST /api/register  
- POST /api/auth/password/forgot
- POST /api/auth/password/reset
- POST /api/auth/password/validate-token
- GET /api/user
- POST /api/logout
- POST /api/logout_all_sessions
- DELETE /api/user/delete

#### **Ticket #2: Users & Notifications (9 endpoints)**
**Pasta Postman**: `👥 User Management/Users`
- GET /api/users (5 CRUD)
- GET /api/notifications/unread
- GET /api/notifications/all
- POST /api/notifications/read
- POST /api/notifications/read-one/{id}

#### **Ticket #3: Organizations (7 endpoints)**
**Pasta Postman**: `👥 User Management/Organizations`
- GET /api/organizations (5 CRUD)
- GET /api/organization/{id}/check-access
- GET /api/organization/{id}/get-to-asaas-payload

#### **Ticket #4: Profiles (5 endpoints)**
**Pasta Postman**: `👥 User Management/Profiles`
- GET /api/profiles (5 CRUD)

#### **Ticket #5: Departments & Department Users (10 endpoints)**
**Pasta Postman**: `👥 User Management/Departments`
- GET /api/departments (5 CRUD)
- GET /api/department_users (5 CRUD)

#### **Ticket #6: Brands (5 endpoints)**
**Pasta Postman**: `📦 Inventory/Brands`
- GET /api/brands (5 CRUD)

#### **Ticket #7: Products & Product Histories (10 endpoints)**
**Pasta Postman**: `📦 Inventory/Products`
- GET /api/products (5 CRUD)
- GET /api/products_histories (5 CRUD)

#### **Ticket #8: Clients (5 endpoints)**
**Pasta Postman**: `📦 Inventory/Clients`
- GET /api/clients (5 CRUD)

#### **Ticket #9: Projects & Project Products (12 endpoints)**
**Pasta Postman**: `📦 Inventory/Projects`
- GET /api/projects (5 CRUD + 2 specific)
- GET /api/projects_products (5 CRUD)

#### **Ticket #10: Budgets & Budget Products (11 endpoints)**
**Pasta Postman**: `📦 Inventory/Budgets`
- GET /api/budgets (5 CRUD + 1 specific)
- GET /api/budgets_products (5 CRUD)

#### **Ticket #11: Sales & Items (10 endpoints)**
**Pasta Postman**: `📦 Inventory/Sales`
- GET /api/sales (5 CRUD)
- GET /api/items (5 CRUD)

#### **Ticket #12: Stock Management (21 endpoints)**
**Pasta Postman**: `📦 Inventory/Stock`
- GET /api/stocks (5 CRUD)
- GET /api/stock_entries (5 CRUD)
- GET /api/stock_exits (5 CRUD)
- GET /api/batches (5 CRUD + 1 specific)

### **FASE 2 - CHATBOT PRINCIPAL (15 tickets)**

#### **Ticket #13: Flows (6 endpoints)**
**Pasta Postman**: `🤖 ChatBot/Flows`
- GET /api/flows (5 CRUD + 1 specific)

#### **Ticket #14: Steps (5 endpoints)**
**Pasta Postman**: `🤖 ChatBot/Steps`
- GET /api/steps (5 CRUD)

#### **Ticket #15: Components (6 endpoints)**
**Pasta Postman**: `🤖 ChatBot/Components`
- GET /api/components (5 CRUD + 1 specific)

#### **Ticket #16: Buttons (5 endpoints)**
**Pasta Postman**: `🤖 ChatBot/Buttons`
- GET /api/buttons (5 CRUD)

#### **Ticket #17: Templates (9 endpoints)**
**Pasta Postman**: `🤖 ChatBot/Templates`
- GET /api/templates (5 CRUD + 4 specific)

#### **Ticket #18: Parameters (5 endpoints)**
**Pasta Postman**: `🤖 ChatBot/Parameters`
- GET /api/parameters (5 CRUD)

#### **Ticket #19: Phone Numbers (6 endpoints)**
**Pasta Postman**: `🤖 ChatBot/PhoneNumbers`
- GET /api/phone_numbers (5 CRUD + 1 specific)

#### **Ticket #20: Campaigns (13 endpoints)**
**Pasta Postman**: `🤖 ChatBot/Campaigns`
- GET /api/campaigns (5 CRUD + 8 specific)

#### **Ticket #21: Messages (14 endpoints)**
**Pasta Postman**: `🤖 ChatBot/Messages`
- GET /api/messages (5 CRUD + 9 specific)

#### **Ticket #22: Conversations (5 endpoints)**
**Pasta Postman**: `🤖 ChatBot/Conversations`
- GET /api/conversations (5 CRUD)

#### **Ticket #23: Interactions (5 endpoints)**
**Pasta Postman**: `🤖 ChatBot/Interactions`
- GET /api/interactions (5 CRUD)

#### **Ticket #24: Categories (5 endpoints)**
**Pasta Postman**: `🤖 ChatBot/Categories`
- GET /api/categories (5 CRUD)

#### **Ticket #25: Tags (3 endpoints)**
**Pasta Postman**: `🤖 ChatBot/Tags`
- GET /api/tags (3 specific)

#### **Ticket #26: WhatsApp Messages (5 endpoints)**
**Pasta Postman**: `🤖 ChatBot/WhatsApp/Messages`
- GET /api/whatsapp_messages (5 CRUD)

#### **Ticket #27: WhatsApp Integration (17 endpoints)**
**Pasta Postman**: `🤖 ChatBot/WhatsApp/Integration`
- GET /api/whatsapp_webhook_entries (2)
- WhatsApp Sync (7)
- Analytics (8)

### **FASE 3 - INTEGRAÇÕES (15 tickets)**

#### **Ticket #28: ASAAS Account (11 endpoints)**
**Pasta Postman**: `💳 Subscription/ASAAS/Account`
- ASAAS Account endpoints

#### **Ticket #29: ASAAS Customer (8 endpoints)**
**Pasta Postman**: `💳 Subscription/ASAAS/Customer`
- ASAAS Customer endpoints

#### **Ticket #30: ASAAS Payment (5 endpoints)**
**Pasta Postman**: `💳 Subscription/ASAAS/Payment`
- ASAAS Payment endpoints

#### **Ticket #31: ASAAS Subscription (6 endpoints)**
**Pasta Postman**: `💳 Subscription/ASAAS/Subscription`
- ASAAS Subscription endpoints

#### **Ticket #32: ASAAS Organization (3 endpoints)**
**Pasta Postman**: `💳 Subscription/ASAAS/Organization`
- ASAAS Organization endpoints

#### **Ticket #33: ASAAS Client (2 endpoints)**
**Pasta Postman**: `💳 Subscription/ASAAS/Client`
- ASAAS Client endpoints

#### **Ticket #34: ASAAS Sale (2 endpoints)**
**Pasta Postman**: `💳 Subscription/ASAAS/Sale`
- ASAAS Sale endpoints

#### **Ticket #35: ASAAS Old Subscription (3 endpoints)**
**Pasta Postman**: `💳 Subscription/ASAAS/Legacy`
- ASAAS Old Subscription endpoints

#### **Ticket #36: ASAAS Organization Resources (5 endpoints)**
**Pasta Postman**: `💳 Subscription/ASAAS/Resources/Organizations`
- ASAAS Organization Resources

#### **Ticket #37: ASAAS Organization Customer Resources (5 endpoints)**
**Pasta Postman**: `💳 Subscription/ASAAS/Resources/OrganizationCustomers`
- ASAAS Organization Customer Resources

#### **Ticket #38: ASAAS Client Resources (5 endpoints)**
**Pasta Postman**: `💳 Subscription/ASAAS/Resources/Clients`
- ASAAS Client Resources

#### **Ticket #39: ASAAS Sale Resources (5 endpoints)**
**Pasta Postman**: `💳 Subscription/ASAAS/Resources/Sales`
- ASAAS Sale Resources

#### **Ticket #40: ASAAS Subscription Resources (5 endpoints)**
**Pasta Postman**: `💳 Subscription/ASAAS/Resources/Subscriptions`
- ASAAS Subscription Resources

#### **Ticket #41: Telegram Users & Bots (10 endpoints)**
**Pasta Postman**: `🔗 Webhooks/Telegram/Users`
- Telegram Users (5) + Telegram Bots (5)

#### **Ticket #42: Telegram Chats & Messages (12 endpoints)**
**Pasta Postman**: `🔗 Webhooks/Telegram/Messages`
- Telegram Chats (5) + Telegram Messages (5) + Webhooks (2)

### **FASE 4 - FINALIZAÇÃO (8 tickets)**

#### **Ticket #43: Imports (6 endpoints)**
**Pasta Postman**: `📥 Import & Reports/Imports`
- Import endpoints

#### **Ticket #44: Logs (3 endpoints)**
**Pasta Postman**: `📥 Import & Reports/Logs`
- Log endpoints

#### **Ticket #45: Reports (4 endpoints)**
**Pasta Postman**: `📥 Import & Reports/Reports`
- Report endpoints

#### **Ticket #46: Core Subscriptions (6 endpoints)**
**Pasta Postman**: `💳 Subscription/Core`
- Core Subscription endpoints

#### **Ticket #47: Groups & Shops (15 endpoints)**
**Pasta Postman**: `📦 Inventory/Groups`
- Groups (5) + Group Products (5) + Shops (5)

#### **Ticket #48: Custom Products (5 endpoints)**
**Pasta Postman**: `📦 Inventory/CustomProducts`
- Custom Products endpoints

#### **Ticket #49: Webhooks & OCR (7 endpoints)**
**Pasta Postman**: `🔧 Utilities`
- WhatsApp Webhooks (4) + Tesseract/OCR (1) + System (2)

#### **Ticket #50: Validação Final & Testes**
**Pasta Postman**: `🧪 Testing & Validation`
- Validação de todos os endpoints
- Testes de integração
- Documentação final
- Export da collection

## 📊 **Resumo por Fase**

- **Fase 1**: 12 tickets (109 endpoints) - Core Essencial
- **Fase 2**: 15 tickets (92+ endpoints) - ChatBot Principal  
- **Fase 3**: 15 tickets (64 endpoints) - Integrações
- **Fase 4**: 8 tickets (60+ endpoints) - Finalização

**Total**: 50 tickets cobrindo 325+ endpoints
