# 📋 TICKETS COMPLETOS - DOCUMENTAÇÃO API POSTMAN

## 📝 Lista Completa de Tickets para OBVIO-8

### 🏢 **Core/Management (5 entidades)**

#### 📌 Ticket #9: Documentar entidade Profile
- **Controller**: ProfileController
- **Postman path**: User Management/Profiles
- **Endpoints**: GET /profiles, GET /profiles/{id}, POST /profiles, PUT /profiles/{id}, DELETE /profiles/{id}
- **<PERSON>s principais**: id, name, permissions, organization_id

#### 📌 Ticket #10: Documentar entidade Department
- **Controller**: DepartmentController
- **Postman path**: User Management/Departments
- **Endpoints**: GET /departments, GET /departments/{id}, POST /departments, PUT /departments/{id}, DELETE /departments/{id}
- **Campos principais**: id, name, description, organization_id

#### 📌 Ticket #11: Documentar entidade DepartmentUser
- **Controller**: DepartmentUserController
- **Postman path**: User Management/DepartmentUsers
- **Endpoints**: GET /department_users, GET /department_users/{id}, POST /department_users, PUT /department_users/{id}, DELETE /department_users/{id}
- **Campos principais**: id, department_id, user_id

---

### 📦 **Inventory (16 entidades restantes)**

#### 📌 Ticket #12: Documentar entidade ProductHistory
- **Controller**: ProductHistoryController
- **Postman path**: Inventory/ProductHistories
- **Endpoints**: GET /products_histories, GET /products_histories/{id}, POST /products_histories, PUT /products_histories/{id}, DELETE /products_histories/{id}
- **Campos principais**: id, product_id, action, old_values, new_values, user_id

#### 📌 Ticket #13: Documentar entidade CustomProduct
- **Controller**: CustomProductController
- **Postman path**: Inventory/CustomProducts
- **Endpoints**: GET /custom_products, GET /custom_products/{id}, POST /custom_products, PUT /custom_products/{id}, DELETE /custom_products/{id}
- **Campos principais**: id, name, description, price, organization_id

#### 📌 Ticket #14: Documentar entidade Project
- **Controller**: ProjectController
- **Postman path**: Inventory/Projects
- **Endpoints**: GET /projects, GET /projects/{id}, POST /projects, PUT /projects/{id}, DELETE /projects/{id}
- **Campos principais**: id, name, description, client_id, status, organization_id

#### 📌 Ticket #15: Documentar entidade ProjectProduct
- **Controller**: ProjectProductController
- **Postman path**: Inventory/ProjectProducts
- **Endpoints**: GET /projects_products, GET /projects_products/{id}, POST /projects_products, PUT /projects_products/{id}, DELETE /projects_products/{id}
- **Campos principais**: id, project_id, product_id, quantity, price

#### 📌 Ticket #16: Documentar entidade Budget
- **Controller**: BudgetController
- **Postman path**: Inventory/Budgets
- **Endpoints**: GET /budgets, GET /budgets/{id}, POST /budgets, PUT /budgets/{id}, DELETE /budgets/{id}
- **Campos principais**: id, name, description, client_id, total, status, organization_id

#### 📌 Ticket #17: Documentar entidade BudgetProduct
- **Controller**: BudgetProductController
- **Postman path**: Inventory/BudgetProducts
- **Endpoints**: GET /budgets_products, GET /budgets_products/{id}, POST /budgets_products, PUT /budgets_products/{id}, DELETE /budgets_products/{id}
- **Campos principais**: id, budget_id, product_id, quantity, price

#### 📌 Ticket #18: Documentar entidade Batch
- **Controller**: BatchController
- **Postman path**: Inventory/Batches
- **Endpoints**: GET /batches, GET /batches/{id}, POST /batches, PUT /batches/{id}, DELETE /batches/{id}
- **Campos principais**: id, code, product_id, quantity, expiration_date, organization_id

#### 📌 Ticket #19: Documentar entidade Stock
- **Controller**: StockController
- **Postman path**: Inventory/Stocks
- **Endpoints**: GET /stocks, GET /stocks/{id}, POST /stocks, PUT /stocks/{id}, DELETE /stocks/{id}
- **Campos principais**: id, product_id, quantity, min_quantity, max_quantity, organization_id

#### 📌 Ticket #20: Documentar entidade StockEntry
- **Controller**: StockEntryController
- **Postman path**: Inventory/StockEntries
- **Endpoints**: GET /stock_entries, GET /stock_entries/{id}, POST /stock_entries, PUT /stock_entries/{id}, DELETE /stock_entries/{id}
- **Campos principais**: id, product_id, quantity, reason, user_id, organization_id

#### 📌 Ticket #21: Documentar entidade StockExit
- **Controller**: StockExitController
- **Postman path**: Inventory/StockExits
- **Endpoints**: GET /stock_exits, GET /stock_exits/{id}, POST /stock_exits, PUT /stock_exits/{id}, DELETE /stock_exits/{id}
- **Campos principais**: id, product_id, quantity, reason, user_id, organization_id

#### 📌 Ticket #22: Documentar entidade Group
- **Controller**: GroupController
- **Postman path**: Inventory/Groups
- **Endpoints**: GET /groups, GET /groups/{id}, POST /groups, PUT /groups/{id}, DELETE /groups/{id}
- **Campos principais**: id, name, description, organization_id

#### 📌 Ticket #23: Documentar entidade GroupProduct
- **Controller**: GroupProductController
- **Postman path**: Inventory/GroupProducts
- **Endpoints**: GET /groups_products, GET /groups_products/{id}, POST /groups_products, PUT /groups_products/{id}, DELETE /groups_products/{id}
- **Campos principais**: id, group_id, product_id

#### 📌 Ticket #24: Documentar entidade Shop
- **Controller**: ShopController
- **Postman path**: Inventory/Shops
- **Endpoints**: GET /shops, GET /shops/{id}, POST /shops, PUT /shops/{id}, DELETE /shops/{id}
- **Campos principais**: id, name, address, city, state, organization_id

#### 📌 Ticket #25: Documentar entidade Sale
- **Controller**: SaleController
- **Postman path**: Inventory/Sales
- **Endpoints**: GET /sales, GET /sales/{id}, POST /sales, PUT /sales/{id}, DELETE /sales/{id}
- **Campos principais**: id, client_id, total, status, payment_method, organization_id

#### 📌 Ticket #26: Documentar entidade Item
- **Controller**: ItemController
- **Postman path**: Inventory/Items
- **Endpoints**: GET /items, GET /items/{id}, POST /items, PUT /items/{id}, DELETE /items/{id}
- **Campos principais**: id, sale_id, product_id, quantity, price

---

### 🤖 **ChatBot (7 entidades restantes)**

#### 📌 Ticket #27: Documentar entidade Step
- **Controller**: StepController
- **Postman path**: ChatBot/Steps
- **Endpoints**: GET /steps, GET /steps/{id}, POST /steps, PUT /steps/{id}, DELETE /steps/{id}
- **Campos principais**: id, flow_id, name, order, is_input, step, organization_id

#### 📌 Ticket #28: Documentar entidade Component
- **Controller**: ComponentController
- **Postman path**: ChatBot/Components
- **Endpoints**: GET /components, GET /components/{id}, POST /components, PUT /components/{id}, DELETE /components/{id}
- **Campos principais**: id, step_id, type, content, organization_id

#### 📌 Ticket #29: Documentar entidade Button
- **Controller**: ButtonController
- **Postman path**: ChatBot/Buttons
- **Endpoints**: GET /buttons, GET /buttons/{id}, POST /buttons, PUT /buttons/{id}, DELETE /buttons/{id}
- **Campos principais**: id, component_id, text, internal_type, internal_data, organization_id

#### 📌 Ticket #30: Documentar entidade Message
- **Controller**: MessageController
- **Postman path**: ChatBot/Messages
- **Endpoints**: GET /messages, GET /messages/{id}, POST /messages, PUT /messages/{id}, DELETE /messages/{id}
- **Campos principais**: id, campaign_id, client_id, status, external_id, organization_id

#### 📌 Ticket #31: Documentar entidade Template
- **Controller**: TemplateController
- **Postman path**: ChatBot/Templates
- **Endpoints**: GET /templates, GET /templates/{id}, POST /templates, PUT /templates/{id}, DELETE /templates/{id}
- **Campos principais**: id, name, external_id, status, language, organization_id

#### 📌 Ticket #32: Documentar entidade Parameter
- **Controller**: ParameterController
- **Postman path**: ChatBot/Parameters
- **Endpoints**: GET /parameters, GET /parameters/{id}, POST /parameters, PUT /parameters/{id}, DELETE /parameters/{id}
- **Campos principais**: id, template_id, name, example, organization_id

#### 📌 Ticket #33: Documentar entidade PhoneNumber
- **Controller**: PhoneNumberController
- **Postman path**: ChatBot/PhoneNumbers
- **Endpoints**: GET /phone_numbers, GET /phone_numbers/{id}, POST /phone_numbers, PUT /phone_numbers/{id}, DELETE /phone_numbers/{id}
- **Campos principais**: id, number, display_name, status, organization_id

---

### 💳 **ASAAS Integration (4 entidades restantes)**

#### 📌 Ticket #34: Documentar entidade AsaasAccount
- **Controller**: AccountController (ASAAS)
- **Postman path**: Subscription/ASAAS/Accounts
- **Endpoints**: GET /asaas/accounts, GET /asaas/accounts/{id}, POST /asaas/accounts, PUT /asaas/accounts/{id}, DELETE /asaas/accounts/{id}
- **Campos principais**: id, asaas_id, name, email, organization_id

#### 📌 Ticket #35: Documentar entidade AsaasPayment
- **Controller**: PaymentController (ASAAS)
- **Postman path**: Subscription/ASAAS/Payments
- **Endpoints**: GET /asaas/payments, GET /asaas/payments/{id}, POST /asaas/payments, PUT /asaas/payments/{id}, DELETE /asaas/payments/{id}
- **Campos principais**: id, asaas_id, customer_id, value, status, organization_id

#### 📌 Ticket #36: Documentar entidade AsaasSubscription
- **Controller**: SubscriptionController (ASAAS)
- **Postman path**: Subscription/ASAAS/Subscriptions
- **Endpoints**: GET /asaas/subscriptions, GET /asaas/subscriptions/{id}, POST /asaas/subscriptions, PUT /asaas/subscriptions/{id}, DELETE /asaas/subscriptions/{id}
- **Campos principais**: id, asaas_id, customer_id, value, cycle, status, organization_id

#### 📌 Ticket #37: Documentar entidade AsaasOrganization
- **Controller**: AsaasOrganizationController (Resources)
- **Postman path**: Subscription/ASAAS/Resources/Organizations
- **Endpoints**: GET /asaas/resources/organizations, GET /asaas/resources/organizations/{id}, POST /asaas/resources/organizations, PUT /asaas/resources/organizations/{id}, DELETE /asaas/resources/organizations/{id}
- **Campos principais**: id, organization_id, asaas_api_key, asaas_account_id

---

### 📱 **Telegram (4 entidades)**

#### 📌 Ticket #38: Documentar entidade TelegramUser
- **Controller**: TelegramUserController
- **Postman path**: Webhooks/Telegram/Users
- **Endpoints**: GET /telegram_users, GET /telegram_users/{id}, POST /telegram_users, PUT /telegram_users/{id}, DELETE /telegram_users/{id}
- **Campos principais**: id, telegram_id, username, first_name, last_name, organization_id

#### 📌 Ticket #39: Documentar entidade TelegramBot
- **Controller**: TelegramBotController
- **Postman path**: Webhooks/Telegram/Bots
- **Endpoints**: GET /telegram_bots, GET /telegram_bots/{id}, POST /telegram_bots, PUT /telegram_bots/{id}, DELETE /telegram_bots/{id}
- **Campos principais**: id, name, token, username, organization_id

#### 📌 Ticket #40: Documentar entidade TelegramChat
- **Controller**: TelegramChatController
- **Postman path**: Webhooks/Telegram/Chats
- **Endpoints**: GET /telegram_chats, GET /telegram_chats/{id}, POST /telegram_chats, PUT /telegram_chats/{id}, DELETE /telegram_chats/{id}
- **Campos principais**: id, telegram_id, type, title, user_id, organization_id

#### 📌 Ticket #41: Documentar entidade TelegramMessage
- **Controller**: TelegramMessageController
- **Postman path**: Webhooks/Telegram/Messages
- **Endpoints**: GET /telegram_messages, GET /telegram_messages/{id}, POST /telegram_messages, PUT /telegram_messages/{id}, DELETE /telegram_messages/{id}
- **Campos principais**: id, telegram_id, chat_id, text, user_id, organization_id

---

### 🔧 **Utilities (4 entidades)**

#### 📌 Ticket #42: Documentar entidade Import
- **Controller**: ImportController
- **Postman path**: Import/Imports
- **Endpoints**: GET /imports, GET /imports/{id}, POST /imports, PUT /imports/{id}, DELETE /imports/{id}
- **Campos principais**: id, type, status, file_path, results, user_id, organization_id

#### 📌 Ticket #43: Documentar entidade Log
- **Controller**: LogController
- **Postman path**: User Management/Logs
- **Endpoints**: GET /logs, GET /logs/{id}, POST /logs, PUT /logs/{id}, DELETE /logs/{id}
- **Campos principais**: id, action, model, model_id, user_id, organization_id

#### 📌 Ticket #44: Documentar entidade Report
- **Controller**: ReportController
- **Postman path**: User Management/Reports
- **Endpoints**: GET /reports, GET /reports/{id}, POST /reports, PUT /reports/{id}, DELETE /reports/{id}
- **Campos principais**: id, name, type, parameters, user_id, organization_id

#### 📌 Ticket #45: Documentar entidade Subscription
- **Controller**: SubscriptionController
- **Postman path**: Subscription/Subscriptions
- **Endpoints**: GET /subscriptions, GET /subscriptions/{id}, POST /subscriptions, PUT /subscriptions/{id}, DELETE /subscriptions/{id}
- **Campos principais**: id, plan, status, organization_id, expires_at

---

### 🖼️ **Tesseract/OCR (1 entidade)**

#### 📌 Ticket #46: Documentar entidade RawImage
- **Controller**: RawImageController (Tesseract)
- **Postman path**: Tesseract/RawImages
- **Endpoints**: POST /tesseract/raw-images/read
- **Campos principais**: image (base64), language, options

---

### 🤖 **ChatBot Entidades Adicionais (4 entidades)**

#### 📌 Ticket #47: Documentar entidade Conversation
- **Controller**: ConversationController
- **Postman path**: ChatBot/Conversations
- **Endpoints**: GET /conversations, GET /conversations/{id}, POST /conversations, PUT /conversations/{id}, DELETE /conversations/{id}
- **Campos principais**: id, phone_number_id, client_id, status, organization_id

#### 📌 Ticket #48: Documentar entidade Interaction
- **Controller**: InteractionController
- **Postman path**: ChatBot/Interactions
- **Endpoints**: GET /interactions, GET /interactions/{id}, POST /interactions, PUT /interactions/{id}, DELETE /interactions/{id}
- **Campos principais**: id, conversation_id, step_id, input_data, organization_id

#### 📌 Ticket #49: Documentar entidade Category
- **Controller**: CategoryController
- **Postman path**: ChatBot/Categories
- **Endpoints**: GET /categories, GET /categories/{id}, POST /categories, PUT /categories/{id}, DELETE /categories/{id}
- **Campos principais**: id, name, description, organization_id

#### 📌 Ticket #50: Documentar entidade Tag
- **Controller**: TagController
- **Postman path**: ChatBot/Tags
- **Endpoints**: GET /tags, GET /tags/{id}, POST /tags, PUT /tags/{id}, DELETE /tags/{id}
- **Campos principais**: id, name, color, organization_id

---

## 📊 **RESUMO FINAL**

### 🎯 **Total de Entidades: 50**

- **Core/Management**: 5 entidades
- **Inventory**: 19 entidades
- **ChatBot**: 13 entidades
- **ASAAS Integration**: 5 entidades
- **Telegram**: 4 entidades
- **Utilities**: 4 entidades

### 🔄 **Processo de Execução**

1. **Análise**: Revisar controller, model, routes para cada entidade
2. **Documentação**: Criar/atualizar requests no Postman seguindo o padrão
3. **Testes**: Validar todos os endpoints funcionam corretamente
4. **Revisão**: Verificar consistência com domain/factory
5. **Validação**: Marcar critérios de aceite como completos

### 📋 **Checklist por Ticket**

Para cada entidade, verificar:
- [ ] Controller documentado
- [ ] Model com fillable e filters corretos
- [ ] Todos os endpoints no Postman
- [ ] Variáveis de ambiente configuradas
- [ ] Exemplos de request/response
- [ ] Domain/Factory consistentes
- [ ] Testes funcionando

### 🎯 **Resultado Esperado**

- ✅ API completamente documentada
- ✅ Postman collection organizada
- ✅ Padrão consistente estabelecido
- ✅ Base para futuras integrações
- ✅ Facilidade para novos desenvolvedores
