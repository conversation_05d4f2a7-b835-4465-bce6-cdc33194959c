# 📋 MAPEAMENTO COMPLETO DE ENDPOINTS - OBVIO-8

## 🔐 **Authentication (9 endpoints)**
```
POST   /api/login
POST   /api/register
POST   /api/auth/password/forgot
POST   /api/auth/password/reset
POST   /api/auth/password/validate-token
GET    /api/user
POST   /api/logout
POST   /api/logout_all_sessions
DELETE /api/user/delete
```

## 👥 **User Management (31 endpoints)**

### Users (5 endpoints)
```
GET    /api/users
POST   /api/users
GET    /api/users/{id}
PUT    /api/users/{id}
DELETE /api/users/{id}
```

### Notifications (4 endpoints)
```
GET    /api/notifications/unread
GET    /api/notifications/all
POST   /api/notifications/read
POST   /api/notifications/read-one/{id}
```

### Organizations (7 endpoints)
```
GET    /api/organizations
POST   /api/organizations
GET    /api/organizations/{id}
PUT    /api/organizations/{id}
DELETE /api/organizations/{id}
GET    /api/organization/{id}/check-access
GET    /api/organization/{id}/get-to-asaas-payload
```

### Profiles (5 endpoints)
```
GET    /api/profiles
POST   /api/profiles
GET    /api/profiles/{id}
PUT    /api/profiles/{id}
DELETE /api/profiles/{id}
```

### Departments (5 endpoints)
```
GET    /api/departments
POST   /api/departments
GET    /api/departments/{id}
PUT    /api/departments/{id}
DELETE /api/departments/{id}
```

### Department Users (5 endpoints)
```
GET    /api/department_users
POST   /api/department_users
GET    /api/department_users/{id}
PUT    /api/department_users/{id}
DELETE /api/department_users/{id}
```

## 📦 **Inventory (86 endpoints)**

### Brands (5 endpoints)
```
GET    /api/brands
POST   /api/brands
GET    /api/brands/{id}
PUT    /api/brands/{id}
DELETE /api/brands/{id}
```

### Products (5 endpoints)
```
GET    /api/products
POST   /api/products
GET    /api/products/{id}
PUT    /api/products/{id}
DELETE /api/products/{id}
```

### Product Histories (5 endpoints)
```
GET    /api/products_histories
POST   /api/products_histories
GET    /api/products_histories/{id}
PUT    /api/products_histories/{id}
DELETE /api/products_histories/{id}
```

### Custom Products (5 endpoints)
```
GET    /api/custom_products
POST   /api/custom_products
GET    /api/custom_products/{id}
PUT    /api/custom_products/{id}
DELETE /api/custom_products/{id}
```

### Clients (5 endpoints)
```
GET    /api/clients
POST   /api/clients
GET    /api/clients/{id}
PUT    /api/clients/{id}
DELETE /api/clients/{id}
```

### Projects (7 endpoints)
```
GET    /api/projects
POST   /api/projects
GET    /api/projects/{id}
PUT    /api/projects/{id}
DELETE /api/projects/{id}
POST   /api/project/budget/{budget_id}
POST   /api/project/{id}/products/
```

### Project Products (5 endpoints)
```
GET    /api/projects_products
POST   /api/projects_products
GET    /api/projects_products/{id}
PUT    /api/projects_products/{id}
DELETE /api/projects_products/{id}
```

### Budgets (6 endpoints)
```
GET    /api/budgets
POST   /api/budgets
GET    /api/budgets/{id}
PUT    /api/budgets/{id}
DELETE /api/budgets/{id}
POST   /api/budget/{id}/products/
```

### Budget Products (5 endpoints)
```
GET    /api/budgets_products
POST   /api/budgets_products
GET    /api/budgets_products/{id}
PUT    /api/budgets_products/{id}
DELETE /api/budgets_products/{id}
```

### Batches (6 endpoints)
```
GET    /api/batches
POST   /api/batches
GET    /api/batches/{id}
PUT    /api/batches/{id}
DELETE /api/batches/{id}
POST   /api/batch/{id}/process-at-stock/
```

### Stocks (5 endpoints)
```
GET    /api/stocks
POST   /api/stocks
GET    /api/stocks/{id}
PUT    /api/stocks/{id}
DELETE /api/stocks/{id}
```

### Stock Entries (5 endpoints)
```
GET    /api/stock_entries
POST   /api/stock_entries
GET    /api/stock_entries/{id}
PUT    /api/stock_entries/{id}
DELETE /api/stock_entries/{id}
```

### Stock Exits (5 endpoints)
```
GET    /api/stock_exits
POST   /api/stock_exits
GET    /api/stock_exits/{id}
PUT    /api/stock_exits/{id}
DELETE /api/stock_exits/{id}
```

### Groups (5 endpoints)
```
GET    /api/groups
POST   /api/groups
GET    /api/groups/{id}
PUT    /api/groups/{id}
DELETE /api/groups/{id}
```

### Group Products (5 endpoints)
```
GET    /api/groups_products
POST   /api/groups_products
GET    /api/groups_products/{id}
PUT    /api/groups_products/{id}
DELETE /api/groups_products/{id}
```

### Shops (5 endpoints)
```
GET    /api/shops
POST   /api/shops
GET    /api/shops/{id}
PUT    /api/shops/{id}
DELETE /api/shops/{id}
```

### Sales (5 endpoints)
```
GET    /api/sales
POST   /api/sales
GET    /api/sales/{id}
PUT    /api/sales/{id}
DELETE /api/sales/{id}
```

### Items (5 endpoints)
```
GET    /api/items
POST   /api/items
GET    /api/items/{id}
PUT    /api/items/{id}
DELETE /api/items/{id}
```

## 🤖 **ChatBot (92+ endpoints)**

### Flows (6 endpoints)
```
GET    /api/flows
POST   /api/flows
GET    /api/flows/{id}
PUT    /api/flows/{id}
DELETE /api/flows/{id}
POST   /api/flow/save
```

### Steps (5 endpoints)
```
GET    /api/steps
POST   /api/steps
GET    /api/steps/{id}
PUT    /api/steps/{id}
DELETE /api/steps/{id}
```

### Components (6 endpoints)
```
GET    /api/components
POST   /api/components
GET    /api/components/{id}
PUT    /api/components/{id}
DELETE /api/components/{id}
GET    /api/component/get-to-whatsapp-payload/{id}
```

### Buttons (5 endpoints)
```
GET    /api/buttons
POST   /api/buttons
GET    /api/buttons/{id}
PUT    /api/buttons/{id}
DELETE /api/buttons/{id}
```

### Campaigns (13 endpoints)
```
GET    /api/campaigns
POST   /api/campaigns
GET    /api/campaigns/{id}
PUT    /api/campaigns/{id}
DELETE /api/campaigns/{id}
POST   /api/campaign/add-clients/{id}
POST   /api/campaign/remove-client/{id}
POST   /api/campaign/launch/{id}
GET    /api/campaign/{id}/clients
POST   /api/campaign/{id}/categories
POST   /api/campaign/{id}/tags
POST   /api/campaign/{id}/cancel
GET    /api/campaign/{id}/status-history
GET    /api/campaign/{id}/status-timeline
```

### Messages (14 endpoints)
```
GET    /api/messages
POST   /api/messages
GET    /api/messages/{id}
PUT    /api/messages/{id}
DELETE /api/messages/{id}
POST   /api/message/generate-messages/{campaign_id}
GET    /api/message/get-to-whatsapp-payload/{id}
GET    /api/campaign/{id}/messages
GET    /api/campaign/{id}/messages/failed
GET    /api/campaign/{id}/messages/statistics
POST   /api/campaign/{id}/messages/resend-failed
POST   /api/message/{id}/resend
GET    /api/message/{id}/delivery-status
GET    /api/debug/message/getMessagesAvailableToSent
```

### Templates (9 endpoints)
```
GET    /api/templates
POST   /api/templates
GET    /api/templates/{id}
PUT    /api/templates/{id}
DELETE /api/templates/{id}
POST   /api/template/save
POST   /api/template/publish/whatsapp/{id}
POST   /api/template/republish/whatsapp/{id}
GET    /api/template/get-to-whatsapp-payload/{id}
```

### Parameters (5 endpoints)
```
GET    /api/parameters
POST   /api/parameters
GET    /api/parameters/{id}
PUT    /api/parameters/{id}
DELETE /api/parameters/{id}
```

### Phone Numbers (6 endpoints)
```
GET    /api/phone_numbers
POST   /api/phone_numbers
GET    /api/phone_numbers/{id}
PUT    /api/phone_numbers/{id}
DELETE /api/phone_numbers/{id}
GET    /api/whatsapp/testWhatsAppToken/{phone_number_id}
```

### Conversations (5 endpoints)
```
GET    /api/conversations
POST   /api/conversations
GET    /api/conversations/{id}
PUT    /api/conversations/{id}
DELETE /api/conversations/{id}
```

### Interactions (5 endpoints)
```
GET    /api/interactions
POST   /api/interactions
GET    /api/interactions/{id}
PUT    /api/interactions/{id}
DELETE /api/interactions/{id}
```

### Categories (5 endpoints)
```
GET    /api/categories
POST   /api/categories
GET    /api/categories/{id}
PUT    /api/categories/{id}
DELETE /api/categories/{id}
```

### Tags (3 endpoints)
```
GET    /api/tags
GET    /api/tags/most-used
GET    /api/tags/suggestions
```

### WhatsApp Messages (5 endpoints)
```
GET    /api/whatsapp_messages
POST   /api/whatsapp_messages
GET    /api/whatsapp_messages/{id}
PUT    /api/whatsapp_messages/{id}
DELETE /api/whatsapp_messages/{id}
```

### WhatsApp Webhook Entries (2 endpoints)
```
GET    /api/whatsapp_webhook_entries
GET    /api/whatsapp_webhook_entries/{id}
```

### WhatsApp Sync (7 endpoints)
```
POST   /api/whatsapp/sync/message/{id}
POST   /api/whatsapp/sync/campaign/{id}
GET    /api/whatsapp/sync/logs
GET    /api/whatsapp/sync/entity-logs
GET    /api/whatsapp/sync/trends
GET    /api/whatsapp/sync/status-overview
POST   /api/whatsapp/sync/trigger-proactive
```

### Analytics (8 endpoints)
```
GET    /api/analytics/dashboard
GET    /api/analytics/campaign/{id}
POST   /api/analytics/campaigns/multiple
POST   /api/analytics/campaigns/compare
POST   /api/analytics/engagement/record
POST   /api/analytics/engagement/bulk
GET    /api/analytics/message/{id}/engagement
POST   /api/analytics/trigger-calculation
```

## 💳 **ASAAS Integration (39 endpoints)**

### ASAAS Account (11 endpoints)
```
GET    /api/asaas/account/my-account
PUT    /api/asaas/account/my-account
GET    /api/asaas/account/balance
GET    /api/asaas/account/statistics
POST   /api/asaas/account/subaccount
GET    /api/asaas/account/subaccounts
GET    /api/asaas/account/subaccount
GET    /api/asaas/account/subaccount/search
GET    /api/asaas/account/organization/sync
PUT    /api/asaas/account/subaccount
DELETE /api/asaas/account/subaccount
```

### ASAAS Customer (8 endpoints)
```
POST   /api/asaas/customer
GET    /api/asaas/customers
GET    /api/asaas/customer/{client_id}
PUT    /api/asaas/customer/{client_id}
DELETE /api/asaas/customer/{client_id}
GET    /api/asaas/customer/search/email
GET    /api/asaas/customer/search/document
GET    /api/asaas/customer/{client_id}/notifications
```

### ASAAS Payment (5 endpoints)
```
POST   /api/asaas/payment
GET    /api/asaas/payments
GET    /api/asaas/payment/{payment_id}
PUT    /api/asaas/payment/{payment_id}
DELETE /api/asaas/payment/{payment_id}
```

### ASAAS Subscription (6 endpoints)
```
POST   /api/asaas/subscription
GET    /api/asaas/subscriptions
GET    /api/asaas/subscription/{subscription_id}
PUT    /api/asaas/subscription/{subscription_id}
DELETE /api/asaas/subscription/{subscription_id}
GET    /api/asaas/subscription/{subscription_id}/payments
```

### ASAAS Organization (3 endpoints)
```
POST   /api/asaas/organization/create-subaccount
GET    /api/asaas/organization/{id}/status
GET    /api/asaas/organization/{id}/access-check
```

### ASAAS Client (2 endpoints)
```
POST   /api/asaas/client/create-customer
GET    /api/asaas/client/{id}/status
```

### ASAAS Sale (2 endpoints)
```
POST   /api/asaas/sale/create-payment
GET    /api/asaas/sale/{id}/status
```

### ASAAS Old Subscription (3 endpoints)
```
POST   /api/asaas/subscription/create-asaas-subscription
POST   /api/asaas/subscription/sync-subscription/{id}
GET    /api/asaas/subscription/{id}/status
```

## 🗄️ **ASAAS Resources (25 endpoints)**

### ASAAS Organization Resources (5 endpoints)
```
GET    /api/asaas-resources/organizations
POST   /api/asaas-resources/organizations
GET    /api/asaas-resources/organizations/{id}
PUT    /api/asaas-resources/organizations/{id}
DELETE /api/asaas-resources/organizations/{id}
```

### ASAAS Organization Customer Resources (5 endpoints)
```
GET    /api/asaas-resources/organization-customers
POST   /api/asaas-resources/organization-customers
GET    /api/asaas-resources/organization-customers/{id}
PUT    /api/asaas-resources/organization-customers/{id}
DELETE /api/asaas-resources/organization-customers/{id}
```

### ASAAS Client Resources (5 endpoints)
```
GET    /api/asaas-resources/clients
POST   /api/asaas-resources/clients
GET    /api/asaas-resources/clients/{id}
PUT    /api/asaas-resources/clients/{id}
DELETE /api/asaas-resources/clients/{id}
```

### ASAAS Sale Resources (5 endpoints)
```
GET    /api/asaas-resources/sales
POST   /api/asaas-resources/sales
GET    /api/asaas-resources/sales/{id}
PUT    /api/asaas-resources/sales/{id}
DELETE /api/asaas-resources/sales/{id}
```

### ASAAS Subscription Resources (5 endpoints)
```
GET    /api/asaas-resources/subscriptions
POST   /api/asaas-resources/subscriptions
GET    /api/asaas-resources/subscriptions/{id}
PUT    /api/asaas-resources/subscriptions/{id}
DELETE /api/asaas-resources/subscriptions/{id}
```

## 📱 **Telegram (22 endpoints)**

### Telegram Users (5 endpoints)
```
GET    /api/telegram_users
POST   /api/telegram_users
GET    /api/telegram_users/{id}
PUT    /api/telegram_users/{id}
DELETE /api/telegram_users/{id}
```

### Telegram Bots (5 endpoints)
```
GET    /api/telegram_bots
POST   /api/telegram_bots
GET    /api/telegram_bots/{id}
PUT    /api/telegram_bots/{id}
DELETE /api/telegram_bots/{id}
```

### Telegram Chats (5 endpoints)
```
GET    /api/telegram_chats
POST   /api/telegram_chats
GET    /api/telegram_chats/{id}
PUT    /api/telegram_chats/{id}
DELETE /api/telegram_chats/{id}
```

### Telegram Messages (5 endpoints)
```
GET    /api/telegram_messages
POST   /api/telegram_messages
GET    /api/telegram_messages/{id}
PUT    /api/telegram_messages/{id}
DELETE /api/telegram_messages/{id}
```

### Telegram Webhooks (2 endpoints)
```
POST   /api/telegram/receive-message
POST   /api/telegram/{bot_id}/receive
```

## 🔧 **Utilities & Reports (18 endpoints)**

### Imports (6 endpoints)
```
GET    /api/imports
POST   /api/imports
GET    /api/imports/{id}
PUT    /api/imports/{id}
DELETE /api/imports/{id}
POST   /api/import/{id}/process
```

### Logs (3 endpoints)
```
GET    /api/logs/fetch/{id}
GET    /api/logs/fetch_from_organization/{organization_id}
GET    /api/logs/fetch_all
```

### Reports (4 endpoints)
```
GET    /api/reports/stock_entries
GET    /api/reports/stock_exits
GET    /api/reports/{model}/count
GET    /api/reports/{model}/sum/{column}
```

### Subscriptions (6 endpoints)
```
POST   /api/subscriptions
GET    /api/subscriptions/{id}
PUT    /api/subscriptions/{id}
GET    /api/subscriptions/organization/{organizationId}
POST   /api/subscriptions/grant-courtesy
DELETE /api/subscriptions/revoke-courtesy/{organizationId}
```

## 🖼️ **Tesseract/OCR (1 endpoint)**
```
POST   /api/ocr/image-reader/
```

## 🔗 **Webhooks (4 endpoints)**

### WhatsApp Webhooks (2 endpoints)
```
GET    /api/whatsapp/webhook
POST   /api/whatsapp/webhook
```

### WhatsApp Webhook Alternative (2 endpoints)
```
GET    /api/whatsapp/webhook/
POST   /api/whatsapp/webhook/
```

## 🛠️ **System Utilities (2 endpoints)**
```
GET    /api/give-my-php-info
GET    /api/give-my-php-v
```

## 📊 **RESUMO FINAL**

### **Total: 325+ endpoints mapeados**

- 🔐 Authentication: 9
- 👥 User Management: 31
- 📦 Inventory: 86
- 🤖 ChatBot: 92+
- 💳 ASAAS Integration: 39
- 🗄️ ASAAS Resources: 25
- 📱 Telegram: 22
- 🔧 Utilities & Reports: 18
- 🖼️ Tesseract/OCR: 1
- 🔗 Webhooks: 4
- 🛠️ System: 2

**Este é o mapeamento COMPLETO de todos os endpoints disponíveis no sistema Obvio.**
