# EPIC: Sistema de Webhook Multi-Organizacional WhatsApp

## Visão Geral da Epic

**Objetivo:** Implementar sistema de webhook unificado para múltiplas organizações no WhatsApp, permitindo que cada organização tenha seu próprio token de verificação enquanto mantém múltiplos números por organização.

**Valor de Negócio:** Escalabilidade para suportar milhares de organizações com webhook único, melhor isolamento de dados e configuração flexível por cliente.

**Duração Estimada:** 5-7 dias úteis
**Complexidade:** Média
**Prioridade:** Alta

---

## Ticket 1: Resolver Conflitos de Rotas Webhook

### Descrição
Atualmente existem rotas duplicadas para o webhook WhatsApp que causam conflitos. Precisamos consolidar em um único controller e endpoint.

### Acceptance Criteria
- [ ] Remover rotas duplicadas em `routes/api.php`
- [ ] Manter apenas `WhatsAppWebhookController` como controller principal
- [ ] Endpoint único: `GET/POST /api/whatsapp/webhook`
- [ ] Remover `WebhookController` da pasta Meta/WhatsApp
- [ ] Testes existentes continuam passando
- [ ] Webhook atual continua funcionando sem quebras

### Tarefas Técnicas
1. Analisar diferenças entre `WhatsAppWebhookController` e `WebhookController`
2. Consolidar lógica no `WhatsAppWebhookController`
3. Remover rotas duplicadas
4. Atualizar testes se necessário
5. Validar funcionamento com webhook atual

### Definição de Pronto
- Apenas um endpoint de webhook ativo
- Testes passando
- Documentação atualizada
- Webhook funcionando em ambiente de desenvolvimento

---

## Ticket 2: Organizar Configurações WhatsApp

### Descrição
Organizar e completar as configurações WhatsApp existentes em `config/whatsapp.php`, adicionando campos necessários para webhook e mantendo a estrutura atual.

### Acceptance Criteria
- [ ] Adicionar `webhook_secret` em `config/whatsapp.php`
- [ ] Organizar variáveis de ambiente no `.env.example`
- [ ] Manter `config/whatsapp.php` como arquivo principal
- [ ] Manter compatibilidade total com código existente
- [ ] Documentar configurações adicionais

### Tarefas Técnicas
1. Adicionar configurações faltantes em `config/whatsapp.php`
2. Atualizar `.env.example` e `.env.docs`
3. Verificar se código existente continua funcionando
4. Documentar novas configurações

### Configurações a Adicionar
```php
// config/whatsapp.php
return [
    'phone_number_id' => env('WHATSAPP_PHONE_NUMBER_ID'),
    'business_id' => env('WHATSAPP_BUSINESS_ID'),
    'access_token' => env('WHATSAPP_ACCESS_TOKEN'),
    'base_url' => env('WHATSAPP_API_BASE_URL', 'https://graph.facebook.com/v23.0'),
    'webhook_verify_token' => env('WHATSAPP_WEBHOOK_VERIFY_TOKEN', 'your_verify_token'),
    'webhook_secret' => env('WHATSAPP_WEBHOOK_SECRET'), // NOVO
];
```

### Definição de Pronto
- Configurações organizadas em `config/whatsapp.php`
- Código existente funcionando
- Documentação atualizada
- Variáveis de ambiente organizadas

---

## Ticket 3: Criar Entidade WhatsAppWebhookLog Completa

### Descrição
Implementar entidade completa `WhatsAppWebhookLog` seguindo padrões do projeto com Domain, Model, Factory, Repository e UseCases para auditoria de webhooks.

### Acceptance Criteria
- [ ] Migration para tabela `whatsapp_webhook_logs`
- [ ] Model `WhatsAppWebhookLog` com relacionamentos
- [ ] Domain `WhatsAppWebhookLog` com métodos de negócio
- [ ] Factory `WhatsAppWebhookLogFactory` para testes
- [ ] Repository `WhatsAppWebhookLogRepository` com métodos CRUD
- [ ] UseCases: Store, Update, Get, GetAll, Delete
- [ ] UseCase adicional: `LogWebhookEvent` para uso no controller
- [ ] Testes unitários para todos os componentes

### Tarefas Técnicas
1. Criar migration para `whatsapp_webhook_logs`
2. Criar model `App\Models\WhatsAppWebhookLog`
3. Criar domain `App\Domains\WhatsAppWebhookLog`
4. Criar factory `App\Factories\WhatsAppWebhookLogFactory`
5. Criar repository `App\Repositories\WhatsAppWebhookLogRepository`
6. Criar UseCases básicos (Store, Update, Get, GetAll, Delete)
7. Criar UseCase `LogWebhookEvent` para controller
8. Implementar testes unitários completos

### Estrutura da Tabela
```sql
CREATE TABLE whatsapp_webhook_logs (
    id BIGINT PRIMARY KEY,
    organization_id BIGINT NULL,
    phone_number_id VARCHAR(255) NULL,
    event_type ENUM('message', 'status', 'other'),
    webhook_payload JSON,
    processed_at TIMESTAMP NULL,
    processing_status ENUM('pending', 'success', 'failed'),
    error_message TEXT NULL,
    created_at TIMESTAMP,
    updated_at TIMESTAMP,
    INDEX(organization_id),
    INDEX(phone_number_id),
    INDEX(event_type),
    INDEX(processing_status)
);
```

### UseCase LogWebhookEvent
```php
class LogWebhookEvent
{
    public function perform(
        ?int $organizationId,
        string $phoneNumberId,
        string $eventType,
        array $webhookPayload,
        string $processingStatus = 'pending',
        ?string $errorMessage = null
    ): WhatsAppWebhookLog {
        // Criar domain e salvar via repository
    }
}
```

### Definição de Pronto
- Entidade completa implementada (Domain, Model, Factory, Repository)
- UseCases básicos funcionando
- UseCase `LogWebhookEvent` implementado
- Testes unitários passando
- Documentação da estrutura

---

## Ticket 4: Adicionar Token de Verificação por Organização

### Descrição
Adicionar campo `whatsapp_webhook_verify_token` na tabela organizations e atualizar toda a estrutura (Model, Domain, Factory) para suportar tokens específicos por organização.

### Acceptance Criteria
- [ ] Migration para adicionar campo em `organizations`
- [ ] Campo nullable para manter compatibilidade
- [ ] Atualizar model `Organization` com novo campo
- [ ] Atualizar domain `Organization` com novo campo
- [ ] Atualizar factory `OrganizationFactory`
- [ ] Atualizar métodos `toArray()`, `toStoreArray()`, `toUpdateArray()` no domain
- [ ] Manter funcionamento atual como fallback

### Tarefas Técnicas
1. Criar migration para adicionar campo
2. Atualizar model `App\Models\Organization`
3. Atualizar domain `App\Domains\Organization`
4. Atualizar factory `App\Factories\OrganizationFactory`
5. Atualizar testes existentes
6. Documentar novo campo

### Migration
```sql
ALTER TABLE organizations 
ADD whatsapp_webhook_verify_token VARCHAR(255) NULL 
AFTER whatsapp_webhook_verify_token;

CREATE INDEX idx_organizations_webhook_token 
ON organizations(whatsapp_webhook_verify_token);
```

### Definição de Pronto
- Campo adicionado na tabela
- Model atualizado com fillable e casts
- Domain atualizado com novo campo
- Factory atualizada
- Métodos do domain atualizados
- Testes passando
- Documentação atualizada

---

## Ticket 5: Implementar UseCase para Buscar Organização por PhoneNumber

### Descrição
Criar UseCase `FetchOrganizationFromPhoneNumber` seguindo padrões do projeto, que usa repository do PhoneNumber para buscar organização através do `phone_number_id`.

### Acceptance Criteria
- [ ] UseCase `FetchOrganizationFromPhoneNumber` criado
- [ ] Método `fetchByWhatsAppPhoneNumberId` no `PhoneNumberRepository`
- [ ] Retorna domain `PhoneNumber` com organização carregada
- [ ] Tratamento de casos onde não encontra
- [ ] Performance otimizada com eager loading
- [ ] Testes unitários completos

### Tarefas Técnicas
1. Criar método `fetchByWhatsAppPhoneNumberId` em `PhoneNumberRepository`
2. Criar UseCase `App\UseCases\Organization\FetchOrganizationFromPhoneNumber`
3. Implementar lógica de busca com eager loading
4. Implementar tratamento de erros
5. Criar testes unitários abrangentes

### Implementação Repository
```php
// PhoneNumberRepository
public function fetchByWhatsAppPhoneNumberId(string $phoneNumberId): ?PhoneNumber
{
    $model = PhoneNumber::where('whatsapp_phone_number_id', $phoneNumberId)
                       ->where('is_active', true)
                       ->with(['organization' => function($query) {
                           $query->where('is_active', true)
                                 ->where('is_suspended', false);
                       }])
                       ->first();

    return $model ? $this->buildFromModel($model) : null;
}
```

### Implementação UseCase
```php
class FetchOrganizationFromPhoneNumber
{
    public function perform(string $phoneNumberId): ?array
    {
        $phoneNumber = $this->phoneNumberRepository->fetchByWhatsAppPhoneNumberId($phoneNumberId);

        if (!$phoneNumber || !$phoneNumber->organization) {
            return null;
        }

        return [
            'organization' => $phoneNumber->organization,
            'phone_number' => $phoneNumber
        ];
    }
}
```

### Definição de Pronto
- Método no repository implementado
- UseCase implementado seguindo padrões
- Busca funcionando corretamente
- Performance otimizada
- Testes unitários passando
- Documentação dos componentes

---

## Ticket 6: Implementar Verificação de Token por Organização

### Descrição
Criar UseCase para verificação de webhook e refatorar método `verify` do controller para usar repository pattern, seguindo padrões do projeto.

### Acceptance Criteria
- [ ] UseCase `VerifyWebhookToken` criado
- [ ] Método no `OrganizationRepository` para buscar por token
- [ ] Controller `verify` refatorado para usar UseCase
- [ ] Fallback para token global mantido
- [ ] Logs de verificação implementados
- [ ] Resposta correta para Meta (challenge)
- [ ] Tratamento de tokens inválidos
- [ ] Testes para todos os cenários

### Tarefas Técnicas
1. Criar método `fetchByWebhookToken` em `OrganizationRepository`
2. Criar UseCase `VerifyWebhookToken`
3. Refatorar método `verify` em `WhatsAppWebhookController`
4. Implementar logging via `LogWebhookEvent`
5. Criar testes para todos os cenários

### Implementação Repository
```php
// OrganizationRepository
public function fetchByWebhookToken(string $token): ?Organization
{
    $model = Organization::where('whatsapp_webhook_verify_token', $token)
                        ->where('is_active', true)
                        ->where('is_suspended', false)
                        ->first();

    return $model ? $this->buildFromModel($model) : null;
}
```

### Implementação UseCase
```php
class VerifyWebhookToken
{
    public function perform(string $mode, string $token): array
    {
        if ($mode !== 'subscribe') {
            return ['success' => false, 'error' => 'Invalid mode'];
        }

        // Prioridade 1: Token de organização
        $organization = $this->organizationRepository->fetchByWebhookToken($token);
        if ($organization) {
            return [
                'success' => true,
                'organization' => $organization,
                'type' => 'organization'
            ];
        }

        // Prioridade 2: Token global
        if ($token === config('whatsapp.webhook_verify_token')) {
            return [
                'success' => true,
                'organization' => null,
                'type' => 'global'
            ];
        }

        return ['success' => false, 'error' => 'Invalid token'];
    }
}
```

### Implementação Controller
```php
public function verify(Request $request): JsonResponse
{
    $mode = $request->query('hub_mode');
    $token = $request->query('hub_verify_token');
    $challenge = $request->query('hub_challenge');

    $verifyResult = $this->verifyWebhookToken->perform($mode, $token);

    if ($verifyResult['success']) {
        // Log via UseCase
        $this->logWebhookEvent->perform(
            $verifyResult['organization']?->id,
            null,
            'verification',
            ['type' => $verifyResult['type']],
            'success'
        );

        return response()->json((int) $challenge);
    }

    // Log falha
    $this->logWebhookEvent->perform(
        null,
        null,
        'verification',
        ['token' => substr($token, 0, 8) . '...', 'mode' => $mode],
        'failed',
        $verifyResult['error']
    );

    return response()->json(['error' => 'Forbidden'], 403);
}
```

### Definição de Pronto
- Repository method implementado
- UseCase implementado seguindo padrões
- Controller refatorado
- Logs via UseCase implementados
- Testes cobrindo todos os cenários
- Documentação atualizada

---

## Ticket 7: Implementar Processamento Unificado de Webhook

### Descrição
Criar UseCases para processamento de webhook e refatorar método `handle` do controller para usar repository pattern e seguir padrões do projeto.

### Acceptance Criteria
- [ ] UseCase `ProcessWebhookMessage` criado
- [ ] UseCase `ProcessWebhookStatus` criado
- [ ] UseCase `ValidateWebhookPayload` criado
- [ ] Controller `handle` refatorado para usar UseCases
- [ ] Identificação via `FetchOrganizationFromPhoneNumber`
- [ ] Logging via `LogWebhookEvent`
- [ ] Manter compatibilidade com ChatBot existente
- [ ] Tratamento de erros robusto
- [ ] Validação de payload do Meta

### Tarefas Técnicas
1. Criar UseCase `ValidateWebhookPayload`
2. Criar UseCase `ProcessWebhookMessage`
3. Criar UseCase `ProcessWebhookStatus`
4. Refatorar método `handle` em `WhatsAppWebhookController`
5. Integrar com UseCases existentes
6. Criar testes de integração

### Implementação UseCases
```php
class ValidateWebhookPayload
{
    public function perform(array $webhookData): bool
    {
        return isset($webhookData['object']) &&
               $webhookData['object'] === 'whatsapp_business_account' &&
               isset($webhookData['entry']) &&
               is_array($webhookData['entry']);
    }
}

class ProcessWebhookMessage
{
    public function perform(array $changeValue, Organization $organization, PhoneNumber $phoneNumber): array
    {
        // Processar mensagens recebidas
        // Integrar com ChatBot existente
        return $this->chatBotService->processWebhook([
            'message' => $changeValue['messages'][0],
            'metadata' => $changeValue['metadata'] ?? [],
            'contacts' => $changeValue['contacts'] ?? [],
            'organization' => $organization,
            'phone_number' => $phoneNumber
        ]);
    }
}

class ProcessWebhookStatus
{
    public function perform(array $changeValue, Organization $organization, PhoneNumber $phoneNumber): array
    {
        // Processar status de mensagens
        // Atualizar logs de campanha
        return [
            'type' => 'status',
            'processed' => count($changeValue['statuses'] ?? []),
            'organization_id' => $organization->id
        ];
    }
}
```

### Implementação Controller
```php
public function handle(Request $request): JsonResponse
{
    try {
        $webhookData = $request->all();

        // Validar via UseCase
        if (!$this->validateWebhookPayload->perform($webhookData)) {
            return response()->json(['error' => 'Invalid webhook data'], 400);
        }

        $results = [];

        foreach ($webhookData['entry'] ?? [] as $entry) {
            foreach ($entry['changes'] ?? [] as $change) {
                if ($change['field'] === 'messages') {
                    $result = $this->processChange($change['value']);
                    if ($result) {
                        $results[] = $result;
                    }
                }
            }
        }

        return response()->json([
            'status' => 'success',
            'processed' => count($results),
            'results' => $results
        ]);

    } catch (\Exception $e) {
        $this->logWebhookEvent->perform(
            null, null, 'error', $request->all(), 'failed', $e->getMessage()
        );

        return response()->json([
            'status' => 'error',
            'message' => 'Internal server error'
        ], 500);
    }
}

private function processChange(array $changeValue): ?array
{
    $phoneNumberId = $changeValue['metadata']['phone_number_id'] ?? null;

    if (!$phoneNumberId) {
        return null;
    }

    // Buscar via UseCase
    $identificationResult = $this->fetchOrganizationFromPhoneNumber->perform($phoneNumberId);

    if (!$identificationResult) {
        return null;
    }

    $organization = $identificationResult['organization'];
    $phoneNumber = $identificationResult['phone_number'];

    // Log via UseCase
    $this->logWebhookEvent->perform(
        $organization->id,
        $phoneNumberId,
        isset($changeValue['messages']) ? 'message' : 'status',
        $changeValue,
        'pending'
    );

    // Processar via UseCases
    if (isset($changeValue['messages'])) {
        return $this->processWebhookMessage->perform($changeValue, $organization, $phoneNumber);
    } elseif (isset($changeValue['statuses'])) {
        return $this->processWebhookStatus->perform($changeValue, $organization, $phoneNumber);
    }

    return null;
}
```

### Definição de Pronto
- UseCases implementados seguindo padrões
- Controller refatorado
- Identificação via UseCase funcionando
- Roteamento por tipo implementado
- Logging via UseCase completo
- Compatibilidade mantida
- Testes de integração passando

---

## Ticket 8: Implementar Validação de Assinatura

### Descrição
Criar UseCase para validação de assinatura X-Hub-Signature-256 do Meta seguindo padrões do projeto.

### Acceptance Criteria
- [ ] UseCase `ValidateWebhookSignature` criado
- [ ] Validação de assinatura X-Hub-Signature-256
- [ ] Configuração de webhook secret em `config/whatsapp.php`
- [ ] Integração no controller via UseCase
- [ ] Rejeição de webhooks com assinatura inválida
- [ ] Logs via `LogWebhookEvent` para tentativas inválidas
- [ ] Testes para validação de assinatura

### Tarefas Técnicas
1. Criar UseCase `ValidateWebhookSignature`
2. Adicionar configuração webhook secret
3. Integrar UseCase no controller
4. Adicionar logging de segurança via UseCase
5. Criar testes para validação

### Implementação UseCase
```php
class ValidateWebhookSignature
{
    public function perform(string $payload, string $signature): bool
    {
        $secret = config('whatsapp.webhook_secret');

        if (!$signature || !$secret) {
            return false;
        }

        $expectedSignature = 'sha256=' . hash_hmac('sha256', $payload, $secret);

        return hash_equals($expectedSignature, $signature);
    }
}
```

### Implementação Controller
```php
public function handle(Request $request): JsonResponse
{
    // Validar assinatura via UseCase
    $signature = $request->header('X-Hub-Signature-256');
    $payload = $request->getContent();

    if (!$this->validateWebhookSignature->perform($payload, $signature)) {
        $this->logWebhookEvent->perform(
            null, null, 'security',
            ['signature' => substr($signature, 0, 20) . '...'],
            'failed',
            'Invalid signature'
        );

        return response()->json(['error' => 'Forbidden'], 403);
    }

    // Continuar processamento...
}
```

### Definição de Pronto
- UseCase implementado seguindo padrões
- Validação de assinatura funcionando
- Webhooks inválidos rejeitados
- Logs via UseCase funcionando
- Testes passando
- Documentação de segurança

---

## Ticket 9: Criar Testes Abrangentes

### Descrição
Implementar suite completa de testes unitários e de integração para todo o sistema de webhook multi-organizacional.

### Acceptance Criteria
- [ ] Testes unitários para `OrganizationIdentifier`
- [ ] Testes de integração para webhook completo
- [ ] Testes para verificação de token por organização
- [ ] Testes para roteamento de eventos
- [ ] Testes para logging de webhook
- [ ] Testes para validação de assinatura
- [ ] Cobertura de testes > 90%

### Tarefas Técnicas
1. Criar testes para `OrganizationIdentifier`
2. Criar testes de webhook end-to-end
3. Criar testes para verificação de token
4. Criar testes para processamento de eventos
5. Criar testes para logging
6. Verificar cobertura de testes

### Cenários de Teste
- Webhook com token de organização válido
- Webhook com token global (fallback)
- Webhook com token inválido
- Webhook com phone_number_id válido
- Webhook com phone_number_id inexistente
- Webhook com payload de mensagem
- Webhook com payload de status
- Webhook com assinatura válida/inválida
- Webhook com organização inativa/suspensa

### Definição de Pronto
- Todos os testes passando
- Cobertura adequada
- Testes de regressão funcionando
- Documentação de testes
- CI/CD configurado

---

## Ticket 10: Documentação e Configuração

### Descrição
Criar documentação completa para configuração e uso do sistema de webhook multi-organizacional.

### Acceptance Criteria
- [ ] Documentação de configuração no Meta Business
- [ ] Guia de configuração por organização
- [ ] Documentação de troubleshooting
- [ ] Exemplos de payloads de webhook
- [ ] Documentação de API interna
- [ ] Guia de migração (se necessário)

### Tarefas Técnicas
1. Documentar configuração no Meta Business
2. Criar guia de configuração por organização
3. Documentar troubleshooting comum
4. Criar exemplos de uso
5. Documentar APIs internas
6. Criar guia de migração

### Documentação Necessária
- Como configurar webhook no Meta Business
- Como definir token por organização
- Como testar webhook
- Como debugar problemas
- Estrutura de logs
- APIs de consulta de logs

### Definição de Pronto
- Documentação completa
- Guias funcionais
- Exemplos testados
- Troubleshooting documentado
- Migração documentada (se aplicável)

---

## Resumo da Epic

### Ordem de Implementação
1. **Ticket 1-2**: Preparação (rotas + configs)
2. **Ticket 3-4**: Infraestrutura (logs + token)
3. **Ticket 5-7**: Core (identificação + processamento)
4. **Ticket 8**: Segurança (validação)
5. **Ticket 9-10**: Qualidade (testes + docs)

### Dependências
- Ticket 5 depende de Ticket 4
- Ticket 6-7 dependem de Ticket 5
- Ticket 8 pode ser paralelo a 6-7
- Ticket 9 depende de todos os anteriores
- Ticket 10 pode ser paralelo aos demais

### Riscos e Mitigações
- **Risco**: Quebrar webhook atual
- **Mitigação**: Manter compatibilidade total
- **Risco**: Performance da busca
- **Mitigação**: Índices otimizados
- **Risco**: Configuração complexa
- **Mitigação**: Documentação detalhada
