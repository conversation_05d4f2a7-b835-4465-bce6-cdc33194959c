# Epic: Campanhas e Disparo de Mensagens via WhatsApp - Melhorias e Expansão

## Overview

Esta Epic visa aprimorar significativamente o sistema de campanhas WhatsApp existente, transformando-o em uma plataforma robusta e completa para marketing digital. O sistema atual já possui funcionalidades básicas de criação, lançamento e envio de campanhas, mas necessita de melhorias substanciais em organização, monitoramento, reenvio e análise de performance.

### Funcionalidades Atuais Identificadas:
- ✅ Criação e gestão básica de campanhas (`Campaign` model)
- ✅ Sistema de templates com publicação no WhatsApp
- ✅ Geração automática de mensagens por campanha
- ✅ Envio automatizado via cron job (`whatsapp:send-messages`)
- ✅ Integração com WhatsApp Business API
- ✅ Webhook para recebimento de status de entrega
- ✅ Sistema básico de status de mensagens (`MessageStatus` enum)

### Melhorias Propostas:

#### 1. **Sistema de Categorização e Tags**
Implementar um sistema robusto de organização de campanhas através de categorias e tags, permitindo melhor segmentação e organização para empresas com alto volume de campanhas.

#### 2. **Aprimoramento do Sistema de Status**
Expandir o `MessageStatus` enum atual e criar um `CampaignStatus` enum para melhor rastreamento do ciclo de vida das campanhas, incluindo estados como: rascunho, agendada, em execução, pausada, concluída, cancelada, com falhas.

#### 3. **Funcionalidades Avançadas de Mensagens**
Desenvolver endpoints específicos para gestão granular de mensagens, incluindo listagem por campanha, reenvio de mensagens falhadas, envio individual e consulta de status em tempo real.

#### 4. **Monitoramento e Sincronização Aprimorados**
Implementar sistema de sincronização bidirecional com WhatsApp, incluindo consulta ativa de status de mensagens e job automatizado para atualização de mensagens falhadas.

#### 5. **Analytics e Métricas Avançadas**
Criar sistema completo de métricas e relatórios, incluindo taxa de entrega, tempo de resposta, engajamento e ROI por campanha.

## Resumo do Plano de Implementação

Esta Epic será implementada em 5 fases principais, cada uma construindo sobre a anterior para criar um sistema robusto de campanhas WhatsApp:

**Fase 1**: Sistema de Categorização - Implementar CRUD completo de categorias e sistema de tags flexível
**Fase 2**: Aprimoramento de Status - Adicionar enum CampaignStatus mantendo compatibilidade com booleans existentes
**Fase 3**: Gestão Avançada de Mensagens - Endpoints para reenvio inteligente e operações granulares
**Fase 4**: Sincronização Ativa - Jobs para consulta proativa de status no WhatsApp API
**Fase 5**: Analytics Avançadas - Dashboard completo com métricas e relatórios

## Plano de Implementação Detalhado

### 1. Sistema de Categorização e Tags

#### Rotas/Endpoints Necessários:
- `POST /api/categories` - Criar categoria (CRUD completo)
- `GET /api/categories` - Listar categorias
- `PUT /api/categories/{id}` - Atualizar categoria
- `DELETE /api/categories/{id}` - Deletar categoria
- `POST /api/campaign/{id}/tags` - Adicionar tags à campanha
- `DELETE /api/campaign/{id}/tags/{tag_id}` - Remover tag da campanha
- `GET /api/tags` - Listar todas as tags disponíveis
- `GET /api/campaigns?category_ids={id1,id2}&tags={tag1,tag2}` - Filtrar campanhas

#### Database:
- **Tabela `categories`**: CRUD próprio para categorias reutilizáveis. Campos: id, name, description, color, type (enum: campaign, template, client), organization_id, created_at, updated_at.
- **Tabela `campaigns_categories`**: Relacionamento many-to-many entre campanhas e categorias. Campos: campaign_id, category_id, assigned_at.
- **Tabela `tags`**: Sistema de tags flexível para marcação livre. Campos: id, name, organization_id, usage_count, created_at, updated_at.
- **Tabela `campaign_tag_assignments`**: Relacionamento many-to-many entre campanhas e tags. Campos: campaign_id, tag_id, assigned_at.

#### Domínios:
- **Category**: Domínio reutilizável para categorias do sistema (campanhas, templates, etc.)
- **Tag**: Gerenciar tags com auto-complete e sugestões baseadas em uso
- **CampaignCategoryAssignment**: Relacionamento entre campanhas e categorias
- **CampaignTagAssignment**: Relacionamento entre campanhas e tags

#### Usecases:
- **Category/Store**: Criar categoria com validação de duplicatas
- **Category/Update**: Atualizar categoria existente
- **Category/Delete**: Deletar categoria (verificar dependências)
- **Campaign/AssignCategories**: Atribuir múltiplas categorias
- **Campaign/AssignTags**: Atribuir múltiplas tags com validação
- **Campaign/GetByFilters**: Busca avançada por categoria, tags e outros filtros

### 2. Aprimoramento do Sistema de Status

#### Enums Necessários:
- **CampaignStatus**: Coexistir com booleans existentes para compatibilidade:
  - `DRAFT` (1) - Campanha em criação (is_sent=false, is_sending=false, is_scheduled=false)
  - `SCHEDULED` (2) - Agendada para envio futuro (is_scheduled=true)
  - `SENDING` (3) - Enviando mensagens (is_sending=true)
  - `COMPLETED` (4) - Envio concluído com sucesso (is_sent=true, is_sending=false)
  - `FAILED` (5) - Falhou durante o envio (is_sending=false, com mensagens falhadas)
  - `CANCELLED` (6) - Cancelada antes do envio

#### Rotas/Endpoints:
- `GET /api/campaign/{id}/status-history` - Histórico de mudanças de status
- `POST /api/campaign/{id}/cancel` - Cancelar campanha antes do envio

#### Database:
- **Tabela `campaign_status_history`**: Rastrear mudanças de status. Campos: id, campaign_id, old_status, new_status, reason, user_id, created_at.
- **Migração para `campaigns`**: Adicionar campo `status` enum (manter booleans existentes), adicionar `cancelled_at`, `failed_at`.

#### Usecases:
- **Campaign/Cancel**: Cancelar campanha antes do envio
- **Campaign/UpdateStatusFromMessages**: Job/UseCase para sincronizar status baseado nas mensagens
- **Campaign/GetStatusHistory**: Histórico de mudanças de status

#### Jobs/Cron:
- **SyncCampaignStatusWithBooleans**: Job que roda a cada 5 minutos para garantir que o enum status está sincronizado com os booleans existentes, detectando inconsistências e corrigindo automaticamente.

### 3. Funcionalidades Avançadas de Mensagens

#### Rotas/Endpoints Necessários:
- `GET /api/campaign/{id}/messages` - Listar mensagens de uma campanha específica
- `POST /api/campaign/{id}/messages/resend-failed` - Reenviar apenas mensagens falhadas
- `POST /api/message/{id}/resend` - Reenviar mensagem individual
- `GET /api/message/{id}/status` - Consultar status detalhado de uma mensagem
- `POST /api/message/send-individual` - Enviar mensagem individual fora de campanha
- `GET /api/messages/failed` - Listar todas as mensagens falhadas da organização
- `POST /api/messages/bulk-resend` - Reenvio em lote com filtros

#### Database:
- **Tabela `message_delivery_attempts`**: Rastrear tentativas de envio. Campos: id, message_id, attempt_number, status, error_message, attempted_at, whatsapp_response_json.
- **Migração para `messages`**: Adicionar campos `delivery_attempts` (int), `last_attempt_at` (timestamp), `max_retries` (int, default 3).

#### Domínios Existentes Utilizados:
- **Message**: Domínio existente será expandido com métodos para retry
- **WhatsAppMessage**: Domínio existente para integração com WhatsApp

#### Novos Domínios:
- **MessageDeliveryAttempt**: Representa uma tentativa de envio específica com detalhes do erro e resposta do WhatsApp

#### Usecases:
- **Message/GetByCampaign**: Listar mensagens com filtros avançados (status, cliente, data)
- **Message/ResendFailed**: Reenviar mensagens falhadas de uma campanha específica
- **Message/Resend**: Reenviar mensagem individual com validação de tentativas
- **Message/SendIndividual**: Enviar mensagem única sem campanha (para testes ou comunicação direta)
- **Message/BulkResend**: Reenvio em lote com filtros e validações
- **Message/GetDeliveryStatus**: Status detalhado com histórico de tentativas

#### Lógica de Retry:
O sistema utilizará os domínios existentes (Message, WhatsAppMessage) expandindo-os com lógica de retry. Quando uma mensagem falha:
1. Incrementa `delivery_attempts`
2. Cria registro em `message_delivery_attempts`
3. Agenda próxima tentativa baseada em backoff (1min, 5min, 15min)
4. Após `max_retries`, marca como definitivamente falhada

### 4. Monitoramento e Sincronização Aprimorados

#### Conceito de Sincronização:
O sistema atual depende apenas de webhooks para atualização de status. Esta fase implementa **sincronização ativa**, onde o sistema consulta proativamente a API do WhatsApp para verificar o status real das mensagens, especialmente aquelas que falharam ou não receberam confirmação via webhook.

#### Rotas/Endpoints:
- `POST /api/whatsapp/sync-message-status/{id}` - Forçar sincronização de mensagem específica
- `POST /api/whatsapp/sync-campaign-status/{id}` - Sincronizar todas as mensagens de uma campanha
- `GET /api/whatsapp/delivery-reports` - Relatórios de entrega consolidados
- `GET /api/whatsapp/sync-logs` - Histórico de sincronizações

#### Database:
- **Tabela `whatsapp_sync_logs`**: Log de sincronizações. Campos: id, sync_type (enum: message, campaign), entity_id, status (success/failed), response_data_json, error_message, synced_at.
- **Migração para `whatsapp_messages`**: Adicionar campos `last_status_check` (timestamp), `status_check_count` (int), `delivery_confirmed_at` (timestamp).

#### Jobs/Cron Necessários:
- **SyncPendingMessagesStatus**: Job que roda a cada 10 minutos para consultar status de mensagens que estão há mais de 30 minutos sem confirmação de entrega
- **UpdateCampaignStatusFromMessages**: Job que analisa o status de todas as mensagens de uma campanha e atualiza o status da campanha automaticamente
- **CleanupOldSyncLogs**: Job de limpeza semanal para remover logs de sincronização antigos

#### Usecases:
- **WhatsApp/SyncMessageStatus**: Consultar API do WhatsApp para status real de uma mensagem específica
- **WhatsApp/SyncCampaignStatus**: Sincronizar status de todas as mensagens de uma campanha
- **WhatsApp/ProcessImprovedWebhook**: Processar webhook com validação aprimorada e log detalhado
- **WhatsApp/ReconcileMessageStatuses**: Identificar e corrigir diferenças entre status local e WhatsApp
- **WhatsApp/GenerateDeliveryReport**: Relatório consolidado de entregas com métricas de sincronização

#### Fluxo de Sincronização:
1. **Identificação**: Job identifica mensagens sem confirmação há mais de 30 minutos
2. **Consulta**: Faz request para WhatsApp API consultando status da mensagem
3. **Comparação**: Compara status retornado com status local
4. **Atualização**: Atualiza status local se houver diferença
5. **Log**: Registra resultado da sincronização em `whatsapp_sync_logs`

### 5. Analytics e Métricas Avançadas

#### Rotas/Endpoints:
- `GET /api/campaign/{id}/analytics` - Analytics detalhadas da campanha
- `GET /api/campaigns/analytics/summary` - Resumo de todas as campanhas
- `GET /api/campaigns/analytics/comparison` - Comparar performance entre campanhas
- `POST /api/campaigns/analytics/export` - Exportar relatórios em PDF/Excel
- `GET /api/analytics/dashboard` - Dashboard consolidado de métricas

#### Database:
- **Tabela `campaign_analytics`**: Métricas calculadas em tempo real. Campos: id, campaign_id, total_messages, sent_count, delivered_count, failed_count, read_count, response_count, avg_delivery_time, calculated_at.

- **Tabela `campaign_performance_snapshots`**: **Snapshots diários de performance** - Esta tabela armazena um "retrato" das métricas de cada campanha ao final de cada dia. Permite análise histórica e comparação de performance ao longo do tempo. O campo `metrics_json` contém um JSON com todas as métricas do dia (taxa de entrega, engajamento, etc.). Útil para gráficos de evolução temporal e relatórios históricos.

- **Tabela `message_engagement_events`**: **Eventos de engajamento** - Registra cada interação do usuário com as mensagens enviadas. `event_type` pode ser: 'delivered', 'read', 'replied', 'clicked_button', 'clicked_url'. `metadata_json` armazena dados específicos do evento (ex: qual botão foi clicado, URL acessada). Permite análise granular de como usuários interagem com as campanhas.

#### Domínios:
- **CampaignAnalytics**: Calcula e gerencia métricas de performance (taxa de entrega, engajamento, ROI)
- **PerformanceSnapshot**: Representa um snapshot temporal das métricas de uma campanha
- **MessageEngagementEvent**: Representa um evento específico de interação do usuário

#### Usecases:
- **Analytics/CalculateCampaignMetrics**: Calcular métricas em tempo real baseado no status das mensagens
- **Analytics/GeneratePerformanceReport**: Relatórios detalhados com gráficos e comparações
- **Analytics/CompareCampaignPerformance**: Análise comparativa entre múltiplas campanhas
- **Analytics/ExportReport**: Exportação em PDF/Excel com gráficos e tabelas
- **Analytics/CreateDailySnapshot**: Job diário que cria snapshot das métricas
- **Analytics/TrackEngagementEvent**: Registrar eventos de engajamento via webhook

## Previsão de PR

Esta seção mapeia todos os arquivos que devem ser criados ou modificados para implementar completamente esta Epic, organizados por diretório seguindo os padrões do sistema.

### Enums
```
app/Enums/CampaignStatus.php (novo)
```

### Models
```
app/Models/Category.php (novo)
app/Models/Tag.php (novo)
app/Models/CampaignCategoryAssignment.php (novo)
app/Models/CampaignTagAssignment.php (novo)
app/Models/MessageDeliveryAttempt.php (novo)
app/Models/CampaignStatusHistory.php (novo)
app/Models/WhatsAppSyncLog.php (novo)
app/Models/CampaignAnalytics.php (novo)
app/Models/CampaignPerformanceSnapshot.php (novo)
app/Models/MessageEngagementEvent.php (novo)
```

### Domains
```
app/Domains/ChatBot/Category.php (novo)
app/Domains/ChatBot/Tag.php (novo)
app/Domains/ChatBot/CampaignCategoryAssignment.php (novo)
app/Domains/ChatBot/CampaignTagAssignment.php (novo)
app/Domains/ChatBot/MessageDeliveryAttempt.php (novo)
app/Domains/ChatBot/CampaignStatusHistory.php (novo)
app/Domains/ChatBot/WhatsAppSyncLog.php (novo)
app/Domains/ChatBot/CampaignAnalytics.php (novo)
app/Domains/ChatBot/PerformanceSnapshot.php (novo)
app/Domains/ChatBot/MessageEngagementEvent.php (novo)
app/Domains/ChatBot/Campaign.php (modificado - adicionar métodos para status)
app/Domains/ChatBot/Message.php (modificado - adicionar métodos para retry)
```

### Factories
```
app/Factories/ChatBot/CategoryFactory.php (novo)
app/Factories/ChatBot/TagFactory.php (novo)
app/Factories/ChatBot/CampaignCategoryAssignmentFactory.php (novo)
app/Factories/ChatBot/CampaignTagAssignmentFactory.php (novo)
app/Factories/ChatBot/MessageDeliveryAttemptFactory.php (novo)
app/Factories/ChatBot/CampaignStatusHistoryFactory.php (novo)
app/Factories/ChatBot/WhatsAppSyncLogFactory.php (novo)
app/Factories/ChatBot/CampaignAnalyticsFactory.php (novo)
app/Factories/ChatBot/PerformanceSnapshotFactory.php (novo)
app/Factories/ChatBot/MessageEngagementEventFactory.php (novo)
app/Factories/ChatBot/CampaignFactory.php (modificado)
app/Factories/ChatBot/MessageFactory.php (modificado)
```

### Repositories
```
app/Repositories/CategoryRepository.php (novo)
app/Repositories/TagRepository.php (novo)
app/Repositories/CampaignCategoryAssignmentRepository.php (novo)
app/Repositories/CampaignTagAssignmentRepository.php (novo)
app/Repositories/MessageDeliveryAttemptRepository.php (novo)
app/Repositories/CampaignStatusHistoryRepository.php (novo)
app/Repositories/WhatsAppSyncLogRepository.php (novo)
app/Repositories/CampaignAnalyticsRepository.php (novo)
app/Repositories/PerformanceSnapshotRepository.php (novo)
app/Repositories/MessageEngagementEventRepository.php (novo)
app/Repositories/CampaignRepository.php (modificado)
app/Repositories/MessageRepository.php (modificado)
```

### Use Cases
```
app/UseCases/ChatBot/Category/Store.php (novo)
app/UseCases/ChatBot/Category/Update.php (novo)
app/UseCases/ChatBot/Category/Delete.php (novo)
app/UseCases/ChatBot/Category/GetAll.php (novo)
app/UseCases/ChatBot/Campaign/AssignCategories.php (novo)
app/UseCases/ChatBot/Campaign/AssignTags.php (novo)
app/UseCases/ChatBot/Campaign/Cancel.php (novo)
app/UseCases/ChatBot/Campaign/UpdateStatusFromMessages.php (novo)
app/UseCases/ChatBot/Campaign/GetStatusHistory.php (novo)
app/UseCases/ChatBot/Message/GetByCampaign.php (novo)
app/UseCases/ChatBot/Message/ResendFailed.php (novo)
app/UseCases/ChatBot/Message/Resend.php (novo)
app/UseCases/ChatBot/Message/SendIndividual.php (novo)
app/UseCases/ChatBot/Message/BulkResend.php (novo)
app/UseCases/ChatBot/Message/GetDeliveryStatus.php (novo)
app/UseCases/ChatBot/WhatsApp/SyncMessageStatus.php (novo)
app/UseCases/ChatBot/WhatsApp/SyncCampaignStatus.php (novo)
app/UseCases/ChatBot/WhatsApp/ProcessImprovedWebhook.php (novo)
app/UseCases/ChatBot/WhatsApp/ReconcileMessageStatuses.php (novo)
app/UseCases/ChatBot/WhatsApp/GenerateDeliveryReport.php (novo)
app/UseCases/ChatBot/Analytics/CalculateCampaignMetrics.php (novo)
app/UseCases/ChatBot/Analytics/GeneratePerformanceReport.php (novo)
app/UseCases/ChatBot/Analytics/CompareCampaignPerformance.php (novo)
app/UseCases/ChatBot/Analytics/ExportReport.php (novo)
app/UseCases/ChatBot/Analytics/CreateDailySnapshot.php (novo)
app/UseCases/ChatBot/Analytics/TrackEngagementEvent.php (novo)
```

### Controllers
```
app/Http/Controllers/ChatBot/CategoryController.php (novo)
app/Http/Controllers/ChatBot/TagController.php (novo)
app/Http/Controllers/ChatBot/CampaignAnalyticsController.php (novo)
app/Http/Controllers/ChatBot/WhatsAppSyncController.php (novo)
app/Http/Controllers/ChatBot/CampaignController.php (modificado)
app/Http/Controllers/ChatBot/MessageController.php (modificado)
```

### Requests
```
app/Http/Requests/Category/StoreRequest.php (novo)
app/Http/Requests/Category/UpdateRequest.php (novo)
app/Http/Requests/Campaign/AssignCategoriesRequest.php (novo)
app/Http/Requests/Campaign/AssignTagsRequest.php (novo)
app/Http/Requests/Message/ResendRequest.php (novo)
app/Http/Requests/Message/SendIndividualRequest.php (novo)
app/Http/Requests/Analytics/ExportRequest.php (novo)
```

### Jobs
```
app/Jobs/SyncCampaignStatusWithBooleans.php (novo)
app/Jobs/SyncPendingMessagesStatus.php (novo)
app/Jobs/UpdateCampaignStatusFromMessages.php (novo)
app/Jobs/CleanupOldSyncLogs.php (novo)
app/Jobs/CreateDailyCampaignSnapshots.php (novo)
```

### Migrations
```
database/migrations/xxxx_create_categories_table.php (novo)
database/migrations/xxxx_create_tags_table.php (novo)
database/migrations/xxxx_create_campaigns_categories_table.php (novo)
database/migrations/xxxx_create_campaign_tag_assignments_table.php (novo)
database/migrations/xxxx_create_message_delivery_attempts_table.php (novo)
database/migrations/xxxx_create_campaign_status_history_table.php (novo)
database/migrations/xxxx_create_whatsapp_sync_logs_table.php (novo)
database/migrations/xxxx_create_campaign_analytics_table.php (novo)
database/migrations/xxxx_create_campaign_performance_snapshots_table.php (novo)
database/migrations/xxxx_create_message_engagement_events_table.php (novo)
database/migrations/xxxx_add_status_fields_to_campaigns_table.php (novo)
database/migrations/xxxx_add_delivery_fields_to_messages_table.php (novo)
database/migrations/xxxx_add_sync_fields_to_whatsapp_messages_table.php (novo)
```

### Routes
```
routes/api.php (modificado - adicionar novas rotas)
```

### Console
```
app/Console/Kernel.php (modificado - adicionar novos jobs ao schedule)
```

### Filters
```
app/Domains/Filters/CategoryFilters.php (novo)
app/Domains/Filters/TagFilters.php (novo)
app/Domains/Filters/CampaignFilters.php (modificado)
app/Domains/Filters/MessageFilters.php (modificado)
```

**Total Estimado: ~85 arquivos (45 novos + 40 modificados)**

## Melhorias Específicas Identificadas

### Problema 1: Sistema de Status Limitado
**Situação Atual**: O sistema usa campos booleanos (`is_sent`, `is_sending`, `is_scheduled`) que não cobrem todos os estados possíveis de uma campanha.
**Solução**: Implementar `CampaignStatus` enum com estados granulares e histórico de mudanças.

### Problema 2: Falta de Reenvio Inteligente
**Situação Atual**: Não existe funcionalidade para reenviar apenas mensagens falhadas de uma campanha.
**Solução**: Endpoints específicos para reenvio com políticas de retry e backoff exponencial.

### Problema 3: Monitoramento Passivo
**Situação Atual**: Sistema depende apenas de webhooks para atualização de status.
**Solução**: Jobs ativos que consultam API do WhatsApp para sincronizar status de mensagens falhadas.

### Problema 4: Falta de Organização
**Situação Atual**: Campanhas não possuem categorização ou tags para organização.
**Solução**: Sistema completo de categorias e tags com filtros avançados.

### Problema 5: Analytics Limitadas
**Situação Atual**: Não existe sistema de métricas e relatórios detalhados.
**Solução**: Dashboard completo com métricas de entrega, engajamento e ROI.

## Plano de Testes

### Testes Unitários:
- Validação de transições de status de campanha
- Políticas de retry para mensagens falhadas
- Cálculos de métricas e analytics
- Validação de categorias e tags

### Testes de Integração:
- Sincronização com WhatsApp Business API
- Processamento de webhooks de status
- Jobs de sincronização automática
- Exportação de relatórios

### Testes de Performance:
- Envio de campanhas com 10k+ mensagens
- Processamento de webhooks em massa
- Consultas de analytics com grandes volumes
- Jobs de sincronização com timeout

### Testes de Regressão:
- Compatibilidade com sistema atual de campanhas
- Migração de dados existentes
- Funcionalidades existentes não afetadas

## Conclusão

Esta Epic transformará o sistema atual de campanhas WhatsApp em uma plataforma robusta e profissional, adequada para empresas de todos os tamanhos. As melhorias propostas abordam limitações críticas identificadas no sistema atual e introduzem funcionalidades essenciais para um sistema de marketing digital moderno.

### Benefícios Esperados:
- **Organização**: Sistema de categorias e tags para melhor gestão
- **Confiabilidade**: Monitoramento ativo e reenvio inteligente
- **Visibilidade**: Analytics detalhadas e relatórios profissionais
- **Controle**: Gestão granular de status e operações
- **Escalabilidade**: Arquitetura preparada para alto volume

### Impacto no Negócio:
- Redução de mensagens perdidas através de reenvio automático
- Melhor ROI através de analytics detalhadas
- Maior satisfação do cliente com entregas confiáveis
- Eficiência operacional através de melhor organização

## Referências

- WhatsApp Business API Documentation
- Meta for Developers - Webhook Guide
- Laravel Queue Documentation
- Campaign Management Best Practices
- Marketing Analytics Standards
