# Epic: ChatBot Core Improvements

## 📋 Overview

Esta Epic foca em **completar e melhorar** as funcionalidades básicas do sistema ChatBot que já estão parcialmente implementadas. O objetivo é tornar o sistema mais robusto, confiável e funcional, preparando-o para as funcionalidades avançadas das próximas Epics.

**Pré-requisito:** Esta Epic assume que o [Sistema ChatBot base](./chatbot-automatico.md) já está implementado.

## 🔍 Funcionalidades Atuais Identificadas

| Status | Funcionalidade |
|--------|----------------|
| ✅ | Processamento básico de webhook WhatsApp |
| ✅ | Domains principais (Flow, Step, Component, etc.) |
| ✅ | Navegação condicional básica via botões |
| ✅ | Coleta de input do usuário |
| ✅ | Substituição de variáveis `{{client.name}}` |
| ⚠️ | Command Steps (estrutura existe, lógica incompleta) |
| ⚠️ | Validação de input (básica, precisa melhorias) |
| ⚠️ | Error handling (tratamento limitado) |
| ⚠️ | Conditional Navigation Service (básico) |
| ❌ | Retry mechanism para falhas |
| ❌ | Timeout handling para conversas |
| ❌ | Validação robusta de fluxos |

## 🚀 Melhorias Propostas

### 1. Completar Command Steps
Implementar a lógica de execução para steps com `is_command=true`, permitindo execução de ações de negócio como atualização de dados do cliente, criação de leads, etc.

### 2. Sistema robusto de validação de input
Expandir a validação de input coletado em steps `is_input=true` com tipos de dados, formatos, e regras de negócio.

### 3. Error handling e recovery
Implementar tratamento de erros robusto com fallbacks, retry automático e mensagens de erro amigáveis para o usuário.

### 4. Timeout e cleanup de conversas
Sistema para detectar conversas inativas e fazer cleanup automático, evitando acúmulo de dados desnecessários.

### 5. Validação de integridade de fluxos
Validar fluxos antes da execução para detectar problemas como steps órfãos, loops infinitos, etc.

## 📅 Resumo do Plano de Implementação

- **Fase 1:** Implementar Command Steps com ações de negócio
- **Fase 2:** Expandir sistema de validação de input
- **Fase 3:** Melhorar error handling e recovery
- **Fase 4:** Implementar timeout e cleanup de conversas
- **Fase 5:** Adicionar validação de integridade de fluxos

## 🔧 Plano de Implementação Detalhado

### 1. Command Steps Implementation

**Novos UseCases:**
- `app/Services/Meta/WhatsApp/ChatBot/UseCases/Commands/ExecuteCommand.php`
- `app/Services/Meta/WhatsApp/ChatBot/UseCases/Commands/UpdateClientData.php`
- `app/Services/Meta/WhatsApp/ChatBot/UseCases/Commands/CreateLead.php`
- `app/Services/Meta/WhatsApp/ChatBot/UseCases/Commands/SendNotification.php`

**Command Types:**
```php
// Tipos de comandos suportados
enum CommandType: string 
{
    case UPDATE_CLIENT = 'update_client';
    case CREATE_LEAD = 'create_lead';
    case SEND_EMAIL = 'send_email';
    case SCHEDULE_CALLBACK = 'schedule_callback';
    case UPDATE_CONVERSATION_DATA = 'update_conversation_data';
}
```

**Estrutura de Command Step:**
```json
{
    "step": "update_client_name",
    "type": "command",
    "is_command": true,
    "command_type": "update_client",
    "command_config": {
        "field": "name",
        "source": "last_interaction_input",
        "validation": "required|string|min:2"
    },
    "success_message": "Nome atualizado com sucesso!",
    "error_message": "Erro ao atualizar nome. Tente novamente.",
    "next_step": "ask_email"
}
```

### 2. Enhanced Input Validation

**Input Validation Service:**
- `app/Services/Meta/WhatsApp/ChatBot/Services/InputValidationService.php`

**Validation Types:**
```php
enum InputType: string 
{
    case TEXT = 'text';
    case EMAIL = 'email';
    case PHONE = 'phone';
    case NUMBER = 'number';
    case DATE = 'date';
    case CPF = 'cpf';
    case CNPJ = 'cnpj';
}
```

**Enhanced Step Configuration:**
```json
{
    "step": "collect_email",
    "type": "input",
    "is_input": true,
    "input_config": {
        "type": "email",
        "validation": "required|email",
        "error_message": "Por favor, digite um email válido.",
        "retry_limit": 3,
        "field_name": "email"
    },
    "message": "Por favor, digite seu email:",
    "next_step": "confirm_data"
}
```

### 3. Error Handling and Recovery

**Error Handler Service:**
- `app/Services/Meta/WhatsApp/ChatBot/Services/ErrorHandlerService.php`

**Error Types:**
```php
enum ChatBotErrorType: string 
{
    case VALIDATION_ERROR = 'validation_error';
    case COMMAND_EXECUTION_ERROR = 'command_execution_error';
    case FLOW_ERROR = 'flow_error';
    case EXTERNAL_API_ERROR = 'external_api_error';
    case TIMEOUT_ERROR = 'timeout_error';
}
```

**Error Recovery Strategies:**
- **Retry:** Tentar novamente automaticamente
- **Fallback:** Ir para step alternativo
- **Human Escalation:** Transferir para atendente
- **Reset:** Reiniciar conversa do início

### 4. Conversation Timeout and Cleanup

**Timeout Service:**
- `app/Services/Meta/WhatsApp/ChatBot/Services/ConversationTimeoutService.php`

**Command para Cleanup:**
- `app/Console/Commands/ChatBot/CleanupInactiveConversations.php`

**Timeout Configuration:**
```php
// Em config/chatbot.php
'timeouts' => [
    'input_step' => 300,      // 5 minutos para input
    'interactive_step' => 600, // 10 minutos para seleção
    'conversation' => 3600,    // 1 hora para conversa completa
],
'cleanup' => [
    'inactive_after' => 86400, // 24 horas
    'run_cleanup_every' => 3600, // A cada hora
]
```

### 5. Flow Integrity Validation

**Flow Validator Service:**
- `app/Services/Meta/WhatsApp/ChatBot/Services/FlowValidatorService.php`

**Validation Rules:**
- Todos os steps têm next_step válido (exceto ending steps)
- Não há loops infinitos
- Steps condicionais têm targets válidos
- Initial step existe e é único
- Ending step existe

**Validation UseCase:**
- `app/Services/Meta/WhatsApp/ChatBot/UseCases/ValidateFlow.php`

## 📦 Previsão de Arquivos do PR

### Services
```
app/Services/Meta/WhatsApp/ChatBot/Services/InputValidationService.php (novo)
app/Services/Meta/WhatsApp/ChatBot/Services/ErrorHandlerService.php (novo)
app/Services/Meta/WhatsApp/ChatBot/Services/ConversationTimeoutService.php (novo)
app/Services/Meta/WhatsApp/ChatBot/Services/FlowValidatorService.php (novo)
```

### UseCases
```
app/Services/Meta/WhatsApp/ChatBot/UseCases/Commands/ExecuteCommand.php (novo)
app/Services/Meta/WhatsApp/ChatBot/UseCases/Commands/UpdateClientData.php (novo)
app/Services/Meta/WhatsApp/ChatBot/UseCases/Commands/CreateLead.php (novo)
app/Services/Meta/WhatsApp/ChatBot/UseCases/Commands/SendNotification.php (novo)
app/Services/Meta/WhatsApp/ChatBot/UseCases/ValidateFlow.php (novo)
app/Services/Meta/WhatsApp/ChatBot/UseCases/ProcessFlowStep.php (modificado)
```

### Enums
```
app/Enums/ChatBot/CommandType.php (novo)
app/Enums/ChatBot/InputType.php (novo)
app/Enums/ChatBot/ChatBotErrorType.php (novo)
```

### Commands
```
app/Console/Commands/ChatBot/CleanupInactiveConversations.php (novo)
app/Console/Commands/ChatBot/ValidateFlows.php (novo)
```

### Configuration
```
config/chatbot.php (novo)
```

### Migrations
```
database/migrations/xxxx_add_timeout_fields_to_conversations_table.php (novo)
database/migrations/xxxx_add_validation_fields_to_flows_table.php (novo)
```

### Tests
```
tests/Feature/Services/Meta/WhatsApp/ChatBot/CommandStepsTest.php (novo)
tests/Feature/Services/Meta/WhatsApp/ChatBot/InputValidationTest.php (novo)
tests/Feature/Services/Meta/WhatsApp/ChatBot/ErrorHandlingTest.php (novo)
tests/Feature/Services/Meta/WhatsApp/ChatBot/ConversationTimeoutTest.php (novo)
tests/Unit/Services/Meta/WhatsApp/ChatBot/FlowValidatorTest.php (novo)
```

**Total estimado:** ~25 arquivos (23 novos + 2 modificados)

## 🔍 Melhorias Específicas Identificadas

### Problema 1: Command Steps não executam ações

| Aspecto | Descrição |
|---------|-----------|
| **Situação Atual** | Steps com `is_command=true` apenas retornam placeholder |
| **Impacto** | Impossível executar lógica de negócio no fluxo |
| **Solução** | Sistema de comandos tipados com ExecuteCommand UseCase |

### Problema 2: Validação de input limitada

| Aspecto | Descrição |
|---------|-----------|
| **Situação Atual** | Input coletado sem validação robusta |
| **Impacto** | Dados inválidos podem quebrar fluxos |
| **Solução** | InputValidationService com tipos e regras configuráveis |

### Problema 3: Error handling básico

| Aspecto | Descrição |
|---------|-----------|
| **Situação Atual** | Erros podem interromper conversas sem recovery |
| **Impacto** | Experiência ruim do usuário em caso de falhas |
| **Solução** | ErrorHandlerService com estratégias de recovery |

## 🧪 Plano de Testes

### Testes Unitários
- ✅ Execução de diferentes tipos de comandos
- ✅ Validação de input com diferentes tipos de dados
- ✅ Estratégias de error recovery
- ✅ Detecção de timeout de conversas
- ✅ Validação de integridade de fluxos

### Testes de Integração
- ✅ Fluxo completo com command steps
- ✅ Validação de input em cenários reais
- ✅ Recovery de erros em webhook processing
- ✅ Cleanup automático de conversas inativas

### Testes de Regressão
- ✅ Funcionalidades existentes não afetadas
- ✅ Webhook processing mantém compatibilidade
- ✅ Navegação condicional continua funcionando
- ✅ Substituição de variáveis preservada

## 🎯 Conclusão

Esta Epic completa as funcionalidades básicas do ChatBot, tornando-o mais robusto e confiável. As melhorias implementadas criam uma base sólida para as funcionalidades avançadas das próximas Epics, especialmente o sistema de escalação humana e analytics.

## 📈 Benefícios Esperados

### Técnicos
- ✅ Command Steps funcionais para lógica de negócio
- ✅ Validação robusta de input do usuário
- ✅ Error handling com recovery automático
- ✅ Cleanup automático de recursos
- ✅ Validação preventiva de fluxos

### De Negócio
- 🤖 Automação completa de processos de negócio
- 📊 Coleta confiável de dados dos clientes
- 🔄 Redução de falhas e interrupções
- ⚡ Melhor performance do sistema

### De Usuário
- 💬 Conversas mais fluidas e confiáveis
- 🔧 Mensagens de erro claras e úteis
- ⏱️ Respostas mais rápidas do sistema
- 🎯 Experiência consistente

## 💼 Impacto no Negócio

- 🏢 Automação completa de atendimento inicial
- 📈 Melhora na qualificação de leads
- 🤖 Redução de carga de trabalho manual
- 🔄 Base sólida para funcionalidades avançadas

## 📚 Referências

- [ChatBot Sistema Base](./chatbot-automatico.md)
- [ProcessFlowStep UseCase](../../app/Services/Meta/WhatsApp/ChatBot/UseCases/ProcessFlowStep.php)
- [Conditional Navigation Guide](../../services/Meta/CONDITIONAL_NAVIGATION_GUIDE.md)
- [Laravel Validation](https://laravel.com/docs/validation)

---

**Próximos Passos:** Após implementação, seguir para [Epic 2: Sistema de Escalação Humana](./chatbot-human-escalation.md).
