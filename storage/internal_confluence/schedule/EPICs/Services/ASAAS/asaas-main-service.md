# Epic: Integração com ASAAS - Camada de Serviço Base

## 📋 Overview

A integração com a plataforma ASAAS é essencial para permitir que nosso sistema se conecte de forma segura e organizada com os serviços de cobrança, assinatura, carteira digital e subcontas oferecidos pela API ASAAS. Esta Epic trata da criação da camada de serviço base, que atuará como uma abstração padronizada para todas as chamadas feitas à API externa. Essa camada será utilizada pelos módulos de Organizações e Vendas/Clientes em etapas futuras.

## 🔍 Funcionalidades Atuais Identificadas

| Status | Funcionalidade |
|--------|----------------|
| ❌ | Integração com API ASAAS |
| ❌ | Camada de serviço centralizada para consumo de APIs externas |
| ⚠️ | Tratamento de erros genérico com Guzzle em alguns serviços |
| ⚠️ | Logs de requisição ainda não padronizados |

## 🚀 Melhorias Propostas

### 1. Cliente HTTP padronizado com token dinâmico
Implementar um cliente que permita trocar dinamicamente o token de autenticação conforme a subconta da organização, permitindo multitenância na integração com o ASAAS.

### 2. Camada de serviço ASAAS centralizada
Criação de um serviço `AsaasService` com métodos `get`, `post`, `put` e `delete` genéricos que encapsulam detalhes da comunicação HTTP com autenticação, logs e tratamento de exceções.

### 3. Tratamento e logging de erros padronizado
Padronização da captura de erros com base nos códigos e mensagens da API ASAAS, salvando logs relevantes para auditoria usando o padrão `DBLog` existente.

### 4. Suporte a modo sandbox/produção
Permitir troca de ambiente via configuração `.env` sem alterações no código.

## 📅 Resumo do Plano de Implementação

- **Fase 1:** Criação do cliente HTTP e suporte a token dinâmico
- **Fase 2:** Implementação da classe AsaasService com métodos genéricos
- **Fase 3:** Padronização de tratamento de erros e logging
- **Fase 4:** Adição de suporte a ambientes sandbox e produção
- **Fase 5:** Testes unitários e mocks para integração ASAAS

## 🔧 Plano de Implementação Detalhado

### 1. Criação do cliente HTTP e suporte a token dinâmico

**Domínios:**
- `app/Services/ASAAS/Domains/AsaasHttpClient.php` - Responsável por enviar requisições com token e base URL definidos dinamicamente

**UseCases:**
- `app/Services/ASAAS/UseCases/Http/CreateHttpClient.php` - Cria o client com headers baseados na organização logada ou contexto da requisição

### 2. Classe AsaasService com métodos genéricos

**Services:**
- `app/Services/ASAAS/AsaasService.php` - Expõe métodos `get`, `post`, `put`, `delete` seguindo o padrão do `WhatsAppService`

**UseCases:**
- `app/Services/ASAAS/UseCases/CallEndpoint.php` - UseCase que faz chamadas genéricas recebendo path, método e payload

### 3. Padronização de tratamento de erros e logging

**Exceptions:**
- `app/Services/ASAAS/Exceptions/AsaasException.php` - Exceção personalizada para erros ASAAS

**Models:**
- `app/Services/ASAAS/Models/AsaasLog.php` - Modelo para armazenar logs relevantes (seguindo padrão existente)

**Commands:**
- `app/Console/Commands/ASAAS/ProcessAsaasLogs.php` - Processa logs de erro em segundo plano (seguindo padrão de commands existentes)

### 4. Suporte a ambientes sandbox/produção

**Configuração:**
- Variáveis `.env`: `ASAAS_ENV`, `ASAAS_TOKEN_SANDBOX`, `ASAAS_TOKEN_PROD`, `ASAAS_URL_SANDBOX`, `ASAAS_URL_PROD`
- `config/asaas.php` - Arquivo de configuração centralizado

**Enums:**
- `app/Enums/AsaasEnvironment.php` - Enum para ambientes (sandbox/production)

### 5. Testes unitários e mocks para integração ASAAS

**Testes Unitários:**
- Testes para `AsaasService` com `GuzzleMockHandler`
- Testes de erros e logs seguindo padrão existente em `tests/Feature/Services/`

## 📦 Previsão de Arquivos do PR

### Enums
```
app/Enums/AsaasEnvironment.php (novo)
```

### Services
```
app/Services/ASAAS/AsaasService.php (novo)
app/Services/ASAAS/Domains/AsaasHttpClient.php (novo)
app/Services/ASAAS/Models/AsaasLog.php (novo)
app/Services/ASAAS/Exceptions/AsaasException.php (novo)
```

### UseCases
```
app/Services/ASAAS/UseCases/CallEndpoint.php (novo)
app/Services/ASAAS/UseCases/Http/CreateHttpClient.php (novo)
```

### Commands
```
app/Console/Commands/ASAAS/ProcessAsaasLogs.php (novo)
```

### Configuration
```
config/asaas.php (novo)
```

### Migrations
```
database/migrations/xxxx_create_asaas_logs_table.php (novo)
```

### Tests
```
tests/Feature/Services/ASAAS/AsaasServiceTest.php (novo)
tests/Feature/Services/ASAAS/AsaasHttpClientTest.php (novo)
```

### Console (modificado)
```
app/Console/Kernel.php (modificado - adicionar command agendado se aplicável)
```

**Total Estimado:** ~12 arquivos (11 novos + 1 modificado)

## 🔍 Melhorias Específicas Identificadas

### Problema 1: Ausência de cliente HTTP padronizado para APIs externas

| Aspecto | Descrição |
|---------|-----------|
| **Situação Atual** | Chamadas externas com Guzzle feitas ad-hoc e sem logs |
| **Impacto** | Dificuldade de rastrear problemas e falta de reutilização |
| **Solução** | Cliente `AsaasHttpClient` com logging via `DBLog` e exceções padronizadas |

### Problema 2: Falta de multitenância no acesso à API ASAAS

| Aspecto | Descrição |
|---------|-----------|
| **Situação Atual** | Não é possível trocar token dinamicamente |
| **Impacto** | Não é possível atender múltiplas contas ASAAS |
| **Solução** | Injetar token dinamicamente por organização (seguindo padrão do `WhatsAppService`) |

### Problema 3: Ambiente de testes não isolado

| Aspecto | Descrição |
|---------|-----------|
| **Situação Atual** | Difícil alternar entre sandbox e produção |
| **Impacto** | Risco de testes afetarem dados reais |
| **Solução** | Variáveis de ambiente e enum `AsaasEnvironment` para diferenciar |

## 🧪 Plano de Testes

### Testes Unitários
- ✅ Chamadas `get`, `post`, `put`, `delete` com mock
- ✅ Token dinâmico e troca de ambiente
- ✅ Validação de exceções ASAAS
- ✅ Logging via `DBLog` em cenários de erro

### Testes de Integração
- ✅ Integração com endpoint real em ambiente sandbox (opcional)
- ✅ Verificação de logs e falhas simuladas
- ✅ Teste de multitenância com diferentes organizações

### Testes de Regressão
- ✅ Verifica se demais módulos continuam operando sem impacto
- ✅ Compatibilidade com padrões existentes de serviços

## 🎯 Conclusão

Esta Epic estabelece a base para toda a integração futura com o ASAAS, garantindo padronização, segurança e rastreabilidade em todas as chamadas HTTP. A implementação modular e reutilizável reduz a repetição de código e mitiga erros comuns de integrações API, seguindo os padrões já estabelecidos no sistema (como `WhatsAppService`, `DBLog`, etc.).

## 📈 Benefícios Esperados

### Técnicos
- ✅ Redução de código duplicado
- ✅ Reutilização de serviços seguindo padrões existentes
- ✅ Logs padronizados via `DBLog`
- ✅ Tratamento de exceções consistente
- ✅ Suporte a multitenância

### De Negócio
- 💰 Permite integração com ASAAS para cobranças e assinaturas
- ⚡ Reduz tempo de desenvolvimento de futuras integrações
- 🔍 Permite rastrear e auditar falhas externas de pagamento

### De Usuário
- 🚀 Facilita futuras features sem afetar experiência existente
- 🔒 Maior segurança nas transações financeiras

## 💼 Impacto no Negócio

- 🏢 Habilita monetização via ASAAS para organizações
- ⏱️ Reduz tempo de desenvolvimento de futuras integrações
- 📊 Permite rastrear e auditar falhas externas de pagamento
- 🔄 Base sólida para expansão do ecossistema de pagamentos

## 📚 Referências

- [ASAAS API Documentation](https://docs.asaas.com/api)
- [Guzzle HTTP Client](https://docs.guzzlephp.org)
- [Laravel Commands](https://laravel.com/docs/artisan)
- [Epic: Integração com Subcontas ASAAS](./asaas-organizations-service.md) *(a ser criado)*
- [Epic: Integração com Vendas e Clientes ASAAS](./asaas-sales-service.md) *(a ser criado)*

---

**Próximos Passos:** Após a implementação desta Epic, será possível desenvolver as integrações específicas para Organizações e Vendas/Clientes, utilizando a base sólida aqui estabelecida.

