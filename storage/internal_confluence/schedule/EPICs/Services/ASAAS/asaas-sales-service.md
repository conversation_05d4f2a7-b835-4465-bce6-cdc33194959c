# Epic: Integração com ASAAS - Vendas e Clientes das Organizações

## 📋 Overview

Esta Epic trata da integração entre os dados de Vendas e Clientes do nosso sistema com a API ASAAS. Cada Cliente pertence a uma Organização, e pode realizar compras avulsas ou contratar serviços recorrentes (assinaturas). O objetivo é permitir que todo o fluxo financeiro de cada Cliente seja registrado no ASAAS, utilizando a subconta da Organização correspondente.

**Pré-requisitos:** Esta Epic assume que:
- [Epic: Camada de Serviço ASAAS Base](./asaas-main-service.md) está implementada
- [Epic: Integração ASAAS - Organizações e Subcontas](./asaas-organizations-service.md) está finalizada

## 🔍 Funcionalidades Atuais Identificadas

| Status | Funcionalidade |
|--------|----------------|
| ❌ | Cadastro de cliente ASAAS por organização |
| ❌ | Integração de vendas (boletos/cartões) via ASAAS |
| ❌ | Integração com assinatura ASAAS para clientes |
| ⚠️ | Sincronização de status de pagamento (manual/parcial) |
| ❌ | Visualização de faturas e cobranças no painel |
| ✅ | Domínio Client com organização vinculada |
| ✅ | Domínio Sale e Item com valores e produtos definidos |
| ✅ | UseCases de Client e Sale com validação de organização |
| ✅ | Controllers de Client e Sale funcionais |

## 🚀 Melhorias Propostas

### 1. Cadastro de cliente ASAAS
Sempre que um cliente for criado no sistema, criar um cliente correspondente no ASAAS via endpoint `/customers`, utilizando o `AsaasService` com token da subconta da organização. Armazenar o `asaas_customer_id` no registro.

### 2. Integração com vendas avulsas (boleto/cartão)
Gerar uma cobrança ASAAS para cada `Sale` registrada, via endpoint `/payments`, com base nos itens e valores totais. Armazenar o `asaas_payment_id` no registro.

### 3. Assinaturas recorrentes via ASAAS
Permitir que certos produtos possam ser recorrentes. Ao identificar esse caso, criar uma assinatura no ASAAS via endpoint `/subscriptions`. Armazenar `asaas_subscription_id`, `status`, próxima cobrança, etc.

### 4. Sincronização de status de pagamento
Command periódico que consulta status de pagamentos e atualiza os registros locais (`Sale`, `Subscription`, `Client`) com os dados mais recentes, seguindo padrão de commands existentes.

### 5. Visualização de cobranças/assinaturas
Permitir que a organização visualize todos os pagamentos e assinaturas dos seus clientes via rota `/api/clients/billing`, com filtros por status, data e cliente.

## 📅 Resumo do Plano de Implementação

- **Fase 1:** Cadastro de cliente ASAAS na criação de `Client`
- **Fase 2:** Integração com vendas (`Sale`) e cobranças avulsas
- **Fase 3:** Integração com produtos recorrentes (assinaturas)
- **Fase 4:** Sincronização automática com status de pagamento
- **Fase 5:** Visualização das cobranças e relatório das receitas

## 🔧 Plano de Implementação Detalhado

### 1. Cadastro de Cliente ASAAS

**Migration:**
- Adicionar campo `asaas_customer_id` à tabela `clients`

**UseCases:**
- `app/Services/ASAAS/UseCases/Clients/CreateCustomer.php` - Cria cliente no ASAAS usando `AsaasService`
- Modificar `app/UseCases/Inventory/Client/Store.php` para disparar criação no ASAAS
- Modificar `app/UseCases/Inventory/Client/Update.php` para sincronizar dados

**Validações:**
- Verificar se já existe `asaas_customer_id` antes de criar
- Associar cliente ASAAS à subconta correta via token da organização
- Rollback do cliente local se falhar criação no ASAAS

### 2. Integração com Vendas (Avulsas)

**Migration:**
- Campo `asaas_payment_id` na tabela `sales`
- Campo `payment_status` na tabela `sales` (enum)

**UseCases:**
- `app/Services/ASAAS/UseCases/Sales/CreatePayment.php` - Cria cobrança no ASAAS
- Modificar `app/UseCases/Inventory/Sale/Store.php` para disparar cobrança

**Validações:**
- Cálculo do valor total via soma de `items`
- Forma de pagamento (boleto, pix, cartão)
- Cliente deve ter `asaas_customer_id` válido

### 3. Integração com Assinaturas Recorrentes

**Domains:**
- `app/Services/ASAAS/Domains/ClientSubscription.php` - Encapsula lógica de assinatura

**Models:**
- `app/Services/ASAAS/Models/AsaasSubscription.php` - Modelo para assinaturas

**UseCases:**
- `app/Services/ASAAS/UseCases/Subscriptions/CreateSubscription.php`
- `app/Services/ASAAS/UseCases/Subscriptions/CancelSubscription.php`

**Validações:**
- Produto deve ser marcado como recorrente
- Verifica duplicidade de assinatura

### 4. Sincronização de Status (Commands)

**Commands:**
- `app/Console/Commands/ASAAS/SyncPaymentsStatus.php` - Sincroniza status de pagamentos
- `app/Console/Commands/ASAAS/SyncSubscriptionsStatus.php` - Sincroniza assinaturas

**Agendamento:**
- Cron a cada hora via `Kernel.php` (seguindo padrão existente)

**UseCases:**
- `app/Services/ASAAS/UseCases/Sales/SyncPaymentStatus.php`
- `app/Services/ASAAS/UseCases/Subscriptions/SyncSubscriptionStatus.php`

### 5. Visualização de Cobranças e Assinaturas

**Rotas:**
- `GET /api/clients/billing` - Lista cobranças dos clientes da organização
- Integrar com `ClientController` existente

**UseCases:**
- `app/Services/ASAAS/UseCases/Billing/GetClientPayments.php`
- `app/Services/ASAAS/UseCases/Billing/GetClientSubscriptions.php`

## 📦 Previsão de Arquivos do PR

### Migrations
```
database/migrations/xxxx_add_asaas_customer_id_to_clients_table.php (novo)
database/migrations/xxxx_add_asaas_payment_fields_to_sales_table.php (novo)
database/migrations/xxxx_create_asaas_subscriptions_table.php (novo)
```

### Models (modificados)
```
app/Models/Client.php (modificado - adicionar asaas_customer_id)
app/Models/Sale.php (modificado - adicionar campos ASAAS)
app/Domains/Inventory/Client.php (modificado - adicionar campos ASAAS)
app/Domains/Inventory/Sale.php (modificado - adicionar campos ASAAS)
app/Factories/ClientFactory.php (modificado - suporte aos novos campos)
app/Factories/SaleFactory.php (modificado - suporte aos novos campos)
```

### Services
```
app/Services/ASAAS/Domains/ClientSubscription.php (novo)
app/Services/ASAAS/Models/AsaasSubscription.php (novo)
```

### UseCases
```
app/Services/ASAAS/UseCases/Clients/CreateCustomer.php (novo)
app/Services/ASAAS/UseCases/Sales/CreatePayment.php (novo)
app/Services/ASAAS/UseCases/Sales/SyncPaymentStatus.php (novo)
app/Services/ASAAS/UseCases/Subscriptions/CreateSubscription.php (novo)
app/Services/ASAAS/UseCases/Subscriptions/CancelSubscription.php (novo)
app/Services/ASAAS/UseCases/Subscriptions/SyncSubscriptionStatus.php (novo)
app/Services/ASAAS/UseCases/Billing/GetClientPayments.php (novo)
app/Services/ASAAS/UseCases/Billing/GetClientSubscriptions.php (novo)
app/UseCases/Inventory/Client/Store.php (modificado - integrar criação ASAAS)
app/UseCases/Inventory/Client/Update.php (modificado - sincronizar ASAAS)
app/UseCases/Inventory/Sale/Store.php (modificado - integrar cobrança ASAAS)
```

### Commands
```
app/Console/Commands/ASAAS/SyncPaymentsStatus.php (novo)
app/Console/Commands/ASAAS/SyncSubscriptionsStatus.php (novo)
```

### Controllers (modificados)
```
app/Http/Controllers/ClientController.php (modificado - adicionar rota billing)
```

### Routes (modificados)
```
routes/api.php (modificado - adicionar rota /clients/billing)
```

### Console (modificados)
```
app/Console/Kernel.php (modificado - agendamento dos commands)
```

### Enums
```
app/Enums/PaymentStatus.php (novo)
app/Enums/SubscriptionStatus.php (novo)
```

### Tests
```
tests/Feature/Services/ASAAS/Clients/CreateCustomerTest.php (novo)
tests/Feature/Services/ASAAS/Sales/CreatePaymentTest.php (novo)
tests/Feature/Services/ASAAS/Sales/SyncPaymentTest.php (novo)
tests/Feature/Services/ASAAS/Subscriptions/SubscriptionFlowTest.php (novo)
tests/Feature/Services/ASAAS/Billing/ClientBillingTest.php (novo)
tests/Feature/Services/ASAAS/Commands/SyncCommandsTest.php (novo)
```

**Total Estimado:** ~32 arquivos (24 novos + 8 modificados)

## 🔍 Melhorias Específicas Identificadas

### Problema 1: Clientes não são sincronizados com ASAAS

| Aspecto | Descrição |
|---------|-----------|
| **Situação Atual** | Clientes locais sem registro financeiro externo |
| **Impacto** | Impossível emitir cobranças ou faturas em nome do cliente |
| **Solução** | Criar cliente no ASAAS automaticamente na criação do registro local usando `AsaasService` |

### Problema 2: Vendas não geram cobranças automáticas

| Aspecto | Descrição |
|---------|-----------|
| **Situação Atual** | Sem relacionamento entre vendas e pagamentos |
| **Impacto** | Pagamentos manuais e sem rastreabilidade |
| **Solução** | Criar cobrança via `/payments` no ASAAS automaticamente ao registrar venda |

### Problema 3: Produtos recorrentes não geram assinaturas

| Aspecto | Descrição |
|---------|-----------|
| **Situação Atual** | Assinaturas controladas internamente |
| **Impacto** | Sem controle externo de recorrência, cobranças manuais |
| **Solução** | Integração via `/subscriptions` ASAAS para produtos marcados como recorrentes |

## 🧪 Plano de Testes

### Testes Unitários
- ✅ Criação de cliente ASAAS
- ✅ Geração de cobrança para venda
- ✅ Geração/cancelamento de assinatura
- ✅ Atualização de status via commands
- ✅ Integração com UseCases existentes de Client e Sale
- ✅ Validação de organização em todos os fluxos

### Testes de Integração
- ✅ Criação de cliente → venda → pagamento ASAAS
- ✅ Sincronização em ambiente sandbox
- ✅ Fluxo de assinatura e cancelamento
- ✅ Commands de sincronização com dados reais
- ✅ Integração com subcontas das organizações

### Testes de Regressão
- ✅ Cadastro de client continua funcionando sem ASAAS
- ✅ Vendas continuam salvas normalmente
- ✅ Compatibilidade com painel existente
- ✅ UseCases existentes não são afetados
- ✅ Controllers existentes mantêm funcionalidade

## 🎯 Conclusão

Com esta Epic, fechamos o ciclo de integração ASAAS ao permitir que as vendas e assinaturas feitas por clientes das Organizações sejam processadas, cobradas e auditadas de forma centralizada e segura. Os dados financeiros ficam centralizados na subconta da Organização, garantindo rastreabilidade e compatibilidade com modelos SaaS, mantendo total compatibilidade com a estrutura existente.

## 📈 Benefícios Esperados

### Técnicos
- ✅ Integração modular com ASAAS via `AsaasService`
- ✅ Sincronização de dados financeiros via commands agendados
- ✅ Validação de consistência de cobranças
- ✅ Logs padronizados via `DBLog` para auditoria
- ✅ Compatibilidade com estrutura existente de Client e Sale

### De Negócio
- 💰 Escalabilidade para múltiplos clientes por organização
- 📊 Redução de inadimplência por notificação automatizada
- ⏱️ Acompanhamento de receita em tempo real
- 🔄 Automação completa do fluxo de cobrança

### De Usuário
- 🔍 Transparência no status de cobranças
- 💳 Melhor experiência na gestão de pagamentos
- 📱 Facilidade em aderir a planos recorrentes
- 📊 Visibilidade completa do histórico financeiro

## 💼 Impacto no Negócio

- 🏢 Permite que as organizações usem o ASAAS para toda gestão financeira
- 📈 Melhora previsão de receita mensal
- 🤖 Reduz necessidade de suporte humano para cobranças
- 🔄 Base sólida para expansão do ecossistema de pagamentos

## 📚 Referências

- [ASAAS API - Customers](https://docs.asaas.com/reference/criar-cliente)
- [ASAAS API - Payments](https://docs.asaas.com/reference/criar-cobranca)
- [ASAAS API - Subscriptions](https://docs.asaas.com/reference/criar-assinatura)
- [ASAAS API - Faturas](https://docs.asaas.com/reference/listar-faturas)
- [Epic: Integração ASAAS - Organizações e Subcontas](./asaas-organizations-service.md)
- [Epic: Camada de Serviço ASAAS Base](./asaas-main-service.md)
- [Laravel Commands](https://laravel.com/docs/artisan)

---

**Conclusão da Trilha ASAAS:** Esta Epic completa a integração completa com ASAAS, permitindo que organizações gerenciem clientes, vendas e assinaturas de forma totalmente automatizada e integrada.
