# 🎯 **EPIC 2: AsaasOrganizationCustomer Entity**

## 📋 **RESUMO**

Implementação completa da entidade `AsaasOrganizationCustomer` para gerenciar organizações como customers da nossa conta principal no ASAAS. Esta entidade permitirá que cobremos organizações pelo uso da nossa plataforma, diferente do `AsaasClient` que representa clientes das organizações.

## 🎯 **OBJETIVOS**

### **Objetivo Principal**
Criar uma entidade completa para gerenciar organizações como customers no ASAAS, permitindo cobrança por uso da plataforma.

### **Objetivos Específicos**
1. ✅ Criação completa da entidade AsaasOrganizationCustomer (migration, model, domain, factory, repository)
2. ✅ Implementação de Use Cases principais para sincronização e criação
3. ✅ Integração com endpoints ASAAS usando nossa conta principal
4. ✅ Relacionamento com Organization (não necessariamente AsaasOrganization)

## 🏗️ **ARQUITETURA**

### **1. Diferenças Conceituais**

```
AsaasClient:
- Customer no ASAAS das subcontas (AsaasOrganization)
- Representa clientes finais das organizações
- Usa API key da organização específica
- Relacionamento: Client -> AsaasClient

AsaasOrganizationCustomer:
- Customer no ASAAS da nossa conta principal
- Representa organizações que usam nossa plataforma
- Usa nossa API key principal
- Relacionamento: Organization -> AsaasOrganizationCustomer
```

### **2. Estrutura de Arquivos**

```
database/migrations/
├── xxxx_create_asaas_organization_customers_table.php

app/Services/ASAAS/Models/
├── AsaasOrganizationCustomer.php

app/Services/ASAAS/Domains/
├── AsaasOrganizationCustomer.php

app/Services/ASAAS/Factories/
├── AsaasOrganizationCustomerFactory.php

app/Services/ASAAS/Repositories/
├── AsaasOrganizationCustomerRepository.php

app/Services/ASAAS/UseCases/OrganizationCustomers/
├── CreateOrganizationCustomer.php
├── SyncAsaasOrganizationCustomer.php
├── UpdateAsaasOrganizationCustomer.php
```

## 📊 **ESTRUTURA DE DADOS**

### **Migration: asaas_organization_customers**

```sql
CREATE TABLE asaas_organization_customers (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    
    -- Relacionamentos
    organization_id BIGINT UNSIGNED NOT NULL,
    asaas_customer_id VARCHAR(255) UNIQUE NOT NULL,
    
    -- Controle de Sincronização
    asaas_synced_at TIMESTAMP NULL,
    asaas_sync_errors JSON NULL,
    sync_status ENUM('pending', 'synced', 'error') DEFAULT 'pending',
    
    -- Dados do Customer (espelhados do ASAAS)
    name VARCHAR(255) NULL,
    email VARCHAR(255) NULL,
    phone VARCHAR(20) NULL,
    mobile_phone VARCHAR(20) NULL,
    address VARCHAR(255) NULL,
    address_number VARCHAR(10) NULL,
    complement VARCHAR(255) NULL,
    province VARCHAR(100) NULL,
    city_name VARCHAR(100) NULL,
    state VARCHAR(2) NULL,
    country VARCHAR(100) NULL,
    postal_code VARCHAR(10) NULL,
    cpf_cnpj VARCHAR(18) NULL,
    person_type ENUM('FISICA', 'JURIDICA') NULL,
    external_reference VARCHAR(255) NULL,
    notification_disabled BOOLEAN DEFAULT FALSE,
    additional_emails TEXT NULL,
    observations TEXT NULL,
    foreign_customer BOOLEAN DEFAULT FALSE,
    deleted BOOLEAN DEFAULT FALSE,
    
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL,
    deleted_at TIMESTAMP NULL,
    
    -- Foreign Keys
    FOREIGN KEY (organization_id) REFERENCES organizations(id) ON DELETE CASCADE,
    
    -- Indexes
    INDEX idx_organization_id (organization_id),
    INDEX idx_asaas_customer_id (asaas_customer_id),
    INDEX idx_sync_status (sync_status),
    INDEX idx_asaas_synced_at (asaas_synced_at),
    INDEX idx_external_reference (external_reference),
    INDEX idx_cpf_cnpj (cpf_cnpj),
    INDEX idx_email (email)
);
```

### **Campos Internos Específicos**
- `organization_id`: Relacionamento com Organization
- `asaas_customer_id`: ID do customer no ASAAS (nossa conta principal)

### **Campos da API ASAAS**
Todos os campos do exemplo fornecido:
```json
{
  "name": "John Doe",
  "cpfCnpj": "24971563792",
  "email": "<EMAIL>",
  "phone": "4738010919",
  "mobilePhone": "4799376637",
  "address": "Av. Paulista",
  "addressNumber": "150",
  "complement": "Sala 201",
  "province": "Centro",
  "postalCode": "01310-000",
  "externalReference": "12987382",
  "notificationDisabled": false,
  "additionalEmails": "<EMAIL>,<EMAIL>",
  "observations": "ótimo pagador, nenhum problema até o momento",
  "foreignCustomer": false
}
```

## 🔧 **IMPLEMENTAÇÃO**

### **1. Model**
```php
class AsaasOrganizationCustomer extends Model
{
    use HasFactory, SoftDeletes;
    
    protected $table = 'asaas_organization_customers';
    
    protected $fillable = [
        'organization_id',
        'asaas_customer_id',
        'asaas_synced_at',
        'asaas_sync_errors',
        'sync_status',
        // ... todos os campos da API ASAAS
    ];
    
    protected $casts = [
        'asaas_synced_at' => 'datetime',
        'asaas_sync_errors' => 'array',
        'notification_disabled' => 'boolean',
        'foreign_customer' => 'boolean',
        'deleted' => 'boolean',
    ];
    
    public function organization(): BelongsTo
    {
        return $this->belongsTo(Organization::class);
    }
    
    // Métodos similares ao AsaasClient
    public function hasAsaasIntegration(): bool
    public function needsSync(): bool
    public function hasErrors(): bool
    public function isPending(): bool
}
```

### **2. Domain**
```php
class AsaasOrganizationCustomer
{
    public function __construct(
        public readonly ?int $id,
        public readonly int $organization_id,
        public readonly string $asaas_customer_id,
        // ... todos os campos necessários
        public readonly ?Organization $organization = null,
    ) {}
    
    // Métodos de negócio
    public function hasAsaasIntegration(): bool
    public function needsSync(): bool
    public function hasErrors(): bool
    public function isPending(): bool
    
    // Métodos de transformação
    public function toAsaasPayload(): array
    public function toArray(): array
    public function toStoreArray(): array
    public function toUpdateArray(): array
}
```

### **3. Factory**
```php
class AsaasOrganizationCustomerFactory
{
    public function buildFromModel(?AsaasOrganizationCustomerModel $model): ?AsaasOrganizationCustomer
    public function buildFromModels(Collection $models): array
    public function buildFromAsaasResponse(array $response, int $organizationId): AsaasOrganizationCustomer
    public function buildFromOrganization(Organization $organization, ?string $asaasCustomerId = null): AsaasOrganizationCustomer
    public function buildFromStoreArray(array $data, ?Organization $organization = null): AsaasOrganizationCustomer
}
```

### **4. Repository**
```php
class AsaasOrganizationCustomerRepository
{
    // Métodos de busca
    public function findById(int $id): ?AsaasOrganizationCustomer
    public function findByOrganizationId(int $organizationId): ?AsaasOrganizationCustomer
    public function findByAsaasCustomerId(string $asaasCustomerId): ?AsaasOrganizationCustomer
    public function findByExternalReference(string $externalReference): ?AsaasOrganizationCustomer
    
    // Métodos de persistência
    public function save(AsaasOrganizationCustomer $customer): AsaasOrganizationCustomer
    public function create(AsaasOrganizationCustomer $customer): AsaasOrganizationCustomer
    public function update(AsaasOrganizationCustomer $customer): AsaasOrganizationCustomer
    public function delete(int $id): bool
    
    // Métodos de listagem e filtros
    public function findPendingSync(int $limit = 100): Collection
    public function findWithErrors(int $limit = 100): Collection
    public function findNeedingSync(int $limit = 100): Collection
}
```

## 🚀 **USE CASES PRINCIPAIS**

### **1. CreateOrganizationCustomer**
```php
class CreateOrganizationCustomer
{
    public function perform(Organization $organization, ?AsaasEnvironment $environment = null): AsaasOrganizationCustomer
    {
        // 1. Validar se organização já tem customer
        // 2. Preparar dados da organização
        // 3. Criar customer no ASAAS (usando nossa API key principal)
        // 4. Salvar AsaasOrganizationCustomer local
        // 5. Retornar domain
    }
}
```

### **2. SyncAsaasOrganizationCustomer**
```php
class SyncAsaasOrganizationCustomer
{
    public function perform(AsaasOrganizationCustomer $customer, ?AsaasEnvironment $environment = null): AsaasOrganizationCustomer
    {
        // 1. Buscar dados atualizados no ASAAS
        // 2. Atualizar dados locais
        // 3. Marcar como sincronizado
        // 4. Retornar domain atualizado
    }
}
```

### **3. UpdateAsaasOrganizationCustomer**
```php
class UpdateAsaasOrganizationCustomer
{
    public function perform(AsaasOrganizationCustomer $customer, ?AsaasEnvironment $environment = null): AsaasOrganizationCustomer
    {
        // 1. Atualizar dados no ASAAS
        // 2. Atualizar dados locais
        // 3. Marcar como sincronizado
        // 4. Retornar domain atualizado
    }
}
```

## 🔗 **RELACIONAMENTOS**

### **Organization Model - Adicionar Relacionamento**
```php
// app/Models/Organization.php
public function asaasCustomer(): HasOne
{
    return $this->hasOne(AsaasOrganizationCustomer::class);
}

public function hasAsaasCustomerIntegration(): bool
{
    return $this->asaasCustomer !== null;
}
```

## 📝 **TASKS DE IMPLEMENTAÇÃO**

### **Fase 1: Estrutura Base (Sprint 1)**
- [ ] **Task 1.1**: Criar migration `create_asaas_organization_customers_table`
  - Definir todas as colunas baseadas no AsaasClient
  - Adicionar índices apropriados
  - Foreign key para organizations
  - Unique constraint para asaas_customer_id

- [ ] **Task 1.2**: Implementar Model `AsaasOrganizationCustomer`
  - Fillable fields
  - Casts apropriados
  - Relacionamento com Organization
  - Métodos de conveniência (hasAsaasIntegration, needsSync, etc.)

- [ ] **Task 1.3**: Implementar Domain `AsaasOrganizationCustomer`
  - Constructor com todos os campos readonly
  - Métodos de negócio
  - Métodos de transformação (toAsaasPayload, toArray, etc.)

### **Fase 2: Factory e Repository (Sprint 1-2)**
- [ ] **Task 2.1**: Implementar Factory `AsaasOrganizationCustomerFactory`
  - buildFromModel
  - buildFromModels
  - buildFromAsaasResponse
  - buildFromOrganization
  - buildFromStoreArray

- [ ] **Task 2.2**: Implementar Repository `AsaasOrganizationCustomerRepository`
  - Métodos de busca (findById, findByOrganizationId, etc.)
  - Métodos de persistência (save, create, update, delete)
  - Métodos de listagem com filtros
  - Métodos para sincronização (findPendingSync, findNeedingSync)

### **Fase 3: Use Cases Principais (Sprint 2)**
- [ ] **Task 3.1**: Implementar `CreateOrganizationCustomer`
  - Validação de dados da organização
  - Integração com API ASAAS (usando nossa chave principal)
  - Criação do customer no ASAAS
  - Persistência local
  - Tratamento de erros e rollback

- [ ] **Task 3.2**: Implementar `SyncAsaasOrganizationCustomer`
  - Busca de dados atualizados no ASAAS
  - Comparação e atualização de dados locais
  - Controle de sincronização
  - Log de operações

- [ ] **Task 3.3**: Implementar `UpdateAsaasOrganizationCustomer`
  - Atualização no ASAAS
  - Atualização local
  - Sincronização bidirecional
  - Validação de dados

### **Fase 4: Integração e Relacionamentos (Sprint 2-3)**
- [ ] **Task 4.1**: Adicionar relacionamento em Organization
  - Método asaasCustomer() HasOne
  - Método hasAsaasCustomerIntegration()
  - Atualizar Organization Domain se necessário

- [ ] **Task 4.2**: Atualizar AsaasService para suporte a múltiplos contextos
  - Método para usar chave principal vs chave da organização
  - Configuração de ambiente apropriada
  - Rate limiting diferenciado

### **Fase 5: Testes (Sprint 3)**
- [ ] **Task 5.1**: Testes Unitários
  - Factory tests
  - Domain business logic tests
  - Repository operation tests
  - Use case validation tests

- [ ] **Task 5.2**: Testes de Integração
  - API ASAAS integration tests
  - Database operation tests
  - End-to-end workflow tests
  - Error handling tests

- [ ] **Task 5.3**: Testes de Performance
  - Bulk operation tests
  - Sync performance tests
  - Database query optimization tests

### **Fase 6: Comandos e Automação (Sprint 3-4)**
- [ ] **Task 6.1**: Comando de Sincronização
  - `asaas:sync-organization-customers`
  - Opções de filtro (organização específica, pendentes, com erro)
  - Relatório de resultados
  - Dry-run mode

- [ ] **Task 6.2**: Comando de Criação em Massa
  - `asaas:create-organization-customers`
  - Criação para organizações sem customer
  - Validação prévia
  - Relatório de criação

- [ ] **Task 6.3**: Cronjobs de Automação
  - Sincronização automática diária
  - Retry de operações com erro
  - Limpeza de logs antigos
  - Monitoramento de saúde

### **Fase 7: Documentação e Finalização (Sprint 4)**
- [ ] **Task 7.1**: Documentação Técnica
  - README específico para AsaasOrganizationCustomer
  - Exemplos de uso dos Use Cases
  - Guia de troubleshooting
  - Diagramas de fluxo

- [ ] **Task 7.2**: Documentação de API
  - Endpoints relacionados (se houver)
  - Payloads de exemplo
  - Códigos de erro
  - Rate limits

- [ ] **Task 7.3**: Validação Final
  - Code review completo
  - Testes em ambiente de staging
  - Validação com dados reais (sandbox)
  - Performance benchmarks

## ⚠️ **CONSIDERAÇÕES IMPORTANTES**

### **Diferenças do AsaasClient**
1. **API Key**: Usa nossa chave principal, não da organização
2. **Endpoint**: Mesmo endpoint `/v3/customers` mas com contexto diferente
3. **Relacionamento**: Organization ao invés de Client
4. **Propósito**: Cobrança da plataforma ao invés de vendas da organização

### **Segurança**
- Usar sempre nossa API key principal
- Validar permissões de organização
- Log de todas as operações
- Controle de rate limiting

### **Performance**
- Índices apropriados para consultas frequentes
- Paginação em listagens
- Cache quando apropriado
- Sincronização em background

## 💡 **EXEMPLOS PRÁTICOS**

### **Exemplo 1: Criação de Customer para Organização**
```php
// Use Case: Organização se cadastra na plataforma
$organization = Organization::find(1);

$createCustomer = app(CreateOrganizationCustomer::class);
$asaasCustomer = $createCustomer->perform($organization, AsaasEnvironment::SANDBOX);

// Resultado: Customer criado no ASAAS com nossa chave principal
// AsaasOrganizationCustomer salvo localmente
```

### **Exemplo 2: Sincronização de Dados**
```php
// Use Case: Sincronizar dados atualizados do ASAAS
$customer = AsaasOrganizationCustomer::where('organization_id', 1)->first();

$syncCustomer = app(SyncAsaasOrganizationCustomer::class);
$updatedCustomer = $syncCustomer->perform($customer);

// Resultado: Dados locais atualizados com informações do ASAAS
```

### **Exemplo 3: Comando de Sincronização em Massa**
```bash
# Sincronizar todos os customers pendentes
php artisan asaas:sync-organization-customers

# Sincronizar customer específico
php artisan asaas:sync-organization-customers --organization=1

# Dry run para ver o que seria sincronizado
php artisan asaas:sync-organization-customers --dry-run
```

## 🔄 **FLUXOS DE INTEGRAÇÃO**

### **Fluxo 1: Nova Organização**
```
1. Organization criada no sistema
2. Trigger automático ou manual para criar customer
3. CreateOrganizationCustomer::perform()
4. Customer criado no ASAAS (nossa conta principal)
5. AsaasOrganizationCustomer salvo localmente
6. Organização pronta para cobrança
```

### **Fluxo 2: Atualização de Dados**
```
1. Dados da organização alterados
2. Trigger para atualizar customer
3. UpdateAsaasOrganizationCustomer::perform()
4. Dados atualizados no ASAAS
5. Sincronização local
```

### **Fluxo 3: Sincronização Automática**
```
1. Cronjob diário executa
2. Busca customers que precisam sync
3. Para cada customer: SyncAsaasOrganizationCustomer::perform()
4. Dados locais atualizados
5. Log de operações
```

## 🧪 **TESTES**

### **Testes Unitários**
- Factory methods (buildFromModel, buildFromAsaasResponse, etc.)
- Domain business logic (hasAsaasIntegration, needsSync, etc.)
- Repository operations (CRUD, filters, bulk operations)
- Use case validations (data validation, error handling)

### **Testes de Integração**
- API ASAAS integration (create, update, sync)
- Database operations (transactions, rollbacks)
- End-to-end workflows (create -> sync -> update)
- Error handling (API failures, network issues)

### **Testes de Performance**
- Bulk operations (1000+ customers)
- Sync performance (concurrent operations)
- Database queries optimization (N+1 queries)
- Memory usage (large datasets)

## 🚨 **RISCOS E MITIGAÇÕES**

### **Risco 1: Conflito de API Keys**
- **Problema**: Usar chave errada (organização vs principal)
- **Mitigação**: Método específico no AsaasService para contexto
- **Validação**: Testes que verificam qual chave está sendo usada

### **Risco 2: Duplicação de Customers**
- **Problema**: Criar customer duplicado para mesma organização
- **Mitigação**: Validação prévia e unique constraint
- **Validação**: Testes de concorrência

### **Risco 3: Sincronização Inconsistente**
- **Problema**: Dados locais diferentes do ASAAS
- **Mitigação**: Sincronização automática e logs detalhados
- **Validação**: Testes de integridade de dados

### **Risco 4: Rate Limiting**
- **Problema**: Muitas requisições simultâneas
- **Mitigação**: Queue para operações em massa
- **Validação**: Testes de carga

## 📊 **MÉTRICAS DE SUCESSO**

### **Técnicas**
- ✅ 100% dos testes passando
- ✅ Cobertura de código > 90%
- ✅ Tempo de resposta < 2s para operações individuais
- ✅ Suporte a 1000+ customers simultâneos

### **Funcionais**
- ✅ Criação automática de customers para novas organizações
- ✅ Sincronização diária sem erros
- ✅ Dados sempre consistentes entre local e ASAAS
- ✅ Zero duplicações de customers

### **Operacionais**
- ✅ Logs detalhados para troubleshooting
- ✅ Comandos funcionais para administração
- ✅ Monitoramento de saúde da integração
- ✅ Documentação completa e atualizada

---

**Status**: 📋 Planejamento
**Prioridade**: 🔥 Alta
**Estimativa**: 3-4 sprints
**Dependências**: Epic 1 (AsaasClient) concluído
**Responsável**: Equipe Backend
**Reviewer**: Tech Lead
