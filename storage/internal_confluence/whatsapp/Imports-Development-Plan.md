# 📥 Imports Module Development Plan

## Overview
This document outlines a comprehensive development plan to improve our Imports module by building complete test coverage and enhancing Postman documentation. The plan covers 11 import entities with a focus on making our data import system more developer-friendly and robust.

## 🎯 Goals
- Build unit and integration/feature tests for all Import entities
- Complete test coverage for UseCases, Domains, Factories, and Repositories
- Improve Postman documentation with complete API field coverage
- Ensure all import workflows have proper request/response examples
- Test file upload, processing, and error handling scenarios

## 📋 Import Entities (11 Total)

### Core Import System
1. **Import** - Main import management entity with file handling
2. **Header** - CSV/Excel header row processing
3. **Core** - Base import functionality and mapping logic

### Entity-Specific Importers
4. **ImportBrand** - Brand data import from CSV/Excel
5. **ImportProduct** - Product data import with brand relationships
6. **ImportClient** - Client data import with full contact information
7. **ImportGroup** - Product group import functionality
8. **ImportProject** - Project data import with client/budget relationships
9. **ImportBudget** - Budget data import with client relationships
10. **ImportStock** - Stock level import with product relationships
11. **ImportStockEntry** - Stock entry transaction imports
12. **ImportStockExit** - Stock exit transaction imports

---

## 🚀 Development Tasks

### Task 1: Import Entity (Core System)
**Priority: High** | **Estimated Time: 6-7 hours**

#### Current Status
- ✅ Domain: Complex with file handling, header processing, and mapping logic
- ✅ UseCases: Complete CRUD + Process + AttachProducts
- ✅ Factory: Exists but needs testing
- ✅ Repository: Exists
- ⚠️ Tests: Missing comprehensive tests
- ✅ Postman: Basic endpoints exist
- ✅ Controller: Complete with file upload handling

#### Tasks
**Unit Tests:**
- [ ] Create `tests/Unit/Domains/Imports/ImportTest.php`
- [ ] Create `tests/Unit/Factories/ImportFactoryTest.php` with app()->make() test
- [ ] Create `tests/Unit/Repositories/ImportRepositoryTest.php`

**Feature/Integration Tests:**
- [ ] Create `tests/Feature/Imports/Import/StoreTest.php` (file upload testing)
- [ ] Create `tests/Feature/Imports/Import/UpdateTest.php`
- [ ] Create `tests/Feature/Imports/Import/DeleteTest.php`
- [ ] Create `tests/Feature/Imports/Import/GetAllTest.php`
- [ ] Create `tests/Feature/Imports/Import/ProcessTest.php` (core import processing)
- [ ] Create `tests/Feature/Imports/Import/AttachProductsTest.php`

**Postman Documentation:**
- [ ] Add all Import fields: model, status, header, map, is_processed, file, filename, filepath, filesize, file_extension, file_mime_type
- [ ] Include file upload examples with proper multipart/form-data
- [ ] Document header processing and mapping workflow
- [ ] Add Process endpoint with status tracking

---

### Task 2: Header Entity
**Priority: Medium** | **Estimated Time: 3-4 hours**

#### Current Status
- ✅ Domain: CSV/Excel header processing functionality
- ❌ UseCases: Missing dedicated use cases
- ❌ Factory: Missing
- ❌ Repository: Missing
- ❌ Tests: Missing all tests
- ❌ Postman: Missing endpoints

#### Tasks
**Unit Tests:**
- [ ] Create `tests/Unit/Domains/Imports/HeaderTest.php`
- [ ] Create `tests/Unit/Factories/Imports/HeaderFactoryTest.php` with app()->make() test

**Feature/Integration Tests:**
- [ ] Create `tests/Feature/Imports/Header/ProcessHeaderTest.php`
- [ ] Create `tests/Feature/Imports/Header/ValidateHeaderTest.php`

**Postman Documentation:**
- [ ] Create header processing endpoints
- [ ] Include CSV/Excel header validation examples
- [ ] Document column mapping functionality

---

### Task 3: Core Entity
**Priority: Medium** | **Estimated Time: 3-4 hours**

#### Current Status
- ✅ Domain: Base import functionality with mapping logic
- ❌ UseCases: Missing dedicated use cases
- ❌ Factory: Missing
- ❌ Repository: Missing
- ❌ Tests: Missing all tests
- ❌ Postman: Missing endpoints

#### Tasks
**Unit Tests:**
- [ ] Create `tests/Unit/Domains/Imports/CoreTest.php`
- [ ] Test mapping logic and row processing methods

**Feature/Integration Tests:**
- [ ] Create `tests/Feature/Imports/Core/MappingLogicTest.php`
- [ ] Create `tests/Feature/Imports/Core/RowProcessingTest.php`

**Postman Documentation:**
- [ ] Document mapping configuration endpoints
- [ ] Include column mapping examples
- [ ] Add row processing validation

---

### Task 4: ImportBrand Entity
**Priority: High** | **Estimated Time: 4-5 hours**

#### Current Status
- ✅ Domain: Complete with brand-specific import logic
- ❌ UseCases: Missing dedicated use cases
- ❌ Factory: Missing
- ❌ Repository: Missing
- ❌ Tests: Missing all tests
- ❌ Postman: Missing endpoints

#### Tasks
**Unit Tests:**
- [ ] Create `tests/Unit/Domains/Imports/ImportBrandTest.php`
- [ ] Create `tests/Unit/Factories/Imports/ImportBrandFactoryTest.php` with app()->make() test

**Feature/Integration Tests:**
- [ ] Create `tests/Feature/Imports/ImportBrand/ProcessBrandImportTest.php`
- [ ] Create `tests/Feature/Imports/ImportBrand/ValidateBrandDataTest.php`
- [ ] Create `tests/Feature/Imports/ImportBrand/BrandCreationTest.php`

**Postman Documentation:**
- [ ] Create brand import endpoints
- [ ] Add all brand fields: name, description
- [ ] Include CSV template examples
- [ ] Document validation rules and error handling

---

### Task 5: ImportProduct Entity
**Priority: High** | **Estimated Time: 5-6 hours**

#### Current Status
- ✅ Domain: Complex with brand relationship handling
- ❌ UseCases: Missing dedicated use cases
- ❌ Factory: Missing
- ❌ Repository: Missing
- ❌ Tests: Missing all tests
- ❌ Postman: Missing endpoints

#### Tasks
**Unit Tests:**
- [ ] Create `tests/Unit/Domains/Imports/ImportProductTest.php`
- [ ] Create `tests/Unit/Factories/Imports/ImportProductFactoryTest.php` with app()->make() test

**Feature/Integration Tests:**
- [ ] Create `tests/Feature/Imports/ImportProduct/ProcessProductImportTest.php`
- [ ] Create `tests/Feature/Imports/ImportProduct/BrandRelationshipTest.php`
- [ ] Create `tests/Feature/Imports/ImportProduct/ProductCreationTest.php`
- [ ] Create `tests/Feature/Imports/ImportProduct/PriceValidationTest.php`

**Postman Documentation:**
- [ ] Add all product fields: brand_id, name, description, price, unity, last_priced_at
- [ ] Include brand relationship examples
- [ ] Document price validation and date formatting
- [ ] Add CSV template with all required columns

---

### Task 6: ImportClient Entity
**Priority: High** | **Estimated Time: 5-6 hours**

#### Current Status
- ✅ Domain: Comprehensive with full client contact information
- ❌ UseCases: Missing dedicated use cases
- ❌ Factory: Missing
- ❌ Repository: Missing
- ❌ Tests: Missing all tests
- ❌ Postman: Missing endpoints

#### Tasks
**Unit Tests:**
- [ ] Create `tests/Unit/Domains/Imports/ImportClientTest.php`
- [ ] Create `tests/Unit/Factories/Imports/ImportClientFactoryTest.php` with app()->make() test

**Feature/Integration Tests:**
- [ ] Create `tests/Feature/Imports/ImportClient/ProcessClientImportTest.php`
- [ ] Create `tests/Feature/Imports/ImportClient/ContactValidationTest.php`
- [ ] Create `tests/Feature/Imports/ImportClient/AddressProcessingTest.php`
- [ ] Create `tests/Feature/Imports/ImportClient/CPFCNPJValidationTest.php`

**Postman Documentation:**
- [ ] Add all client fields: name, phone, email, profession, birthdate, cpf, cnpj, service, address, number, neighborhood, cep, complement, civil_state, description
- [ ] Include contact validation examples
- [ ] Document address formatting requirements
- [ ] Add CPF/CNPJ validation rules

---

### Task 7: ImportGroup Entity
**Priority: Medium** | **Estimated Time: 3-4 hours**

#### Current Status
- ✅ Domain: Simple group import functionality
- ❌ UseCases: Missing dedicated use cases
- ❌ Factory: Missing
- ❌ Repository: Missing
- ❌ Tests: Missing all tests
- ❌ Postman: Missing endpoints

#### Tasks
**Unit Tests:**
- [ ] Create `tests/Unit/Domains/Imports/ImportGroupTest.php`
- [ ] Create `tests/Unit/Factories/Imports/ImportGroupFactoryTest.php` with app()->make() test

**Feature/Integration Tests:**
- [ ] Create `tests/Feature/Imports/ImportGroup/ProcessGroupImportTest.php`
- [ ] Create `tests/Feature/Imports/ImportGroup/GroupCreationTest.php`

**Postman Documentation:**
- [ ] Create group import endpoints
- [ ] Add group fields: name, description
- [ ] Include CSV template examples

---

### Task 8: ImportProject Entity
**Priority: Medium** | **Estimated Time: 4-5 hours**

#### Current Status
- ✅ Domain: Complex with client and budget relationships
- ❌ UseCases: Missing dedicated use cases
- ❌ Factory: Missing
- ❌ Repository: Missing
- ❌ Tests: Missing all tests
- ❌ Postman: Missing endpoints

#### Tasks
**Unit Tests:**
- [ ] Create `tests/Unit/Domains/Imports/ImportProjectTest.php`
- [ ] Create `tests/Unit/Factories/Imports/ImportProjectFactoryTest.php` with app()->make() test

**Feature/Integration Tests:**
- [ ] Create `tests/Feature/Imports/ImportProject/ProcessProjectImportTest.php`
- [ ] Create `tests/Feature/Imports/ImportProject/ClientBudgetRelationshipTest.php`
- [ ] Create `tests/Feature/Imports/ImportProject/ValueCalculationTest.php`

**Postman Documentation:**
- [ ] Add all project fields: client_id, budget_id, name, description, value, cost
- [ ] Include relationship handling examples
- [ ] Document value and cost validation

---

### Task 9: ImportBudget Entity
**Priority: Medium** | **Estimated Time: 4-5 hours**

#### Current Status
- ✅ Domain: Budget import with client relationships
- ❌ UseCases: Missing dedicated use cases
- ❌ Factory: Missing
- ❌ Repository: Missing
- ❌ Tests: Missing all tests
- ❌ Postman: Missing endpoints

#### Tasks
**Unit Tests:**
- [ ] Create `tests/Unit/Domains/Imports/ImportBudgetTest.php`
- [ ] Create `tests/Unit/Factories/Imports/ImportBudgetFactoryTest.php` with app()->make() test

**Feature/Integration Tests:**
- [ ] Create `tests/Feature/Imports/ImportBudget/ProcessBudgetImportTest.php`
- [ ] Create `tests/Feature/Imports/ImportBudget/ClientRelationshipTest.php`
- [ ] Create `tests/Feature/Imports/ImportBudget/FinancialValidationTest.php`

**Postman Documentation:**
- [ ] Add all budget fields: client_id, value, cost, name, description
- [ ] Include client relationship examples
- [ ] Document financial validation rules

---

### Task 10: ImportStock Entity
**Priority: High** | **Estimated Time: 4-5 hours**

#### Current Status
- ✅ Domain: Stock import with product and brand relationships
- ❌ UseCases: Missing dedicated use cases
- ❌ Factory: Missing
- ❌ Repository: Missing
- ❌ Tests: Missing all tests
- ❌ Postman: Missing endpoints

#### Tasks
**Unit Tests:**
- [ ] Create `tests/Unit/Domains/Imports/ImportStockTest.php`
- [ ] Create `tests/Unit/Factories/Imports/ImportStockFactoryTest.php` with app()->make() test

**Feature/Integration Tests:**
- [ ] Create `tests/Feature/Imports/ImportStock/ProcessStockImportTest.php`
- [ ] Create `tests/Feature/Imports/ImportStock/ProductBrandRelationshipTest.php`
- [ ] Create `tests/Feature/Imports/ImportStock/QuantityValidationTest.php`
- [ ] Create `tests/Feature/Imports/ImportStock/ValueCalculationTest.php`

**Postman Documentation:**
- [ ] Add all stock fields: brand_id, product_id, quantity, value, description
- [ ] Include product and brand relationship examples
- [ ] Document quantity and value validation
- [ ] Add stock level management examples

---

### Task 11: ImportStockEntry Entity
**Priority: Medium** | **Estimated Time: 3-4 hours**

#### Current Status
- ✅ Domain: Stock entry transaction import
- ❌ UseCases: Missing dedicated use cases
- ❌ Factory: Missing
- ❌ Repository: Missing
- ❌ Tests: Missing all tests
- ❌ Postman: Missing endpoints

#### Tasks
**Unit Tests:**
- [ ] Create `tests/Unit/Domains/Imports/ImportStockEntryTest.php`
- [ ] Create `tests/Unit/Factories/Imports/ImportStockEntryFactoryTest.php` with app()->make() test

**Feature/Integration Tests:**
- [ ] Create `tests/Feature/Imports/ImportStockEntry/ProcessStockEntryImportTest.php`
- [ ] Create `tests/Feature/Imports/ImportStockEntry/TransactionValidationTest.php`

**Postman Documentation:**
- [ ] Create stock entry import endpoints
- [ ] Include transaction validation examples
- [ ] Document stock increase logic

---

### Task 12: ImportStockExit Entity
**Priority: Medium** | **Estimated Time: 3-4 hours**

#### Current Status
- ✅ Domain: Stock exit transaction import
- ❌ UseCases: Missing dedicated use cases
- ❌ Factory: Missing
- ❌ Repository: Missing
- ❌ Tests: Missing all tests
- ❌ Postman: Missing endpoints

#### Tasks
**Unit Tests:**
- [ ] Create `tests/Unit/Domains/Imports/ImportStockExitTest.php`
- [ ] Create `tests/Unit/Factories/Imports/ImportStockExitFactoryTest.php` with app()->make() test

**Feature/Integration Tests:**
- [ ] Create `tests/Feature/Imports/ImportStockExit/ProcessStockExitImportTest.php`
- [ ] Create `tests/Feature/Imports/ImportStockExit/TransactionValidationTest.php`

**Postman Documentation:**
- [ ] Create stock exit import endpoints
- [ ] Include transaction validation examples
- [ ] Document stock decrease logic

---

## 🛠️ Implementation Guidelines

### Critical Testing Requirements
1. **Circular Dependency Prevention**:
   - Always use `app()->make()` when encountering circular import issues
   - Test factory resolution through service container in every factory test
   - Avoid direct instantiation of factories with complex dependencies

2. **Factory Service Container Testing**:
   - Every factory MUST have a test verifying `app()->make(FactoryClass::class)` works
   - Example test pattern:
   ```php
   public function test_factory_can_be_resolved_via_service_container()
   {
       $factory = app()->make(ImportEntityFactory::class);
       $this->assertInstanceOf(ImportEntityFactory::class, $factory);
   }
   ```

3. **File Upload Testing**:
   - Test CSV and Excel file uploads
   - Validate file size and type restrictions
   - Test malformed file handling
   - Verify file storage and cleanup

### Import-Specific Guidelines

#### File Processing Testing
- Test header row detection and parsing
- Validate column mapping functionality
- Test data type conversion and validation
- Verify error handling for invalid data
- Test batch processing for large files

#### Data Validation Testing
- Test required field validation
- Validate data format constraints (dates, numbers, emails)
- Test relationship validation (foreign keys)
- Verify duplicate detection logic
- Test data sanitization and cleaning

#### Error Handling Testing
- Test import failure scenarios
- Validate error message generation
- Test partial import handling
- Verify rollback functionality
- Test import status tracking

### Test Organization
```
tests/
├── Unit/
│   ├── Domains/Imports/
│   └── Factories/Imports/
└── Feature/
    └── Imports/
        ├── Import/
        ├── ImportBrand/
        ├── ImportProduct/
        ├── ImportClient/
        └── ... (one folder per import entity)
```

### Priority Implementation Order

#### Phase 1: Core Import System (Week 1)
1. **Import** - Main import management
2. **Header** - CSV/Excel processing
3. **Core** - Base functionality

#### Phase 2: High-Priority Importers (Week 2)
1. **ImportBrand** - Foundation for products
2. **ImportProduct** - Core inventory
3. **ImportClient** - Customer data
4. **ImportStock** - Inventory levels

#### Phase 3: Supporting Importers (Week 3)
1. **ImportGroup** - Product organization
2. **ImportProject** - Project management
3. **ImportBudget** - Financial data
4. **ImportStockEntry/Exit** - Stock transactions

### Success Metrics
- [ ] 100% test coverage for all Import entities
- [ ] File upload and processing thoroughly tested
- [ ] Data validation and error handling verified
- [ ] All Postman endpoints include complete field coverage
- [ ] CSV/Excel template documentation complete
- [ ] Factory service container resolution verified

### Next Steps
1. **FIRST**: Audit existing import endpoints against domain fields
2. Start with **Import** entity as it's the foundation
3. **MANDATORY**: Implement factory service container test for every factory
4. Focus on file upload and processing workflows
5. Test data validation and error scenarios
6. Create comprehensive CSV/Excel templates

### Critical Implementation Notes
- **File Handling**: Test upload, storage, processing, and cleanup
- **Data Validation**: Comprehensive validation for all data types
- **Error Handling**: Robust error reporting and recovery
- **Performance**: Test large file processing and memory usage
- **Security**: Validate file types and content sanitization
