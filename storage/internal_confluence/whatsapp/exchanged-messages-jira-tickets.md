# Tickets Jira - Melhorias ExchangedMessages e Controle ChatBot

## Ticket 1: Adicionar Campo de Controle de ChatBot em PhoneNumber

### **Título:** Implementar campo is_chatbot_activated para controle granular do ChatBot

### **Descrição:**
Atualmente não existe uma forma de desabilitar o processamento do ChatBot para números específicos, o que impede o suporte a números 100% manuais. Precisamos adicionar um campo booleano `is_chatbot_activated` na tabela `phone_numbers` para permitir controle granular sobre quando o ChatBot deve ou não processar mensagens.

### **Contexto:**
- Todo webhook de mensagem sempre tenta processar via ChatBot, independente da configuração
- Não há suporte para números que devem ser operados 100% manualmente
- Clientes podem querer desabilitar temporariamente o ChatBot sem perder a configuração do flow

### **Acceptance Criteria:**
1. **Migração de Banco:**
   - Criar migração para adicionar campo `is_chatbot_activated` (BOOLEAN, DEFAULT TRUE)
   - Campo deve ser nullable inicialmente para compatibilidade
   - Após migração, todos os registros existentes devem ter valor TRUE

2. **Atualização do Domain:**
   - Adicionar propriedade `is_chatbot_activated` no domain PhoneNumber
   - Atualizar factory para incluir o novo campo
   - Manter compatibilidade com código existente

3. **Validação:**
   - Campo deve aceitar apenas valores booleanos (true/false)
   - Valor padrão deve ser TRUE (ChatBot ativo por padrão)
   - Não deve quebrar funcionalidade existente

4. **Testes:**
   - Teste unitário para verificar valor padrão
   - Teste de migração para garantir compatibilidade
   - Teste de factory com novo campo

### **Definição de Pronto:**
- Migração executada sem erros
- Domain PhoneNumber atualizado
- Factory atualizada
- Testes passando
- Documentação atualizada

---

## Ticket 2: Implementar Lógica de Verificação de Ativação do ChatBot

### **Título:** Criar método shouldProcessChatBot para controlar processamento do ChatBot

### **Descrição:**
Implementar a lógica de verificação que determina se o ChatBot deve processar mensagens para um número específico. Esta verificação deve considerar múltiplos fatores: se o número está ativo, se o ChatBot está habilitado e se existe um flow configurado.

### **Contexto:**
- Atualmente o ChatBot sempre tenta processar, mesmo quando não deveria
- Precisamos de uma lógica centralizada para determinar quando processar
- Deve suportar diferentes cenários: números manuais, temporariamente desabilitados, etc.

### **Acceptance Criteria:**
1. **Método de Verificação:**
   - Criar método `shouldProcessChatBot(PhoneNumber $phoneNumber): bool`
   - Verificar se `is_active = true`
   - Verificar se `is_chatbot_activated = true`
   - Verificar se `flow_id` não é null
   - Retornar true apenas se TODAS as condições forem atendidas

2. **Cenários Suportados:**
   - **ChatBot Ativo**: is_active=true, is_chatbot_activated=true, flow_id=123 → Retorna TRUE
   - **Número Manual**: is_active=true, is_chatbot_activated=false, flow_id=qualquer → Retorna FALSE
   - **Temporariamente Desabilitado**: is_active=true, is_chatbot_activated=false, flow_id=123 → Retorna FALSE
   - **Número Inativo**: is_active=false, qualquer configuração → Retorna FALSE
   - **Sem Flow**: flow_id=null, qualquer configuração → Retorna FALSE

3. **Localização:**
   - Método deve ser criado em local apropriado (service ou use case)
   - Deve ser facilmente testável e reutilizável
   - Documentação clara sobre cada condição

4. **Testes:**
   - Teste para cada cenário listado acima
   - Teste de edge cases (valores null, etc.)
   - Teste de performance (método será chamado frequentemente)

### **Definição de Pronto:**
- Método implementado e funcionando
- Todos os cenários testados
- Documentação clara
- Performance adequada

---

## Ticket 3: Refatorar SaveExchangedMessages para ProcessWebhookMessage

### **Título:** Mover registro de ExchangedMessages para ProcessWebhookMessage com webhookLogId

### **Descrição:**
Atualmente o registro de ExchangedMessages acontece em ProcessWebhook, mas deveria acontecer em ProcessWebhookMessage que é onde toda mensagem de cliente é processada. Além disso, o webhookLogId não está sendo passado, prejudicando a rastreabilidade. Precisamos mover esta funcionalidade para o local correto e garantir que o webhookLogId seja passado.

### **Contexto:**
- ProcessWebhookMessage roda para TODA mensagem de cliente
- É o local ideal para registrar ExchangedMessages inbound
- webhookLogId é necessário para rastreabilidade completa
- Atualmente está em ProcessWebhook, que é muito genérico

### **Acceptance Criteria:**
1. **Atualização de Assinatura:**
   - Adicionar parâmetro `?int $webhookLogId = null` em ProcessWebhookMessage.perform()
   - Manter compatibilidade com chamadas existentes
   - Documentar o novo parâmetro

2. **Movimentação da Lógica:**
   - Remover chamada de SaveExchangedMessagesFromWebhook de ProcessWebhook
   - Adicionar chamada em ProcessWebhookMessage ANTES do processamento do ChatBot
   - Garantir que ExchangedMessages sejam sempre registradas, independente do ChatBot

3. **Atualização de Chamadas:**
   - Atualizar ProcessWebhook para passar webhookLogId para ProcessWebhookMessage
   - Verificar todas as outras chamadas de ProcessWebhookMessage
   - Garantir que webhookLogId seja propagado corretamente

4. **Fluxo Correto:**
   - ProcessWebhook recebe webhook
   - ProcessWebhook chama ProcessWebhookMessage com webhookLogId
   - ProcessWebhookMessage registra ExchangedMessages PRIMEIRO
   - ProcessWebhookMessage verifica se deve processar ChatBot
   - Se sim, processa via ChatBot; se não, retorna sucesso sem processamento

5. **Testes:**
   - Teste que ExchangedMessages são registradas mesmo quando ChatBot não processa
   - Teste que webhookLogId é corretamente passado e usado
   - Teste de compatibilidade com chamadas sem webhookLogId
   - Teste de integração do fluxo completo

### **Definição de Pronto:**
- Lógica movida para ProcessWebhookMessage
- webhookLogId sendo passado corretamente
- ExchangedMessages registradas em todos os cenários
- Testes passando
- Funcionalidade existente não quebrada

---

## Ticket 4: Integrar Controle de ChatBot em ProcessWebhookMessage

### **Título:** Implementar verificação de ativação do ChatBot em ProcessWebhookMessage

### **Descrição:**
Integrar a lógica de verificação de ativação do ChatBot no ProcessWebhookMessage para que o processamento do ChatBot seja condicionalmente executado baseado na configuração do PhoneNumber. Isso permitirá suporte completo a números manuais e controle granular do ChatBot.

### **Contexto:**
- Atualmente o ChatBot sempre tenta processar mensagens
- Com o novo campo is_chatbot_activated, precisamos usar essa informação
- ProcessWebhookMessage é o local ideal para esta verificação
- Deve manter registro de ExchangedMessages independente do ChatBot

### **Acceptance Criteria:**
1. **Integração da Verificação:**
   - Usar método shouldProcessChatBot() em ProcessWebhookMessage
   - Verificação deve acontecer APÓS registro de ExchangedMessages
   - ChatBot só deve processar se verificação retornar true

2. **Fluxo Atualizado:**
   - Receber mensagem em ProcessWebhookMessage
   - Registrar ExchangedMessages (SEMPRE)
   - Verificar se deve processar ChatBot
   - Se SIM: processar via chatBotService.processWebhook()
   - Se NÃO: retornar sucesso indicando que foi processado como manual

3. **Respostas Diferenciadas:**
   - Quando ChatBot processa: retornar resultado do ChatBot
   - Quando é manual: retornar estrutura indicando processamento manual
   - Incluir informação sobre por que ChatBot não processou (se aplicável)

4. **Estrutura de Resposta:**
   - success: true/false
   - type: 'chatbot' ou 'manual'
   - chatbot_processed: true/false
   - reason_skipped: string (se ChatBot foi pulado)
   - processed_count: número de mensagens processadas
   - organization_id e phone_number_id para rastreabilidade

5. **Cenários de Teste:**
   - Número com ChatBot ativo: deve processar via ChatBot
   - Número manual: deve registrar ExchangedMessage mas não processar ChatBot
   - Número temporariamente desabilitado: deve funcionar como manual
   - Número inativo: deve retornar erro apropriado
   - Número sem flow: deve funcionar como manual

6. **Logs e Monitoramento:**
   - Log quando ChatBot é pulado e por quê
   - Métricas para acompanhar números manuais vs automáticos
   - Informações suficientes para debugging

### **Definição de Pronto:**
- Verificação integrada em ProcessWebhookMessage
- Todos os cenários funcionando corretamente
- Respostas diferenciadas implementadas
- Logs e monitoramento adequados
- Testes cobrindo todos os cenários
- Documentação atualizada

---

## Ticket 5: Criar Testes de Integração para Fluxo Completo

### **Título:** Implementar testes de integração para fluxo ExchangedMessages e controle ChatBot

### **Descrição:**
Criar uma suíte completa de testes de integração que valide todo o fluxo desde o recebimento do webhook até o registro de ExchangedMessages e processamento (ou não) do ChatBot, cobrindo todos os cenários possíveis de configuração de PhoneNumber.

### **Contexto:**
- Mudanças significativas no fluxo de processamento
- Múltiplos cenários de configuração para testar
- Necessário garantir que nada quebrou e tudo funciona como esperado
- Testes devem servir como documentação viva do comportamento

### **Acceptance Criteria:**
1. **Cenários de Teste:**
   - **Número ChatBot Ativo**: Deve registrar ExchangedMessage E processar ChatBot
   - **Número Manual**: Deve registrar ExchangedMessage mas NÃO processar ChatBot
   - **Número Temporariamente Desabilitado**: Deve funcionar como manual
   - **Número Inativo**: Deve retornar erro apropriado
   - **Número Sem Flow**: Deve registrar ExchangedMessage mas não processar ChatBot

2. **Validações por Teste:**
   - ExchangedMessage foi criada corretamente
   - webhookLogId foi associado corretamente
   - ChatBot foi ou não processado conforme esperado
   - Resposta tem estrutura correta
   - Logs foram gerados adequadamente

3. **Dados de Teste:**
   - Criar factories para diferentes configurações de PhoneNumber
   - Usar dados realistas de webhook do WhatsApp
   - Incluir casos edge (dados malformados, campos faltando, etc.)

4. **Testes de Performance:**
   - Verificar que verificação de ativação não impacta performance
   - Testar com volume de mensagens simulado
   - Validar que não há vazamentos de memória

5. **Testes de Regressão:**
   - Garantir que funcionalidade existente não quebrou
   - Testar compatibilidade com código legado
   - Validar que migrações funcionam corretamente

6. **Documentação dos Testes:**
   - Cada teste deve ter descrição clara do que valida
   - Comentários explicando cenários complexos
   - README com instruções para executar testes

### **Definição de Pronto:**
- Todos os cenários testados
- Testes passando consistentemente
- Cobertura de código adequada
- Performance validada
- Documentação completa
- Testes servindo como documentação do comportamento

---

## Resumo dos Tickets

### **Ordem de Execução Recomendada:**
1. **Ticket 1**: Adicionar campo is_chatbot_activated (base para tudo)
2. **Ticket 2**: Implementar lógica de verificação (lógica central)
3. **Ticket 3**: Refatorar SaveExchangedMessages (mover para local correto)
4. **Ticket 4**: Integrar controle ChatBot (juntar tudo)
5. **Ticket 5**: Testes de integração (validar tudo funcionando)

### **Estimativas:**
- Ticket 1: 2-3 pontos (simples, mas requer cuidado com migração)
- Ticket 2: 3-5 pontos (lógica central, precisa ser bem testada)
- Ticket 3: 5-8 pontos (refatoração delicada, muitos pontos de integração)
- Ticket 4: 5-8 pontos (integração complexa, muitos cenários)
- Ticket 5: 8-13 pontos (testes abrangentes, validação completa)

### **Total Estimado:** 23-37 pontos (aproximadamente 1-2 sprints dependendo da capacidade da equipe)
