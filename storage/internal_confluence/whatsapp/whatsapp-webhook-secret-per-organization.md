# 🔐 WhatsApp Webhook Secret por Organização - IMPLEMENTADO

## ✅ **Implementação Completa**

O sistema agora suporta **webhook secrets únicos por organização**, resolvendo o problema de validação de assinatura HMAC para múltiplas organizações.

---

## 🏗️ **Mudanças Implementadas**

### **1. Database Schema**
- ✅ **Nova coluna**: `whatsapp_webhook_secret` na tabela `organizations`
- ✅ **Migration executada**: `2025_01_15_000001_add_whatsapp_webhook_secret_to_organizations_table`

### **2. Domain Organization**
- ✅ **Propriedade adicionada**: `public ?string $whatsapp_webhook_secret`
- ✅ **Construtor atualizado**: Parâmetro `whatsapp_webhook_secret`
- ✅ **toArray() atualizado**: Inclui o novo campo

### **3. Model Organization**
- ✅ **Fillable atualizado**: Campo `whatsapp_webhook_secret` adicionado
- ✅ **Factory atualizada**: Suporte ao novo campo

### **4. Novo UseCase**
- ✅ **ValidateWebhookSignatureWithOrganization**: Validação com organização específica
- ✅ **Extração automática**: phone_number_id do payload
- ✅ **Logs detalhados**: Para debugging e auditoria

### **5. Controller Atualizado**
- ✅ **Fluxo novo**: Extrai phone_number_id → Busca organização → Valida assinatura
- ✅ **Método novo**: `processChangeWithOrganization()`
- ✅ **Logs melhorados**: Rastreamento completo do processo

---

## 🔄 **Novo Fluxo de Validação**

### **Antes (Problema)**
```
Webhook → Validação Global → ❌ Falha (secret único)
```

### **Agora (Solução)**
```
Webhook → Extrai phone_number_id → Busca Organização → 
Valida com secret da organização → ✅ Sucesso
```

---

## ⚙️ **Como Configurar**

### **1. Cadastrar Webhook Secret da Organização**

#### **Via SQL (Temporário)**
```sql
UPDATE organizations 
SET whatsapp_webhook_secret = 'SEU_WEBHOOK_SECRET_DO_META_BUSINESS'
WHERE id = 1; -- ID da sua organização
```

#### **Via API (Recomendado)**
```json
PUT /api/organizations/1
{
    "whatsapp_webhook_secret": "SEU_WEBHOOK_SECRET_DO_META_BUSINESS"
}
```

### **2. Onde Encontrar o Webhook Secret**

1. **Meta Business Manager** → **WhatsApp Business Platform**
2. Selecione seu app
3. **Configuração** → **Webhooks**
4. Copie o **Webhook Secret** exato

### **3. Configurar para Múltiplas Organizações**

Cada organização deve ter seu próprio:
- **WhatsApp Business App** no Meta
- **phone_number_id** único
- **webhook_secret** único

---

## 🧪 **Como Testar**

### **1. Verificar Configuração**
```sql
SELECT id, name, whatsapp_webhook_secret 
FROM organizations 
WHERE whatsapp_webhook_secret IS NOT NULL;
```

### **2. Testar Webhook**
1. Configure o webhook secret na organização
2. Envie um webhook de teste
3. Verifique os logs: `tail -f storage/logs/laravel.log`

### **3. Logs Esperados**
```
WhatsApp webhook: Valid signature for organization 1
DEBUG: Webhook log created with ID: 123
```

---

## 🔍 **Debugging**

### **Logs de Validação**
O sistema agora gera logs detalhados:

#### **✅ Sucesso**
```
WhatsApp webhook: Valid signature for organization {id}
```

#### **❌ Organização não encontrada**
```
WhatsApp webhook: Could not identify organization for phone number ID: {id}
```

#### **❌ Secret não configurado**
```
WhatsApp webhook: Organization {id} does not have webhook secret configured
```

#### **❌ Assinatura inválida**
```
WhatsApp webhook: Invalid signature for organization {id}
```

### **Verificar Configuração**
```bash
# Verificar se phone_number_id está cadastrado
php artisan tinker
>>> App\Models\Organization::whereNotNull('whatsapp_webhook_secret')->get(['id', 'name']);

# Verificar phone numbers
>>> App\Models\PhoneNumber::all(['id', 'whatsapp_phone_number_id', 'organization_id']);
```

---

## 📊 **Status dos Webhooks**

### **Cenários Suportados**

#### **✅ Funcionando**
- Organização com webhook secret configurado
- phone_number_id cadastrado corretamente
- Assinatura HMAC válida

#### **⚠️ Precisa Configuração**
- Organização sem webhook secret
- phone_number_id não cadastrado

#### **❌ Falha**
- Assinatura HMAC inválida
- Payload malformado
- Organização não encontrada

---

## 🚀 **Próximos Passos**

### **1. Configurar Organizações Existentes**
```sql
-- Para cada organização, configure o webhook secret
UPDATE organizations 
SET whatsapp_webhook_secret = 'SECRET_DA_ORGANIZACAO_1'
WHERE id = 1;

UPDATE organizations 
SET whatsapp_webhook_secret = 'SECRET_DA_ORGANIZACAO_2'
WHERE id = 2;
```

### **2. Verificar phone_numbers**
```sql
-- Verificar se todos os phone_number_id estão cadastrados
SELECT p.whatsapp_phone_number_id, o.name, o.whatsapp_webhook_secret
FROM phone_numbers p
JOIN organizations o ON p.organization_id = o.id;
```

### **3. Testar Webhooks**
1. Configure webhook secrets
2. Envie mensagens de teste
3. Verifique logs de webhook
4. Confirme criação de WhatsAppWebhookLog

---

## 🔧 **Comandos Úteis**

### **Verificar Configuração**
```bash
# Ver organizações com secret configurado
php artisan tinker
>>> App\Models\Organization::whereNotNull('whatsapp_webhook_secret')->count();

# Ver últimos webhook logs
>>> App\Models\WhatsAppWebhookLog::latest()->take(5)->get(['id', 'organization_id', 'event_type', 'processing_status']);
```

### **Limpar Cache**
```bash
php artisan config:clear
php artisan route:clear
php artisan cache:clear
```

---

## ✅ **Checklist de Implementação**

- [x] Migration executada
- [x] Domain Organization atualizado
- [x] Model Organization atualizado
- [x] Factory atualizada
- [x] UseCase ValidateWebhookSignatureWithOrganization criado
- [x] Controller atualizado com novo fluxo
- [x] Logs de debug removidos
- [x] Documentação criada

### **Checklist de Configuração**

- [ ] Webhook secret configurado para cada organização
- [ ] phone_number_id cadastrado para cada organização
- [ ] Testes de webhook realizados
- [ ] Logs de webhook funcionando
- [ ] WhatsAppWebhookLog sendo criado

---

## 🎯 **Resultado Final**

Agora o sistema:

1. **Extrai phone_number_id** do payload do webhook
2. **Busca a organização** correspondente
3. **Usa o webhook secret específico** da organização
4. **Valida a assinatura HMAC** corretamente
5. **Processa o webhook** com sucesso
6. **Cria logs** de auditoria completos

**Status**: ✅ **IMPLEMENTADO E PRONTO PARA USO**

Configure os webhook secrets das suas organizações e teste! 🚀
