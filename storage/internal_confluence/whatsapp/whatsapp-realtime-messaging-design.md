# WhatsApp Real-Time Messaging API - Technical Document Design

## Aná<PERSON><PERSON> da Situação Atual

### Infraestrutura Existente ✅
- **WhatsApp Service**: Já implementado e funcional (`app/Services/Meta/WhatsApp/MessageService.php`)
- **Message Domain**: Completo com métodos `toWhatsAppPayload()`, `toWhatsAppTextPayload()`, `toWhatsAppTemplatePayload()`
- **WhatsApp API Integration**: Funcionando via `WhatsAppService::post()` method
- **Message Tracking**: Sistema de tracking com `WhatsAppMessage` domain e `SaveWhatsAppMessage` use case
- **Direct Messages**: Suporte existente via flag `is_direct_message`
- **Database Structure**: `campaign_id` é **nullable** na tabela `messages` ✅
- **Webhook Integration**: Sistema completo de webhooks funciona via `external_wam_id` (WhatsApp Message ID)

### Fluxo Atual (CRON)
1. <PERSON><PERSON><PERSON> geram mensagens via `GenerateMessages` use case
2. Messages ficam com status `is_sending`
3. CRON `SendWhatsAppMessages` processa mensagens pendentes
4. `Send` use case utiliza `MessageService::send()`
5. Tracking salvo via `SaveWhatsAppMessage`
6. Webhooks de status atualizam via `fetchByExternalWamId()`

### Webhook Status Tracking ✅
- **WhatsAppMessage** salva com `whatsapp_message_id` (external WAM ID)
- **Webhooks** encontram mensagens via `fetchByExternalWamId()`
- **Status Updates** funcionam independente de campanha
- **ProcessWebhookMessageStatusUpdate** atualiza Message e WhatsAppMessage

## Proposta: Endpoint de Envio em Tempo Real

### Objetivo
Criar endpoint que receba:
- `text`: Texto da mensagem
- `client_id`: ID do cliente que receberá
- `phone_number_id`: ID do número que enviará
- Retornar resposta da Meta API diretamente
- **Salvar Message com `campaign_id = null`**
- **Garantir tracking completo via WhatsAppMessage**

### Vantagens da Abordagem
1. **Reutilização Total**: Usar domínios e serviços existentes
2. **Consistência**: Mesmo fluxo de tracking e logs
3. **Flexibilidade**: Suporte a templates e mensagens diretas
4. **Webhook Compatibility**: Webhooks funcionam perfeitamente sem campanha
5. **Manutenibilidade**: Não duplicar lógica existente

## Implementation Plan

### 1. Endpoint Definition
```
POST /api/whatsapp/send-realtime-message
```

**Request Body:**
```json
{
    "text": "Olá {{client.name}}, sua mensagem personalizada!",
    "client_id": 123,
    "phone_number_id": 456,
    "template_id": null, // opcional - se fornecido, usa template
    "is_direct_message": true // opcional - default true para real-time
}
```

**Response:**
```json
{
    "success": true,
    "message": "Message sent successfully",
    "data": {
        "message_id": 789,
        "whatsapp_message_id": "wamid.xxx",
        "status": "sent",
        "meta_response": { /* resposta completa da Meta API */ }
    }
}
```

### 2. Implementation Structure

#### 2.1 Controller
```php
// app/Http/Controllers/WhatsApp/RealTimeMessageController.php

class RealTimeMessageController extends Controller
{
    use Response;

    public function send(SendRealTimeMessageRequest $request): JsonResponse
    {
        try {
            /** @var SendRealTimeMessage $useCase */
            $useCase = app()->make(SendRealTimeMessage::class);
            $result = $useCase->perform($request);
            
            return $this->response(
                "Message sent successfully",
                "success", 
                200,
                $result
            );
        } catch (\Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
    }
}
```

#### 2.2 Request Validation
```php
// app/Http/Requests/WhatsApp/SendRealTimeMessageRequest.php

class SendRealTimeMessageRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'text' => 'required|string|max:4096',
            'client_id' => 'required|integer|exists:clients,id',
            'phone_number_id' => 'required|integer|exists:phone_numbers,id',
            'template_id' => 'nullable|integer|exists:templates,id',
            'is_direct_message' => 'boolean'
        ];
    }

    public function authorize(): bool
    {
        return true; // Validação de organização será feita no use case
    }
}
```

#### 2.3 Use Case (Implementação Principal)
```php
// app/UseCases/WhatsApp/SendRealTimeMessage.php

class SendRealTimeMessage
{
    private MessageRepository $messageRepository;
    private ClientRepository $clientRepository;
    private PhoneNumberRepository $phoneNumberRepository;
    private TemplateRepository $templateRepository;
    private MessageFactory $messageFactory;

    public function __construct(
        MessageRepository $messageRepository,
        ClientRepository $clientRepository,
        PhoneNumberRepository $phoneNumberRepository,
        TemplateRepository $templateRepository,
        MessageFactory $messageFactory
    ) {
        $this->messageRepository = $messageRepository;
        $this->clientRepository = $clientRepository;
        $this->phoneNumberRepository = $phoneNumberRepository;
        $this->templateRepository = $templateRepository;
        $this->messageFactory = $messageFactory;
    }

    public function perform(SendRealTimeMessageRequest $request): array
    {
        DB::beginTransaction();
        
        try {
            // 1. Validar permissões
            $this->validatePermissions($request);
            
            // 2. Carregar entidades relacionadas
            $client = $this->getClient($request->client_id);
            $phoneNumber = $this->getPhoneNumber($request->phone_number_id);
            $template = $request->template_id ? $this->getTemplate($request->template_id) : null;
            
            // 3. Criar Message domain com campaign_id = null
            $message = $this->createMessage($request, $client, $template);
            
            // 4. Salvar Message no banco
            $savedMessage = $this->messageRepository->store($message);
            
            // 5. Enviar via MessageService (reutiliza código existente)
            /** @var MessageService $messageService */
            $messageService = app()->makeWith(MessageService::class, [
                'phoneNumber' => $phoneNumber,
            ]);
            
            $whatsappResponse = $messageService->send($savedMessage);
            
            // 6. SaveWhatsAppMessage já é chamado automaticamente pelo MessageService
            
            DB::commit();
            
            return [
                'message_id' => $savedMessage->id,
                'whatsapp_message_id' => $whatsappResponse['messages'][0]['id'] ?? null,
                'status' => 'sent',
                'meta_response' => $whatsappResponse
            ];
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }
    
    private function validatePermissions(SendRealTimeMessageRequest $request): void
    {
        $organizationId = auth()->user()->organization_id;
        
        // Validar que client pertence à organização
        $client = $this->clientRepository->fetchById($request->client_id);
        if (!$client || $client->organization_id !== $organizationId) {
            throw new \Exception("Client does not belong to your organization", 403);
        }
        
        // Validar que phone_number pertence à organização
        $phoneNumber = $this->phoneNumberRepository->fetchById($request->phone_number_id);
        if (!$phoneNumber || $phoneNumber->organization_id !== $organizationId) {
            throw new \Exception("Phone number does not belong to your organization", 403);
        }
        
        // Validar template se fornecido
        if ($request->template_id) {
            $template = $this->templateRepository->fetchById($request->template_id);
            if (!$template || $template->organization_id !== $organizationId) {
                throw new \Exception("Template does not belong to your organization", 403);
            }
        }
    }
    
    private function createMessage(
        SendRealTimeMessageRequest $request, 
        Client $client, 
        ?Template $template
    ): Message {
        return new Message(
            id: null,
            organization_id: auth()->user()->organization_id,
            campaign_id: null, // ✅ NULL para real-time messages
            template_id: $request->template_id,
            client_id: $request->client_id,
            message: $request->text,
            status: MessageStatus::is_sending,
            is_direct_message: $request->is_direct_message ?? true,
            client: $client,
            template: $template
        );
    }
    
    private function getClient(int $clientId): Client
    {
        $client = $this->clientRepository->fetchById($clientId);
        if (!$client) {
            throw new \Exception("Client not found", 404);
        }
        return $client;
    }
    
    private function getPhoneNumber(int $phoneNumberId): PhoneNumber
    {
        $phoneNumber = $this->phoneNumberRepository->fetchById($phoneNumberId);
        if (!$phoneNumber) {
            throw new \Exception("Phone number not found", 404);
        }
        return $phoneNumber;
    }
    
    private function getTemplate(int $templateId): Template
    {
        $template = $this->templateRepository->fetchById($templateId);
        if (!$template) {
            throw new \Exception("Template not found", 404);
        }
        return $template;
    }
}
```

### 3. Route Definition
```php
// routes/api.php
Route::middleware(['auth:sanctum'])->group(function () {
    Route::post('/whatsapp/send-realtime-message', [RealTimeMessageController::class, 'send']);
});
```

### 4. Webhook Compatibility ✅

**Fluxo de Webhook Status:**
1. Message salva com `campaign_id = null`
2. WhatsAppMessage salva com `whatsapp_message_id` (external WAM ID)
3. Webhook chega com status update
4. `fetchByExternalWamId()` encontra WhatsAppMessage
5. Status atualizado em Message e WhatsAppMessage
6. **Funciona perfeitamente sem campanha!**

## Principais Correções Implementadas

### ✅ 1. Não Usar Domínios Temporários
- Message salva no banco com `campaign_id = null`
- Permite referência completa para webhooks
- Mantém histórico e auditoria

### ✅ 2. Salvar WhatsAppMessage Automaticamente
- `MessageService::send()` já chama `SaveWhatsAppMessage`
- External WAM ID salvo automaticamente
- Tracking completo garantido

### ✅ 3. Webhook Compatibility Verificada
- `fetchByExternalWamId()` funciona independente de campanha
- `ProcessWebhookMessageStatusUpdate` atualiza Message e WhatsAppMessage
- Status tracking funciona perfeitamente

## Implementação e Testes

### Ordem de Implementação
1. **Implementar controller, use case e request**
2. **Testar integração com Meta API**
3. **Validar webhook tracking**
4. **Criar testes unitários e de feature**
5. **Documentar no Postman**

### Testes (Implementar após funcionalidade)
- Testes unitários do use case
- Testes de feature do controller
- Testes de integração com webhooks
- Testes de validação de permissões

## Postman Collection
```json
{
    "name": "WhatsApp Real-Time Messaging",
    "requests": [
        {
            "name": "Send Text Message",
            "method": "POST",
            "url": "{{base_url}}/api/whatsapp/send-realtime-message",
            "body": {
                "text": "Olá {{client.name}}!",
                "client_id": "{{client_id}}",
                "phone_number_id": "{{phone_number_id}}"
            }
        }
    ]
}
```

## Considerações de Segurança

- Validar que client pertence à organização do usuário
- Validar que phone_number pertence à organização
- Rate limiting para evitar spam
- Logs detalhados para auditoria
- Validação de templates publicados no WhatsApp
