# WhatsApp Real-Time Messages - Postman Collection

Esta coleção contém todos os endpoints e testes para a funcionalidade de envio de mensagens WhatsApp em tempo real.

## 📁 Arquivos

- `WhatsApp-RealTime-Messages.postman_collection.json` - Coleção principal com todos os endpoints
- `WhatsApp-RealTime-Environment.postman_environment.json` - Ambiente com variáveis pré-configuradas
- `README-WhatsApp-RealTime.md` - Este arquivo de documentação

## 🚀 Como Usar

### 1. Importar no Postman

1. Abra o Postman
2. Clique em "Import"
3. Selecione os arquivos:
   - `WhatsApp-RealTime-Messages.postman_collection.json`
   - `WhatsApp-RealTime-Environment.postman_environment.json`

### 2. Configurar Ambiente

1. Selecione o ambiente "WhatsApp Real-Time Environment"
2. Configure as variáveis necessárias:

```json
{
  "base_url": "http://localhost:8000",           // URL da sua API
  "auth_token": "seu_bearer_token_aqui",         // Token de autenticação
  "client_id": "1",                              // ID do cliente
  "phone_number_id": "1",                        // ID do número WhatsApp
  "template_id": "1"                             // ID do template (opcional)
}
```

### 3. Obter Token de Autenticação

Execute primeiro o endpoint de login para obter o `auth_token`:

```bash
POST /api/login
{
  "email": "<EMAIL>",
  "password": "sua_senha"
}
```

Copie o token da resposta e cole na variável `auth_token` do ambiente.

## 📋 Endpoints Disponíveis

### 🟢 Send Real-Time Message

#### 1. Send Text Message (Success)
- **Método**: `POST /api/message/send-realtime`
- **Descrição**: Envia mensagem de texto em tempo real
- **Payload**:
```json
{
    "text": "Olá {{client.name}}! Esta é uma mensagem em tempo real.",
    "client_id": 1,
    "phone_number_id": 1,
    "is_direct_message": true
}
```

#### 2. Send Message with Template
- **Método**: `POST /api/message/send-realtime`
- **Descrição**: Envia mensagem usando template específico
- **Payload**:
```json
{
    "text": "Mensagem usando template personalizado",
    "client_id": 1,
    "phone_number_id": 1,
    "template_id": 1,
    "is_direct_message": false
}
```

### 🔴 Validation Tests

#### 1. Missing Required Fields
- **Descrição**: Testa validação quando campos obrigatórios estão ausentes
- **Resposta Esperada**: `422 Unprocessable Entity`

#### 2. Text Too Long
- **Descrição**: Testa validação quando texto excede 4096 caracteres
- **Resposta Esperada**: `422 Unprocessable Entity`

#### 3. Invalid Client ID
- **Descrição**: Testa erro quando cliente não existe
- **Resposta Esperada**: `500 Internal Server Error`

## 📊 Estrutura de Resposta

### ✅ Sucesso (200)
```json
{
    "status": "success",
    "message": "Message sent successfully",
    "data": {
        "message_id": 123,
        "whatsapp_message_id": "wamid.HBgLNTU3OTkxNTE0OTU3FQIAEhggRDdBQjlCNzNCNzA4QzlGNEE4QjY4RjVGNzU4NzI1RTI",
        "status": "sent",
        "meta_response": {
            "messages": [
                {
                    "id": "wamid.HBgLNTU3OTkxNTE0OTU3FQIAEhggRDdBQjlCNzNCNzA4QzlGNEE4QjY4RjVGNzU4NzI1RTI"
                }
            ],
            "messaging_product": "whatsapp",
            "contacts": [
                {
                    "input": "+5511999999999",
                    "wa_id": "5511999999999"
                }
            ]
        }
    },
    "errors": null,
    "pagination": null
}
```

### ❌ Erro de Validação (422)
```json
{
    "message": "The given data was invalid.",
    "errors": {
        "text": ["The text field is required."],
        "client_id": ["The client id field is required."]
    }
}
```

### ❌ Erro de Negócio (500)
```json
{
    "status": "error",
    "message": "This client don't belong to this organization.",
    "data": [],
    "errors": null
}
```

## 🔧 Campos de Entrada

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `text` | string | ✅ | Texto da mensagem (máx. 4096 chars) |
| `client_id` | integer | ✅ | ID do cliente destinatário |
| `phone_number_id` | integer | ✅ | ID do número WhatsApp remetente |
| `template_id` | integer | ❌ | ID do template (opcional) |
| `is_direct_message` | boolean | ❌ | Se é mensagem direta (padrão: true) |

## 🎯 Substituição de Variáveis

O sistema suporta substituição de variáveis no formato `{{model.field}}`:

- `{{client.name}}` - Nome do cliente
- `{{client.email}}` - Email do cliente
- `{{client.phone}}` - Telefone do cliente
- E outros campos do modelo Client

## 🧪 Testes Automatizados

Cada endpoint inclui testes automatizados que verificam:

- ✅ Status code correto
- ✅ Estrutura da resposta
- ✅ Presença de campos obrigatórios
- ✅ Tipos de dados corretos
- ✅ Armazenamento de variáveis para próximos testes

## 🔐 Autenticação

Todos os endpoints requerem autenticação Bearer Token:

```
Authorization: Bearer {seu_token_aqui}
```

## 📝 Notas Importantes

1. **Organização**: Todos os recursos (client, phone_number, template) devem pertencer à organização do usuário autenticado
2. **Templates**: Se `template_id` for fornecido, o template deve estar publicado no WhatsApp
3. **Mensagens Diretas**: `is_direct_message: true` cria mensagens sem campanha (`campaign_id = null`)
4. **Webhooks**: Mensagens em tempo real são compatíveis com o sistema de webhooks existente
5. **Tracking**: WhatsAppMessage é criado automaticamente para rastreamento

## 🐛 Troubleshooting

### Erro 401 - Unauthorized
- Verifique se o `auth_token` está correto e não expirou

### Erro 422 - Validation Error
- Verifique se todos os campos obrigatórios estão presentes
- Verifique se o texto não excede 4096 caracteres

### Erro 500 - Client/Phone/Template não pertence à organização
- Verifique se os IDs fornecidos existem e pertencem à sua organização

### Erro 500 - Template não publicado
- Verifique se o template está aprovado e publicado no WhatsApp Business API
