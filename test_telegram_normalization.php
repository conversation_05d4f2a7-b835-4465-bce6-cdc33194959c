<?php

/**
 * Script de teste para validar a normalização dos dados do Telegram
 * Execute este script para testar a limpeza dos dados antes de implementar em produção
 */

// Simular os dados reais que você forneceu

// Dados do banco (funcionando)
$databaseData = '"{\"paciente\":\"<PERSON>\",\"data_nascimento\":\"20\\\/09\\\/1973\",\"idade\":\"51 anos\",\"sexo\":\"Masculino\",\"convenio\":\"Bradesco Sa\\u00fade\",\"codigo_usuario\":\"775263019593002\",\"atendimento\":\"4519301\",\"prontuario\":\"301618\",\"data_entrada\":\"26\\\/11\\\/2024 07:45\",\"setor\":\"HP-Centro Cir\\u00fargico\",\"cirurgia_realizada\":\"Herniorrafia Inguinal - Unilateral Por\",\"cirurgiao\":\"<PERSON>\",\"crm_cirurgiao\":\"5473\",\"anestesista\":\"Rafaela Nunes Dantas\",\"tipo_anestesia\":\"Geral\",\"inicio_cirurgia\":\"26\\\/11\\\/2024 08:30\",\"fim_cirurgia\":\"\",\"classificacao\":\"Limpa\",\"tipo_cirurgia\":\"Eletiva\",\"antibioticoprofilaxia\":\"\",\"procedimentos\":[{\"codigo\":\"31009336\",\"descricao\":\"Herniorrafia Inguinal Unilateral por Videolaparoscopia\"}],\"equipe_cirurgica\":{\"cirurgiao_principal\":\"Rafael Moraes Tavares\",\"primeiro_auxiliar\":\"Leandro Cavalcanti de Albuquerque Leite Barros\"},\"diagnostico_pre_operatorio\":\"h\\u00e9rnia inguinal esquerda h\\u00e9mia umbilical Diagn\\u00f3stico P\\u00f3s-Operat\\u00f3rio h\\u00e9rnia inguinal esquerda\",\"diagnostico_pos_operatorio\":\"\",\"descricao_ato_cirurgico\":\"\",\"materiais\":[{\"item\":\"Materials: SF 500ml\",\"quantidade\":\"01\"},{\"item\":\"Novabupi c\\\/v\",\"quantidade\":\"01\"},{\"item\":\"clip 300\",\"quantidade\":\"01\"},{\"item\":\"Pds\",\"quantidade\":\"001\"},{\"item\":\"OPME: Trocater 10mm descart\\u00e1vel\",\"quantidade\":\"01\"}],\"opme\":[\"Trocater 10mm descart\\u00e1vel 01 unidade\"]}"';

// Dados do Telegram (com erro)
$telegramData = '{"raw_data":"{\\n\\u00a0\\u00a0\\u00a0 \\\"paciente\\\": \\\"Carlos Jose Gomes da Silva\\\",\\n\\u00a0\\u00a0\\u00a0 \\\"data_nascimento\\\": \\\"20\\\/09\\\/1973\\\",\\n\\u00a0\\u00a0\\u00a0 \\\"idade\\\": \\\"51 anos\\\",\\n\\u00a0\\u00a0\\u00a0 \\\"sexo\\\": \\\"Masculino\\\",\\n\\u00a0\\u00a0\\u00a0 \\\"convenio\\\": \\\"Bradesco Sa\\u00fade\\\",\\n\\u00a0\\u00a0\\u00a0 \\\"codigo_usuario\\\": \\\"775263019593002\\\",\\n\\u00a0\\u00a0\\u00a0 \\\"atendimento\\\": \\\"4519301\\\",\\n\\u00a0\\u00a0\\u00a0 \\\"prontuario\\\": \\\"301618\\\",\\n\\u00a0\\u00a0\\u00a0 \\\"data_entrada\\\": \\\"26\\\/11\\\/2024 07:45\\\",\\n\\u00a0\\u00a0\\u00a0 \\\"setor\\\": \\\"HP-Centro Cir\\u00fargico\\\",\\n\\u00a0\\u00a0\\u00a0 \\\"cirurgia_realizada\\\": \\\"Herniorrafia Inguinal - Unilateral Por\\\",\\n\\u00a0\\u00a0\\u00a0 \\\"cirurgiao\\\": \\\"Rafael Moraes Tavares\\\",\\n\\u00a0\\u00a0\\u00a0 \\\"crm_cirurgiao\\\": \\\"5473\\\",\\n\\u00a0\\u00a0\\u00a0 \\\"anestesista\\\": \\\"Rafaela Nunes Dantas\\\",\\n\\u00a0\\u00a0\\u00a0 \\\"tipo_anestesia\\\": \\\"Geral\\\",\\n\\u00a0\\u00a0\\u00a0 \\\"inicio_cirurgia\\\": \\\"26\\\/11\\\/2024 08:30\\\",\\n\\u00a0\\u00a0\\u00a0 \\\"fim_cirurgia\\\": \\\"\\\",\\n\\u00a0\\u00a0\\u00a0 \\\"classificacao\\\": \\\"Limpa\\\",\\n\\u00a0\\u00a0\\u00a0 \\\"tipo_cirurgia\\\": \\\"Eletiva\\\",\\n\\u00a0\\u00a0\\u00a0 \\\"antibioticoprofilaxia\\\": \\\"\\\",\\n\\u00a0\\u00a0\\u00a0 \\\"procedimentos\\\": [\\n\\u00a0\\u00a0\\u00a0\\u00a0\\u00a0\\u00a0\\u00a0 {\\n\\u00a0\\u00a0\\u00a0\\u00a0\\u00a0\\u00a0\\u00a0\\u00a0\\u00a0\\u00a0\\u00a0 \\\"codigo\\\": \\\"31009336\\\",\\n\\u00a0\\u00a0\\u00a0\\u00a0\\u00a0\\u00a0\\u00a0\\u00a0\\u00a0\\u00a0\\u00a0 \\\"descricao\\\": \\\"Herniorrafia Inguinal Unilateral por Videolaparoscopia\\\"\\n\\u00a0\\u00a0\\u00a0\\u00a0\\u00a0\\u00a0\\u00a0 }\\n\\u00a0\\u00a0\\u00a0 ],\\n\\u00a0\\u00a0\\u00a0 \\\"equipe_cirurgica\\\": {\\n\\u00a0\\u00a0\\u00a0\\u00a0\\u00a0\\u00a0\\u00a0 \\\"cirurgiao_principal\\\": \\\"Rafael Moraes Tavares\\\",\\n\\u00a0\\u00a0\\u00a0\\u00a0\\u00a0\\u00a0\\u00a0 \\\"primeiro_auxiliar\\\": \\\"Leandro Cavalcanti de Albuquerque Leite Barros\\\"\\n\\u00a0\\u00a0\\u00a0 },\\n\\u00a0\\u00a0\\u00a0 \\\"diagnostico_pre_operatorio\\\": \\\"h\\u00e9rnia inguinal esquerda h\\u00e9mia umbilical Diagn\\u00f3stico P\\u00f3s-Operat\\u00f3rio h\\u00e9rnia inguinal esquerda\\\",\\n\\u00a0\\u00a0\\u00a0 \\\"diagnostico_pos_operatorio\\\": \\\"\\\",\\n\\u00a0\\u00a0\\u00a0 \\\"descricao_ato_cirurgico\\\": \\\"\\\",\\n\\u00a0\\u00a0\\u00a0 \\\"materiais\\\": [\\n\\u00a0\\u00a0\\u00a0\\u00a0\\u00a0\\u00a0\\u00a0 {\\n\\u00a0\\u00a0\\u00a0\\u00a0\\u00a0\\u00a0\\u00a0\\u00a0\\u00a0\\u00a0\\u00a0 \\\"item\\\": \\\"Materials: SF 500ml\\\",\\n\\u00a0\\u00a0\\u00a0\\u00a0\\u00a0\\u00a0\\u00a0\\u00a0\\u00a0\\u00a0\\u00a0 \\\"quantidade\\\": \\\"01\\\"\\n\\u00a0\\u00a0\\u00a0\\u00a0\\u00a0\\u00a0\\u00a0 },\\n\\u00a0\\u00a0\\u00a0\\u00a0\\u00a0\\u00a0\\u00a0 {\\n\\u00a0\\u00a0\\u00a0\\u00a0\\u00a0\\u00a0\\u00a0\\u00a0\\u00a0\\u00a0\\u00a0 \\\"item\\\": \\\"Novabupi c\\\/v\\\",\\n\\u00a0\\u00a0\\u00a0\\u00a0\\u00a0\\u00a0\\u00a0\\u00a0\\u00a0\\u00a0\\u00a0 \\\"quantidade\\\": \\\"01\\\"\\n\\u00a0\\u00a0\\u00a0\\u00a0\\u00a0\\u00a0\\u00a0 },\\n\\u00a0\\u00a0\\u00a0\\u00a0\\u00a0\\u00a0\\u00a0 {\\n\\u00a0\\u00a0\\u00a0\\u00a0\\u00a0\\u00a0\\u00a0\\u00a0\\u00a0\\u00a0\\u00a0 \\\"item\\\": \\\"clip 300\\\",\\n\\u00a0\\u00a0\\u00a0\\u00a0\\u00a0\\u00a0\\u00a0\\u00a0\\u00a0\\u00a0\\u00a0 \\\"quantidade\\\": \\\"01\\\"\\n\\u00a0\\u00a0\\u00a0\\u00a0\\u00a0\\u00a0\\u00a0 },\\n\\u00a0\\u00a0\\u00a0\\u00a0\\u00a0\\u00a0\\u00a0 {\\n\\u00a0\\u00a0\\u00a0\\u00a0\\u00a0\\u00a0\\u00a0\\u00a0\\u00a0\\u00a0\\u00a0 \\\"item\\\": \\\"Pds\\\",\\n\\u00a0\\u00a0\\u00a0\\u00a0\\u00a0\\u00a0\\u00a0\\u00a0\\u00a0\\u00a0\\u00a0 \\\"quantidade\\\": \\\"001\\\"\\n\\u00a0\\u00a0\\u00a0\\u00a0\\u00a0\\u00a0\\u00a0 },\\n\\u00a0\\u00a0\\u00a0\\u00a0\\u00a0\\u00a0\\u00a0 {\\n\\u00a0\\u00a0\\u00a0\\u00a0\\u00a0\\u00a0\\u00a0\\u00a0\\u00a0\\u00a0\\u00a0 \\\"item\\\": \\\"OPME: Trocater 10mm descart\\u00e1vel\\\",\\n\\u00a0\\u00a0\\u00a0\\u00a0\\u00a0\\u00a0\\u00a0\\u00a0\\u00a0\\u00a0\\u00a0 \\\"quantidade\\\": \\\"01\\\"\\n\\u00a0\\u00a0\\u00a0\\u00a0\\u00a0\\u00a0\\u00a0 }\\n\\u00a0\\u00a0\\u00a0 ],\\n\\u00a0\\u00a0\\u00a0 \\\"opme\\\": [\\n\\u00a0\\u00a0\\u00a0\\u00a0\\u00a0\\u00a0\\u00a0 \\\"Trocater 10mm descart\\u00e1vel 10 unidades\\\"\\n\\u00a0\\u00a0\\u00a0 ]\\n}"}';

echo "=== TESTE DE NORMALIZAÇÃO DOS DADOS ===\n\n";

// Função para simular a limpeza
function cleanTelegramData($data) {
    // Detectar se é formato Telegram com raw_data
    if (strpos($data, '"raw_data"') !== false) {
        $decoded = json_decode($data, true);
        if (isset($decoded['raw_data'])) {
            $rawData = $decoded['raw_data'];
            
            // Limpar os dados
            $cleaned = $rawData;
            
            // Remover quebras de linha e espaços não-quebráveis
            $cleaned = str_replace(['\n', '\\n', '\r', '\\r'], '', $cleaned);
            $cleaned = str_replace(['\u00a0', '\\u00a0'], '', $cleaned);
            
            // Remover espaços extras
            $cleaned = preg_replace('/\s+/', '', $cleaned);
            
            // Corrigir escape characters
            $cleaned = str_replace('\\"', '"', $cleaned);
            $cleaned = str_replace('\\/', '/', $cleaned);
            
            // Tentar decodificar e recodificar
            $innerDecoded = json_decode($cleaned, true);
            if (json_last_error() === JSON_ERROR_NONE && is_array($innerDecoded)) {
                // Recodificar no mesmo formato do banco
                $normalized = json_encode($innerDecoded, JSON_UNESCAPED_SLASHES);
                $normalized = addslashes($normalized);
                return $normalized;
            }
        }
    }
    
    return $data;
}

// Testar dados do banco
echo "1. TESTANDO DADOS DO BANCO (que funcionam):\n";
echo "Dados originais (primeiros 200 chars): " . substr($databaseData, 0, 200) . "...\n";

$bankDecoded = json_decode(trim($databaseData, '"'), true);
if (json_last_error() === JSON_ERROR_NONE) {
    echo "✅ Dados do banco decodificam corretamente\n";
    echo "Paciente: " . $bankDecoded['paciente'] . "\n";
    echo "Data nascimento: " . $bankDecoded['data_nascimento'] . "\n";
} else {
    echo "❌ Erro ao decodificar dados do banco: " . json_last_error_msg() . "\n";
}

echo "\n" . str_repeat("-", 80) . "\n\n";

// Testar dados do Telegram
echo "2. TESTANDO DADOS DO TELEGRAM (com problema):\n";
echo "Dados originais (primeiros 200 chars): " . substr($telegramData, 0, 200) . "...\n";

$telegramDecoded = json_decode($telegramData, true);
if (json_last_error() === JSON_ERROR_NONE && isset($telegramDecoded['raw_data'])) {
    echo "✅ Estrutura externa do Telegram decodifica corretamente\n";
    echo "Tentando decodificar raw_data...\n";
    
    $rawData = $telegramDecoded['raw_data'];
    echo "Raw data (primeiros 200 chars): " . substr($rawData, 0, 200) . "...\n";
    
    $rawDecoded = json_decode($rawData, true);
    if (json_last_error() === JSON_ERROR_NONE) {
        echo "❌ Raw data não deveria decodificar diretamente, mas decodificou\n";
    } else {
        echo "⚠️  Raw data não decodifica diretamente (esperado): " . json_last_error_msg() . "\n";
    }
} else {
    echo "❌ Erro ao decodificar estrutura externa do Telegram: " . json_last_error_msg() . "\n";
}

echo "\n" . str_repeat("-", 80) . "\n\n";

// Testar normalização
echo "3. TESTANDO NORMALIZAÇÃO:\n";
$normalizedTelegram = cleanTelegramData($telegramData);
echo "Dados normalizados (primeiros 200 chars): " . substr($normalizedTelegram, 0, 200) . "...\n";

$normalizedDecoded = json_decode(trim($normalizedTelegram, '"'), true);
if (json_last_error() === JSON_ERROR_NONE) {
    echo "✅ Dados normalizados decodificam corretamente\n";
    echo "Paciente: " . $normalizedDecoded['paciente'] . "\n";
    echo "Data nascimento: " . $normalizedDecoded['data_nascimento'] . "\n";
    echo "Convênio: " . $normalizedDecoded['convenio'] . "\n";
    
    // Verificar se tem a mesma estrutura do banco
    $bankKeys = array_keys($bankDecoded);
    $telegramKeys = array_keys($normalizedDecoded);
    
    $missingKeys = array_diff($bankKeys, $telegramKeys);
    $extraKeys = array_diff($telegramKeys, $bankKeys);
    
    if (empty($missingKeys) && empty($extraKeys)) {
        echo "✅ Estrutura idêntica aos dados do banco\n";
    } else {
        echo "⚠️  Diferenças na estrutura:\n";
        if (!empty($missingKeys)) {
            echo "   Chaves faltando: " . implode(', ', $missingKeys) . "\n";
        }
        if (!empty($extraKeys)) {
            echo "   Chaves extras: " . implode(', ', $extraKeys) . "\n";
        }
    }
} else {
    echo "❌ Erro ao decodificar dados normalizados: " . json_last_error_msg() . "\n";
}

echo "\n" . str_repeat("=", 80) . "\n";
echo "TESTE CONCLUÍDO\n";
