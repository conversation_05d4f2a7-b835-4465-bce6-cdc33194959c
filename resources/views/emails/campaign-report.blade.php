@extends('emails.layout')

@section('title', 'Relatório de Campanha - ' . $campaign->name)

@section('header', 'Relatório de Campanha')

@section('content')
    <h2>{{ $campaign->name }}</h2>
    
    <p><strong>Período:</strong> {{ $period }}</p>
    <p><strong>Organização:</strong> {{ $organization->name }}</p>
    <p><strong>Gerado em:</strong> {{ $generated_at->format('d/m/Y H:i:s') }}</p>

    <div class="alert">
        <p><strong>📊 Resumo da Campanha</strong></p>
        <p><PERSON><PERSON><PERSON> abaixo as métricas detalhadas de performance da sua campanha.</p>
    </div>

    <table class="table">
        <thead>
            <tr>
                <th>Métrica</th>
                <th>Valor</th>
            </tr>
        </thead>
        <tbody>
            @foreach($metrics as $metric => $value)
            <tr>
                <td>{{ $metric }}</td>
                <td><strong>{{ $value }}</strong></td>
            </tr>
            @endforeach
        </tbody>
    </table>

    @if(isset($campaign->description) && $campaign->description)
    <div style="margin-top: 30px;">
        <h3>Descrição da Campanha</h3>
        <p>{{ $campaign->description }}</p>
    </div>
    @endif

    <div style="margin-top: 30px;">
        <h3>Próximos Passos</h3>
        <ul>
            <li>Analise as métricas de performance</li>
            <li>Identifique oportunidades de melhoria</li>
            <li>Considere ajustes para futuras campanhas</li>
            <li>Acompanhe o engajamento dos clientes</li>
        </ul>
    </div>

    <div class="text-center" style="margin-top: 30px;">
        <a href="{{ url('/campaigns/' . $campaign->id) }}" class="button">
            Ver Campanha Completa
        </a>
    </div>
@endsection

@section('footer')
    <p>Este relatório foi gerado automaticamente pelo sistema Obvio.</p>
    <p>Para mais informações ou suporte, entre em contato conosco.</p>
@endsection
