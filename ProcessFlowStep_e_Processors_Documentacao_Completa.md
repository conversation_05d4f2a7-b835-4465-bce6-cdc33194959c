# Documentação Completa: ProcessFlowStep e Processors

## Visão Geral

O sistema de ChatBot do WhatsApp utiliza uma arquitetura baseada em **Strategy Pattern** para processar diferentes tipos de steps (passos) em um fluxo de conversação. O componente central é o `ProcessFlowStep` que coordena todo o processamento, utilizando diferentes **Processors** especializados para cada tipo de step.

## Arquitetura Principal

### 1. ProcessFlowStep (Use Case Principal)

**Localização**: `app/Services/Meta/WhatsApp/ChatBot/UseCases/ProcessFlowStep.php`

O `ProcessFlowStep` é o **orquestrador principal** que:

1. **Recebe** uma interação do usuário (webhook do WhatsApp)
2. **Identifica** o step atual da conversa
3. **Delega** o processamento para o processor apropriado
4. **Aplica** navegação condicional se necessário
5. **Retorna** o resultado final com próximo step

#### Fluxo de Execução:

```php
public function perform(Conversation $conversation, array $messageData, Flow $flow): array
{
    // 1. Salva a interação
    $interaction = $this->interactionRepository->save(
        $this->interactionFactory->buildFromWebhookData($messageData, $conversation)
    );

    // 2. Obtém o step atual
    $currentStep = $conversation->current_step;

    // 3. Processa o step usando Strategy Pattern
    $stepResult = $this->processStepByType($currentStep, $interaction, $conversation);

    // 4. Aplica navegação condicional
    $stepResult = $this->applyConditionalNavigation($stepResult, $currentStep, $interaction);

    // 5. Salva resultado e retorna
    $interaction->result = json_encode($stepResult);
    $this->interactionRepository->save($interaction);
    
    return $stepResult;
}
```

### 2. processStepByType (Strategy Pattern)

**Método central** que implementa o Strategy Pattern:

```php
protected function processStepByType(Step $step, Interaction $interaction, Conversation $conversation): array
{
    try {
        // Obtém o processor apropriado via Factory
        $processor = $this->stepProcessorFactory->getProcessor($step);
        
        // Delega o processamento
        return $processor->process($step, $interaction, $conversation);
    } catch (\InvalidArgumentException $e) {
        // Fallback para processamento padrão
        return $this->processDefaultStep($step);
    }
}
```

### 3. applyConditionalNavigation (Navegação Inteligente)

**Método CRÍTICO** para navegação entre steps:

```php
protected function applyConditionalNavigation(array $stepResult, Step $currentStep, Interaction $interaction): array
{
    // Só aplica se deve mover para próximo step
    if (!($stepResult['move_to_next'] ?? false)) {
        return $stepResult;
    }

    // Verifica se step tem navegação condicional
    if (!$this->conditionalNavigationService->hasConditionalNavigation($currentStep)) {
        return $stepResult;
    }

    // Obtém próximo step baseado na interação do usuário
    $conditionalNextStep = $this->conditionalNavigationService->getNextStepForInteraction($currentStep, $interaction);

    if ($conditionalNextStep) {
        // Sobrescreve o próximo step com o target condicional
        $stepResult['next_step'] = $conditionalNextStep->id;
        $stepResult['conditional_navigation'] = true;
        $stepResult['target_step_identifier'] = $conditionalNextStep->step;
    }

    return $stepResult;
}
```

## StepProcessorFactory (Factory Pattern)

**Localização**: `app/Services/Meta/WhatsApp/ChatBot/Processors/StepProcessorFactory.php`

### Responsabilidades:

1. **Mapeia** tipos de step para seus processors
2. **Instancia** processors sob demanda
3. **Valida** se um processor pode processar um step
4. **Gerencia** registro de processors customizados

### Mapeamento de Processors:

```php
private array $processors = [
    StepType::MESSAGE->value => MessageStepProcessor::class,
    StepType::INTERACTIVE->value => InteractiveStepProcessor::class,
    StepType::INPUT->value => InputStepProcessor::class,
    StepType::COMMAND->value => CommandStepProcessor::class,
    StepType::CONDITION->value => ConditionStepProcessor::class,
    StepType::WEBHOOK->value => WebhookStepProcessor::class,
    StepType::DELAY->value => DelayStepProcessor::class,
];
```

### Método Principal:

```php
public function getProcessor(Step $step): StepProcessorInterface
{
    // Garante que step_type está definido
    $step->setStepTypeFromLegacyFields();
    
    if (!$step->step_type) {
        throw new \InvalidArgumentException("Step {$step->id} has no step_type defined");
    }
    
    return $this->getProcessorByType($step->step_type->value);
}
```

## Tipos de Step (StepType Enum)

**Localização**: `app/Enums/ChatBot/StepType.php`

### 7 Tipos Disponíveis:

1. **MESSAGE** - Exibir mensagem e avançar automaticamente
2. **INTERACTIVE** - Mostrar botões/listas e aguardar seleção
3. **INPUT** - Solicitar entrada de texto, validar e armazenar
4. **COMMAND** - Executar lógica de negócio (pedidos, cálculos)
5. **CONDITION** - Navegação condicional baseada na entrada do usuário
6. **WEBHOOK** - Chamar APIs ou serviços externos
7. **DELAY** - Aguardar tempo especificado antes de continuar

### Características dos Tipos:

```php
public function requiresUserInteraction(): bool
{
    return match($this) {
        self::INTERACTIVE, self::INPUT => true,
        self::MESSAGE, self::COMMAND, self::CONDITION, self::WEBHOOK, self::DELAY => false,
    };
}

public function canAutoAdvance(): bool
{
    return match($this) {
        self::MESSAGE, self::COMMAND, self::WEBHOOK, self::DELAY => true,
        self::INTERACTIVE, self::INPUT, self::CONDITION => false,
    };
}
```

## Processors Detalhados

### 1. MessageStepProcessor

**Propósito**: Processa steps de mensagem simples que avançam automaticamente.

**Comportamento**:
- Exibe mensagem ao usuário
- Avança automaticamente para próximo step
- Não aguarda interação do usuário

**Resultado**:
```php
return [
    'type' => 'message',
    'step_id' => $step->id,
    'action' => 'send_message',
    'message' => $message,
    'next_step' => $step->next_step,
    'move_to_next' => true, // Avança automaticamente
];
```

### 2. InteractiveStepProcessor

**Propósito**: Processa steps com botões ou listas interativas.

**Comportamento**:
- Mostra opções (botões/listas) ao usuário
- Aguarda seleção do usuário
- Processa a seleção quando recebida

**Resultado**:
```php
return [
    'type' => 'interactive',
    'step_id' => $step->id,
    'action' => 'show_options',
    'message' => $message,
    'next_step' => $step->next_step,
    'move_to_next' => true, // Após seleção
];
```

### 3. InputStepProcessor

**Propósito**: Coleta entrada de texto do usuário.

**Comportamento**:
- Solicita entrada de texto
- Valida entrada recebida
- Atualiza objetos de domínio via DynamicInputService
- Usa padrão `client.field` para atualizar dados

**Resultado (Solicitando)**:
```php
return [
    'type' => 'input',
    'step_id' => $step->id,
    'action' => 'request_input',
    'message' => $processedMessage,
    'move_to_next' => false, // Aguarda entrada
];
```

**Resultado (Processando)**:
```php
return [
    'type' => 'input',
    'step_id' => $step->id,
    'action' => 'input_processed',
    'input_result' => $inputResult,
    'next_step' => $step->next_step,
    'move_to_next' => true, // Após processar
];
```

### 4. CommandStepProcessor

**Propósito**: Executa lógica de negócio complexa.

**Comportamento**:
- Executa comandos via ExecuteCommand UseCase
- Pode atualizar dados do cliente
- Pode processar pedidos
- Pode fazer cálculos

**Resultado (Sucesso)**:
```php
return [
    'type' => 'command',
    'step_id' => $step->id,
    'action' => 'command_executed',
    'result' => $commandResult,
    'next_step' => $step->next_step,
    'move_to_next' => true,
    'success' => true,
];
```

**Resultado (Falha)**:
```php
return [
    'type' => 'command',
    'step_id' => $step->id,
    'action' => 'command_failed',
    'result' => ['success' => false, 'error' => $e->getMessage()],
    'next_step' => $step->next_step,
    'move_to_next' => false, // Não avança em falha
    'success' => false,
];
```

### 5. ConditionStepProcessor

**Propósito**: Processa steps com navegação condicional.

**Comportamento**:
- Exibe mensagem/botões condicionais
- Permite navegação baseada em regras
- **CRÍTICO**: `move_to_next => true` para permitir navegação condicional
- Trabalha em conjunto com ConditionalNavigationService

**Resultado**:
```php
return [
    'type' => 'condition',
    'step_id' => $step->id,
    'action' => 'show_condition_message',
    'message' => $message,
    'next_step' => $step->next_step, // Fallback padrão
    'move_to_next' => true, // CRÍTICO: Permite navegação condicional
    'has_buttons' => $this->stepHasButtons($step),
    'has_navigation_rules' => !empty($step->navigation_rules),
];
```

### 6. WebhookStepProcessor

**Propósito**: Faz chamadas HTTP para APIs externas.

**Comportamento**:
- Obtém configuração do webhook do step
- Faz chamada HTTP para endpoint externo
- Processa resposta
- Pode atualizar estado da conversa

**Resultado (Sucesso)**:
```php
return [
    'type' => 'webhook',
    'step_id' => $step->id,
    'action' => 'webhook_executed',
    'webhook_response' => $response,
    'next_step' => $step->next_step,
    'move_to_next' => true,
    'success' => true,
];
```

**Resultado (Falha)**:
```php
return [
    'type' => 'webhook',
    'step_id' => $step->id,
    'action' => 'webhook_failed',
    'error' => $e->getMessage(),
    'next_step' => $step->next_step,
    'move_to_next' => false, // Não avança em falha
    'success' => false,
];
```

### 7. DelayStepProcessor

**Propósito**: Adiciona delays temporais no fluxo.

**Comportamento**:
- Obtém duração do delay da configuração JSON
- Agenda processamento futuro
- Avança automaticamente após delay

**Configuração JSON**:
```json
{
    "duration_seconds": 30,
    "message": "Aguarde um momento..."
}
```

**Resultado**:
```php
return [
    'type' => 'delay',
    'step_id' => $step->id,
    'action' => 'delay_scheduled',
    'delay_seconds' => $delayConfig['duration_seconds'],
    'next_step' => $step->next_step,
    'move_to_next' => true, // Após delay
    'scheduled_at' => now()->addSeconds($delayConfig['duration_seconds']),
];
```

## Tipos de Botão (WhatsAppButtonType)

**Localização**: `app/Enums/ChatBot/WhatsAppButtonType.php`

### 5 Tipos Disponíveis:

1. **REPLY** - Botão de resposta rápida (máximo 3 por mensagem)
2. **URL** - Botão que abre URL no navegador
3. **PHONE_NUMBER** - Botão que inicia chamada telefônica
4. **COPY_CODE** - Botão que copia código para área de transferência
5. **FLOW** - Botão que aciona WhatsApp Flow

### Características dos Botões:

```php
public function getMaxAllowedButtons(): int
{
    return match($this) {
        self::REPLY => 3,
        self::URL, self::PHONE_NUMBER, self::COPY_CODE, self::FLOW => 1,
    };
}

public function getMaxTitleLength(): int
{
    return match($this) {
        self::REPLY, self::URL, self::PHONE_NUMBER => 20,
        self::COPY_CODE, self::FLOW => 25,
    };
}
```

### Payloads WhatsApp:

**REPLY Button**:
```php
[
    'type' => 'reply',
    'reply' => [
        'id' => $data['id'],
        'title' => $data['title']
    ]
]
```

**URL Button**:
```php
[
    'type' => 'url',
    'url' => $data['url'],
    'text' => $data['title']
]
```

**PHONE_NUMBER Button**:
```php
[
    'type' => 'phone_number',
    'phone_number' => $data['phone_number'],
    'text' => $data['title']
]
```

**COPY_CODE Button**:
```php
[
    'type' => 'copy_code',
    'copy_code' => $data['copy_code'],
    'text' => $data['title']
]
```

**FLOW Button**:
```php
[
    'type' => 'flow',
    'flow_token' => $data['flow_token'],
    'text' => $data['title']
]
```

## Button Domain (Domínio de Botão)

**Localização**: `app/Domains/ChatBot/Button.php`

### Métodos Principais:

#### 1. extractInternalDataFromButtonArray()

**Propósito**: Extrai dados internos baseado no tipo do botão.

```php
public static function extractInternalDataFromButtonArray(array $buttonArray): ?string
{
    $type = strtolower($buttonArray['type'] ?? '');

    return match($type) {
        'url' => $buttonArray['url'] ?? null,
        'phone_number' => $buttonArray['phone_number'] ?? null,
        'copy_code' => $buttonArray['copy_code'] ?? null,
        'flow' => $buttonArray['flow_token'] ?? null,
        'reply' => null, // Reply buttons não têm internal_data
        default => null,
    };
}
```

#### 2. getWhatsAppButtonType()

**Propósito**: Converte tipo string para enum WhatsAppButtonType.

```php
public function getWhatsAppButtonType(): ?WhatsAppButtonType
{
    $normalizedType = strtolower($this->type);

    return match($normalizedType) {
        'reply' => WhatsAppButtonType::REPLY,
        'url' => WhatsAppButtonType::URL,
        'phone_number' => WhatsAppButtonType::PHONE_NUMBER,
        'copy_code' => WhatsAppButtonType::COPY_CODE,
        'flow' => WhatsAppButtonType::FLOW,
        default => null,
    };
}
```

#### 3. toWhatsAppPayload()

**Propósito**: Gera payload para WhatsApp Business API.

```php
public function toWhatsAppPayload(): array
{
    $buttonType = $this->getWhatsAppButtonType();

    if (!$buttonType) {
        // Fallback para JSON customizado
        return json_decode($this->json ?? '{}', true) ?: [];
    }

    $data = [
        'id' => (string) $this->id,
        'title' => $this->text,
        'url' => $this->internal_data,
        'phone_number' => $this->internal_data,
        'copy_code' => $this->internal_data,
        'flow_token' => $this->internal_data,
    ];

    return $buttonType->toWhatsAppPayload($data);
}
```

#### 4. validateForWhatsApp()

**Propósito**: Valida botão para conformidade com WhatsApp.

```php
public function validateForWhatsApp(): bool
{
    $buttonType = $this->getWhatsAppButtonType();

    if (!$buttonType) {
        return false; // Tipo inválido
    }

    // Valida comprimento do título
    if (!$this->validateTitleLength()) {
        return false;
    }

    // Validações específicas por tipo
    return match($buttonType) {
        WhatsAppButtonType::URL => $this->validateUrl(),
        WhatsAppButtonType::PHONE_NUMBER => $this->validatePhoneNumber(),
        WhatsAppButtonType::COPY_CODE => $this->validateCopyCode(),
        WhatsAppButtonType::FLOW => $this->validateFlowToken(),
        WhatsAppButtonType::REPLY => true, // Reply sempre válido
    };
}
```

## Navegação de Fluxo

### 1. ConditionalNavigationService

**Localização**: `app/Services/Meta/WhatsApp/ChatBot/Services/ConditionalNavigationService.php`

#### Responsabilidades:

1. **Determina** próximo step baseado em interação do usuário
2. **Processa** regras de navegação JSON
3. **Gerencia** navegação baseada em botões
4. **Resolve** identificadores de step

#### Método Principal:

```php
public function getNextStepForInteraction(Step $currentStep, Interaction $interaction): ?Step
{
    $userInput = $interaction->getTextContent() ?? '';
    $buttonId = $interaction->getButtonId();

    $context = [
        'button_id' => $buttonId,
        'conversation' => $interaction->conversation,
        'interaction' => $interaction,
    ];

    return $this->flowNavigationService->getNextStep($currentStep, $userInput, $context);
}
```

### 2. FlowNavigationService

**Localização**: `app/Services/Meta/WhatsApp/ChatBot/Services/FlowNavigationService.php`

#### Hierarquia de Navegação:

1. **Regras de Navegação JSON** (prioridade máxima)
2. **Navegação baseada em Botões**
3. **Próximo step padrão** (fallback)

#### Método Principal:

```php
public function getNextStep(Step $currentStep, string $userInput, array $context = []): ?Step
{
    try {
        // 1. Verifica regras de navegação primeiro
        $nextStepFromRules = $this->getNextStepFromNavigationRules($currentStep, $userInput, $context);
        if ($nextStepFromRules) {
            return $nextStepFromRules;
        }

        // 2. Verifica navegação baseada em botões
        $nextStepFromButton = $this->getNextStepFromButtonNavigation($currentStep, $userInput, $context);
        if ($nextStepFromButton) {
            return $nextStepFromButton;
        }

        // 3. Fallback para próximo step padrão
        return $this->getDefaultNextStep($currentStep);

    } catch (Exception $e) {
        return $this->getDefaultNextStep($currentStep);
    }
}
```

### 3. Regras de Navegação JSON

**Formato das Regras**:

```json
[
    {
        "condition": "button_clicked",
        "button_data": "small",
        "target_step_identifier": "choose_pizza_flavor",
        "variables": {
            "order.pizza_size": "Pequena",
            "order.total": 25.00
        }
    },
    {
        "condition": "text_equals",
        "text": "sim",
        "target_step_identifier": "confirm_order"
    },
    {
        "condition": "text_contains",
        "text": "cancelar",
        "target_step_identifier": "cancel_flow"
    }
]
```

#### Tipos de Condições:

1. **button_clicked** - Usuário clicou em botão específico
2. **text_equals** - Texto exato (case-insensitive)
3. **text_contains** - Texto contém substring

#### Identificadores de Step:

1. **Numérico** - ID direto do step
2. **String** - Identificador único do step no fluxo
3. **Especiais**:
   - `"next_step"` - Próximo step na sequência
   - `"previous_step"` - Step anterior
   - `"restart_flow"` - Reinicia o fluxo

### 4. Navegação por Botões

#### Botões Condicionais:

Botões com `internal_type = 'condition'` ativam navegação condicional:

```php
if ($button->internal_type === 'condition') {
    return $this->handleConditionalButtonNavigation($button, $currentStep, $context);
}
```

#### Matching de Botões:

```php
protected function buttonMatchesInput($button, string $userInput): bool
{
    // Verifica ID do botão
    if ($button->id && $userInput === (string) $button->id) {
        return true;
    }

    // Verifica texto do botão (case-insensitive)
    if ($button->text && strcasecmp($userInput, $button->text) === 0) {
        return true;
    }

    // Verifica callback_data
    if ($button->callback_data) {
        $callbackData = json_decode($button->callback_data, true);
        if (is_array($callbackData) && isset($callbackData['reply'])) {
            return strcasecmp($userInput, $callbackData['reply']) === 0;
        }
    }

    return false;
}
```

## Substituição de Variáveis

### Padrão {{model.variable}}

O sistema suporta substituição dinâmica de variáveis usando o padrão `{{model.variable}}`:

**Exemplos**:
- `{{client.name}}` - Nome do cliente
- `{{client.phone}}` - Telefone do cliente
- `{{conversation.id}}` - ID da conversa
- `{{step.name}}` - Nome do step atual

### VariableSubstitutionService

**Localização**: `app/Services/Meta/WhatsApp/ChatBot/Services/VariableSubstitutionService.php`

```php
public function substitute(string $text, array $availableModels): string
{
    $pattern = '/\{\{(\w+)\.(\w+)\}\}/';

    return preg_replace_callback($pattern, function ($matches) use ($availableModels) {
        $modelName = $matches[1]; // ex: 'client'
        $property = $matches[2];  // ex: 'name'

        $model = $availableModels[$modelName] ?? null;
        if (!$model) {
            return $matches[0]; // Retorna original se modelo não encontrado
        }

        return $model->{$property} ?? $matches[0];
    }, $text);
}
```

## Interface StepProcessorInterface

**Localização**: `app/Services/Meta/WhatsApp/ChatBot/Processors/StepProcessorInterface.php`

### Contrato Obrigatório:

```php
interface StepProcessorInterface
{
    /**
     * Processa um step específico
     */
    public function process(
        Step $step,
        Interaction $interaction,
        Conversation $conversation
    ): array;

    /**
     * Verifica se pode processar o step
     */
    public function canProcess(Step $step): bool;

    /**
     * Retorna tipos de step suportados
     */
    public function getSupportedStepTypes(): array;
}
```

### Implementação Padrão:

Todos os processors seguem este padrão:

```php
public function canProcess(Step $step): bool
{
    // Garante compatibilidade com sistema legado
    $step->setStepTypeFromLegacyFields();

    return $step->step_type === StepType::SPECIFIC_TYPE;
}

public function getSupportedStepTypes(): array
{
    return [StepType::SPECIFIC_TYPE->value];
}
```

## Tratamento de Erros

### 1. Processor Não Encontrado

```php
try {
    $processor = $this->stepProcessorFactory->getProcessor($step);
    return $processor->process($step, $interaction, $conversation);
} catch (\InvalidArgumentException $e) {
    \Log::warning('No processor found for step type, using default processing', [
        'step_id' => $step->id,
        'step_type' => $step->step_type?->value ?? 'unknown',
        'error' => $e->getMessage()
    ]);

    return $this->processDefaultStep($step);
}
```

### 2. Fallback Padrão

```php
protected function processDefaultStep(Step $step): array
{
    return [
        'type' => 'default',
        'step_id' => $step->id,
        'action' => 'default_processing',
        'message' => $step->step ?? 'Processamento padrão',
        'next_step' => $step->next_step,
        'move_to_next' => true,
        'fallback' => true,
    ];
}
```

### 3. Navegação de Emergência

```php
protected function getEmergencyNextStep(Step $currentStep): ?Step
{
    // Tenta próximo step na sequência
    if ($currentStep->next_step) {
        return $this->stepRepository->fetchById($currentStep->next_step);
    }

    // Tenta step inicial do fluxo
    return $this->stepRepository->getInitialStepForFlow($currentStep->flow_id);
}
```

## Casos de Uso Práticos

### 1. Fluxo de Pizzaria

**Step 1 (CONDITION)**: Escolha do tamanho
- Botões: Pequena, Média, Grande
- Navigation rules baseadas em button_data
- Atualiza `order.pizza_size` e `order.total`

**Step 2 (INPUT)**: Nome do cliente
- Solicita nome via texto
- Atualiza `client.name`
- Valida entrada não vazia

**Step 3 (COMMAND)**: Processar pedido
- Executa lógica de criação do pedido
- Calcula total final
- Envia para sistema de pedidos

**Step 4 (MESSAGE)**: Confirmação
- Exibe resumo do pedido
- Avança automaticamente para fim

### 2. Fluxo de Atendimento

**Step 1 (INTERACTIVE)**: Menu principal
- Botões: Vendas, Suporte, Financeiro
- Cada botão navega para fluxo específico

**Step 2 (CONDITION)**: Roteamento
- Baseado na seleção anterior
- Navigation rules direcionam para steps específicos

**Step 3 (WEBHOOK)**: Integração CRM
- Chama API externa para criar ticket
- Processa resposta
- Atualiza conversa com ticket ID

**Step 4 (DELAY)**: Aguardar atendente
- Delay de 30 segundos
- Mensagem de "conectando com atendente"
- Avança para step de transferência

## Conclusão

O sistema ProcessFlowStep + Processors oferece uma arquitetura robusta e extensível para processamento de fluxos conversacionais no WhatsApp. A combinação do Strategy Pattern com navegação condicional inteligente permite criar experiências conversacionais complexas e dinâmicas.

### Pontos Chave:

1. **Modularidade** - Cada tipo de step tem seu processor especializado
2. **Extensibilidade** - Fácil adição de novos tipos e behaviors
3. **Navegação Inteligente** - Múltiplas estratégias de navegação
4. **Tratamento de Erros** - Fallbacks robustos para cenários de falha
5. **Performance** - Lazy loading e cache de processors
6. **Debugging** - Logging detalhado em todos os pontos críticos

Esta arquitetura permite que o sistema evolua facilmente conforme novos requisitos de negócio surgem, mantendo a estabilidade e performance do sistema existente.

---

**Documento criado em**: 2025-09-22
**Versão**: 1.0
**Autor**: Augment Agent
**Última atualização**: 2025-09-22
