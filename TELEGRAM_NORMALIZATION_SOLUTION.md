# Solução para Normalização de Dados do Telegram

## Problema Identificado

Os dados vindos do Telegram têm uma estrutura diferente dos dados do banco:

### Formato do Banco (Funciona) ✅
```json
"{\"paciente\":\"<PERSON>\",\"data_nascimento\":\"20\\\/09\\\/1973\"}"
```

### Formato do Telegram (Erro) ❌
```json
{"raw_data":"{\\n\\u00a0\\u00a0\\u00a0 \\\"paciente\\\": \\\"<PERSON>\\\",\\n\\u00a0\\u00a0\\u00a0 \\\"data_nascimento\\\": \\\"20\\\/09\\\/1973\\\"\\n}"}
```

## Solução Implementada

### 1. **Detecção Automática de Formato**
A função `detectAndNormalizeFormat()` identifica automaticamente:
- Dados do Telegram (contém `"raw_data"`)
- Dados do banco (contém múltiplos escapes `\\\\\\`)
- Dados normais

### 2. **Normalização Específica para Telegram**
A função `normalizeTelegramFormat()` processa dados do Telegram:

#### Passo 1: Extração do `raw_data`
```php
$decoded = json_decode($data, true);
$rawData = $decoded['raw_data'];
```

#### Passo 2: Limpeza de Formatação
```php
// Remove quebras de linha
$cleaned = str_replace(['\n', '\\n', '\r', '\\r'], '', $rawData);

// Remove espaços não-quebráveis
$cleaned = str_replace(['\u00a0', '\\u00a0'], '', $rawData);

// Remove espaços extras entre elementos JSON
$cleaned = preg_replace('/(?<=[{,:])\s+(?=["\[\{])/', '', $cleaned);
```

#### Passo 3: Correção de Escape Characters
```php
$cleaned = str_replace('\\"', '"', $cleaned);
$cleaned = str_replace('\\/', '/', $cleaned);
```

#### Passo 4: Validação e Recodificação
```php
$innerDecoded = json_decode($cleaned, true);
if (json_last_error() === JSON_ERROR_NONE) {
    return json_encode($innerDecoded, JSON_UNESCAPED_SLASHES);
}
```

### 3. **Método Alternativo de Limpeza**
Se o método principal falhar, `alternativeTelegramCleaning()` aplica limpeza mais agressiva:

```php
// Limpeza agressiva de espaços
$cleaned = preg_replace('/\s+/', ' ', $cleaned);
$cleaned = str_replace([' "', '" ', ' :', ': '], ['"', '"', ':', ':'], $cleaned);
// ... mais substituições
```

## Como Usar

### Uso Automático
```php
$sendWebhook = new SendDocumentToWebhook($telegram);
$sendWebhook->perform($dadosDoTelegramOuBanco);
// A detecção e normalização é automática
```

### Teste Manual
Execute o script `test_telegram_normalization.php` para validar:
```bash
php test_telegram_normalization.php
```

## Logs de Debug

O sistema agora gera logs detalhados para cada etapa:

1. **Dados Originais**: Preview dos dados recebidos
2. **Detecção de Formato**: Qual formato foi detectado
3. **Extração raw_data**: Conteúdo extraído do Telegram
4. **Após Limpeza**: Dados após remoção de formatação
5. **Resultado Final**: Dados normalizados ou erro

### Exemplo de Logs
```
normalizeTelegramFormat - Processing Telegram data
normalizeTelegramFormat - Extracted raw_data
normalizeTelegramFormat - After whitespace removal
normalizeTelegramFormat - Successfully normalized
```

## Transformação Esperada

### Entrada (Telegram)
```json
{
  "raw_data": "{\\n\\u00a0\\u00a0\\u00a0 \\\"paciente\\\": \\\"Carlos Jose Gomes da Silva\\\",\\n\\u00a0\\u00a0\\u00a0 \\\"data_nascimento\\\": \\\"20\\\/09\\\/1973\\\"\\n}"
}
```

### Saída (Normalizada)
```json
{
  "paciente": "Carlos Jose Gomes da Silva",
  "data_nascimento": "20/09/1973",
  "idade": "51 anos",
  "sexo": "Masculino",
  "convenio": "Bradesco Saúde"
}
```

## Tratamento de Erros

### Cenários de Erro Tratados:
1. **JSON inválido após limpeza**: Tenta método alternativo
2. **Estrutura raw_data ausente**: Usa normalização padrão
3. **Falha em todos os métodos**: Retorna `{}`

### Logs de Erro:
```
normalizeTelegramFormat - JSON decode failed: Syntax error
alternativeTelegramCleaning - All methods failed
```

## Validação

### Testes Implementados:
- ✅ Normalização de dados com `raw_data`
- ✅ Remoção de espaços não-quebráveis
- ✅ Correção de escape characters
- ✅ Estruturas aninhadas complexas
- ✅ Arrays de materiais e procedimentos
- ✅ Preservação de caracteres Unicode

### Verificação de Integridade:
```php
// Verifica se a estrutura final é idêntica ao banco
$bankKeys = array_keys($bankDecoded);
$telegramKeys = array_keys($normalizedDecoded);
$identical = (array_diff($bankKeys, $telegramKeys) === array_diff($telegramKeys, $bankKeys));
```

## Monitoramento

### Para verificar se está funcionando:

1. **Verifique os logs do DBLog** para:
   - `normalizeTelegramFormat - Successfully normalized`
   - `SendDocumentToWebhook::perform - Final data to send`

2. **Em caso de erro**:
   - `normalizeTelegramFormat - JSON decode failed`
   - `alternativeTelegramCleaning - All methods failed`

3. **Dados de exemplo nos logs**:
   - `patient: Carlos Jose Gomes da Silva` (indica sucesso)

## Próximos Passos

1. **Teste em Produção**: Monitore os logs para verificar a normalização
2. **Validação**: Confirme que o webhook recebe dados corretos
3. **Otimização**: Ajuste os regex se necessário
4. **Backup**: Mantenha logs dos dados originais para debug

## Exemplo Completo

```php
// Dados problemáticos do Telegram
$telegramData = '{"raw_data":"{\\n\\u00a0\\u00a0\\u00a0 \\\"paciente\\\": \\\"Carlos Jose Gomes da Silva\\\"\\n}"}';

// Processamento automático
$sendWebhook = new SendDocumentToWebhook($telegram);
$sendWebhook->perform($telegramData);

// Resultado esperado no webhook:
// {"paciente": "Carlos Jose Gomes da Silva", "_token": "..."}
```

A solução agora detecta automaticamente o formato dos dados e aplica a normalização apropriada, garantindo que tanto dados do banco quanto do Telegram sejam processados corretamente! 🎯
