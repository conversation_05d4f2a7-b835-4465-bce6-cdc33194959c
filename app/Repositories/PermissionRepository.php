<?php

namespace App\Repositories;

use App\Domains\Permission as PermissionDomain;
use App\Factories\PermissionFactory;
use App\Models\Permission;

class PermissionRepository
{
    private PermissionFactory $permissionFactory;

    public function __construct(PermissionFactory $permissionFactory){
        $this->permissionFactory = $permissionFactory;
    }

    /**
     * @return PermissionDomain[]
     */
    public function fetchAll() : array {
        $permissiones = [];

        $models = Permission::all();

        foreach ($models as $model){
            $permissiones[] = $this->permissionFactory->buildFromModel($model);
        }

        return $permissiones;
    }

    public function store(PermissionDomain $permission) : PermissionDomain {
        $savedPermission = Permission::create($permission->toStoreArray());

        $permission->id = $savedPermission->id;

        return $permission;
    }

    public function update(PermissionDomain $permission) : PermissionDomain {
        Permission::where('id', $permission->id)
            ->update($permission->toUpdateArray());

        return $permission;
    }

    public function fetchById(int $id) : PermissionDomain {
        return $this->permissionFactory->buildFromModel(
            Permission::findOrFail($id)
        );
    }

    public function fetchBySlug(string $slug) : ?PermissionDomain {
        $permission = Permission::where('slug', $slug)->first();

        if (!$permission) {
            return null;
        }

        return $this->permissionFactory->buildFromModel($permission);
    }

    public function delete(PermissionDomain $permission) : bool {
        return Permission::find($permission->id)->delete();
    }
}
