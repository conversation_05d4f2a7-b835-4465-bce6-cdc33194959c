<?php

namespace App\Repositories;

use App\Domains\Filters\OrderBy;
use App\Domains\Filters\ProfileFilters;
use App\Domains\Profile as ProfileDomain;
use App\Factories\ProfileFactory;
use App\Models\Profile;
use EloquentBuilder;

class ProfileRepository
{
    private ProfileFactory $profileFactory;

    public function __construct(ProfileFactory $profileFactory){
        $this->profileFactory = $profileFactory;
    }

    /**
     * @return ProfileDomain[]
     */
    public function fetchAll(ProfileFilters $filters, OrderBy $orderBy) : array {
        $profiles = [];

        $models = EloquentBuilder::to(Profile::class, $filters->filters)
            ->orderBy($orderBy->order, $orderBy->by)
            ->paginate($orderBy->limit ?? 30);

        foreach ($models as $model){
            $profiles[] = $this->profileFactory->buildFromModel($model);
        }

        return [
            'data' => $profiles,
            'count' => $models->count(),
            'total' => $models->total(),
            'currentPage' => $models->currentPage(),
            'lastPage' => $models->lastPage(),
        ];
    }

    public function store(ProfileDomain $profile) : ProfileDomain {
        $savedProfile = Profile::create($profile->toStoreArray());

        $profile->id = $savedProfile->id;

        return $profile;
    }

    public function update(ProfileDomain $profile) : ProfileDomain {
        Profile::where('id', $profile->id)
            ->where('organization_id', auth()->user()->organization_id)
            ->update($profile->toUpdateArray());

        return $profile;
    }

    public function fetchById(int $id) : ProfileDomain {
        return $this->profileFactory->buildFromModel(
            Profile::with('permissions')->findOrFail($id)
        );
    }

    public function delete(ProfileDomain $profile) : bool {
        return Profile::find($profile->id)->delete();
    }

    /**
     * @return array
     */
    public function fetchFromOrganization(int $organization_id, ProfileFilters $filters, OrderBy $orderBy) : array {
        $profiles = [];

        $models = EloquentBuilder::to(Profile::class, $filters->filters)
            ->where("organization_id", auth()->user()->organization_id)
            ->orderBy($orderBy->order, $orderBy->by)
            ->paginate($orderBy->limit ?? 30);

        foreach ($models as $model){
            $profiles[] = $this->profileFactory->buildFromModel($model);
        }

        return [
            'data' => $profiles,
            'count' => $models->count(),
            'total' => $models->total(),
            'currentPage' => $models->currentPage(),
            'lastPage' => $models->lastPage(),
        ];
    }

    public function fetchAdminIntoOrganization(int $organization_id) : ?ProfileDomain {
        return $this->profileFactory->buildFromModel(
            Profile::where("organization_id", $organization_id)->where("is_admin", true)->first()
        );
    }
}
