<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\Response;

class CheckOrganizationOwnership
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     * @param  string|null  $paramName  The route parameter name containing the organization ID (default: 'organizationId')
     */
    public function handle(Request $request, Closure $next, ?string $paramName = 'organizationId'): Response
    {
        $user = $request->user();

        if (!$user) {
            return $this->handleUnauthorized($request, 'User not authenticated');
        }

        if (!$user->profile) {
            return $this->handleUnauthorized($request, 'User has no profile assigned');
        }

        // Super admin can access any organization
        if ($user->profile->is_super_admin) {
            $request->merge([
                'is_super_admin' => true,
                'organization_access_granted' => true,
                'access_reason' => 'super_admin'
            ]);
            return $next($request);
        }

        // Get organization ID from route parameter
        $organizationId = $request->route($paramName);
        
        if (!$organizationId) {
            return $this->handleError($request, "Organization ID parameter '{$paramName}' not found in route");
        }

        // Check if user belongs to the organization
        if ($user->organization_id != $organizationId) {
            Log::warning('Organization ownership denied', [
                'user_id' => $user->id,
                'user_organization_id' => $user->organization_id,
                'requested_organization_id' => $organizationId,
                'profile_id' => $user->profile->id,
                'request_url' => $request->url(),
                'request_method' => $request->method(),
            ]);

            return $this->handleAccessDenied($request, $organizationId);
        }

        // Add organization access info to request
        $request->merge([
            'organization_access_granted' => true,
            'access_reason' => 'organization_member',
            'organization_id' => $organizationId,
            'user_organization_id' => $user->organization_id
        ]);

        return $next($request);
    }

    /**
     * Handle unauthorized access
     */
    protected function handleUnauthorized(Request $request, string $reason): Response
    {
        if ($request->expectsJson()) {
            return response()->json([
                'error' => 'Unauthorized',
                'message' => $reason,
                'code' => 'UNAUTHORIZED'
            ], 401);
        }

        return redirect()->route('login')->with('error', $reason);
    }

    /**
     * Handle access denied
     */
    protected function handleAccessDenied(Request $request, int $organizationId): Response
    {
        if ($request->expectsJson()) {
            return response()->json([
                'error' => 'Access Denied',
                'message' => 'You can only access resources from your own organization.',
                'requested_organization_id' => $organizationId,
                'user_organization_id' => $request->user()->organization_id,
                'code' => 'ORGANIZATION_ACCESS_DENIED'
            ], 403);
        }

        return redirect()->back()->with('error', 'You can only access resources from your own organization.');
    }

    /**
     * Handle system errors
     */
    protected function handleError(Request $request, string $message): Response
    {
        if ($request->expectsJson()) {
            return response()->json([
                'error' => 'System Error',
                'message' => $message,
                'code' => 'SYSTEM_ERROR'
            ], 500);
        }

        return redirect()->back()->with('error', $message);
    }
}
