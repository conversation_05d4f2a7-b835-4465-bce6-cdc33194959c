<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\Response;

class CheckAdmin
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $user = $request->user();

        if (!$user) {
            return $this->handleUnauthorized($request, 'User not authenticated');
        }

        if (!$user->profile) {
            return $this->handleUnauthorized($request, 'User has no profile assigned');
        }

        // Super admin always has admin privileges
        $isAdmin = $user->profile->is_admin || $user->profile->is_super_admin;

        if (!$isAdmin) {
            Log::warning('Admin access denied', [
                'user_id' => $user->id,
                'profile_id' => $user->profile->id,
                'profile_name' => $user->profile->name,
                'is_admin' => $user->profile->is_admin,
                'is_super_admin' => $user->profile->is_super_admin,
                'request_url' => $request->url(),
                'request_method' => $request->method(),
            ]);

            return $this->handleAccessDenied($request);
        }

        // Add admin info to request for use in controllers
        $request->merge([
            'is_admin' => true,
            'is_super_admin' => $user->profile->is_super_admin,
            'user_profile' => [
                'id' => $user->profile->id,
                'name' => $user->profile->name,
                'is_admin' => $user->profile->is_admin,
                'is_super_admin' => $user->profile->is_super_admin,
            ]
        ]);

        return $next($request);
    }

    /**
     * Handle unauthorized access
     */
    protected function handleUnauthorized(Request $request, string $reason): Response
    {
        if ($request->expectsJson()) {
            return response()->json([
                'error' => 'Unauthorized',
                'message' => $reason,
                'code' => 'UNAUTHORIZED'
            ], 401);
        }

        return redirect()->route('login')->with('error', $reason);
    }

    /**
     * Handle access denied
     */
    protected function handleAccessDenied(Request $request): Response
    {
        if ($request->expectsJson()) {
            return response()->json([
                'error' => 'Access Denied',
                'message' => 'You need admin or super admin privileges to access this resource.',
                'code' => 'ADMIN_REQUIRED'
            ], 403);
        }

        return redirect()->back()->with('error', 'You need admin or super admin privileges to access this resource.');
    }
}
