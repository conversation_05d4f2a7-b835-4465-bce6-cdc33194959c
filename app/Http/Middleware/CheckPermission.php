<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\Response;
use App\Factories\ProfileFactory;

class CheckPermission
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     * @param  string  $permission  The permission slug required
     */
    public function handle(Request $request, Closure $next, string $permission): Response
    {
        $user = $request->user();

        if (!$user) {
            return $this->handleUnauthorized($request, 'User not authenticated');
        }

        if (!$user->profile) {
            return $this->handleUnauthorized($request, 'User has no profile assigned');
        }

        try {
            // Convert Model to Domain to use can() method
            $profileFactory = app()->make(ProfileFactory::class);
            $profileDomain = $profileFactory->buildFromModel($user->profile);

            // Check if user has the required permission
            if (!$profileDomain->can($permission)) {
                Log::warning('Permission denied', [
                    'user_id' => $user->id,
                    'profile_id' => $user->profile->id,
                    'required_permission' => $permission,
                    'request_url' => $request->url(),
                    'request_method' => $request->method(),
                ]);

                return $this->handlePermissionDenied($request, $permission);
            }

            // Add permission info to request for use in controllers
            $request->merge([
                'user_permissions' => $profileDomain->permissions ?? [],
                'user_profile' => [
                    'id' => $profileDomain->id,
                    'name' => $profileDomain->name,
                    'is_admin' => $profileDomain->isAdmin(),
                    'is_super_admin' => $profileDomain->isSuperAdmin(),
                ]
            ]);

            return $next($request);

        } catch (\Exception $e) {
            Log::error('Error checking permission', [
                'user_id' => $user->id,
                'permission' => $permission,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return $this->handleError($request, 'Error checking permissions');
        }
    }

    /**
     * Handle unauthorized access
     */
    protected function handleUnauthorized(Request $request, string $reason): Response
    {
        if ($request->expectsJson()) {
            return response()->json([
                'error' => 'Unauthorized',
                'message' => $reason,
                'code' => 'UNAUTHORIZED'
            ], 401);
        }

        return redirect()->route('login')->with('error', $reason);
    }

    /**
     * Handle permission denied
     */
    protected function handlePermissionDenied(Request $request, string $permission): Response
    {
        if ($request->expectsJson()) {
            return response()->json([
                'error' => 'Permission Denied',
                'message' => "You don't have permission to access this resource.",
                'required_permission' => $permission,
                'code' => 'PERMISSION_DENIED'
            ], 403);
        }

        return redirect()->back()->with('error', "You don't have permission to access this resource.");
    }

    /**
     * Handle system errors
     */
    protected function handleError(Request $request, string $message): Response
    {
        if ($request->expectsJson()) {
            return response()->json([
                'error' => 'System Error',
                'message' => $message,
                'code' => 'SYSTEM_ERROR'
            ], 500);
        }

        return redirect()->back()->with('error', $message);
    }
}
