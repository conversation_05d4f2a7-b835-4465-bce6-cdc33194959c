<?php

namespace App\Http\Requests\Permission;

use App\Helpers\Traits\Response;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;

class UpdatePermissionRequest extends FormRequest
{
    use Response;

    public function authorize(): bool
    {
        return true;
    }

    protected function failedValidation(Validator $validator)
    {
        throw new HttpResponseException(
            $this->response(
                'Validation Failed',
                'error',
                422,
                [],
                $validator->errors()->toArray()
            )
        );
    }

    public function rules(): array
    {
        return [
            'slug' => 'required|unique:permissions',
            'name' => 'required',
        ];
    }

    public function messages(): array
    {
        return [
            'slug.required' => 'The slug of permission is required',
            'name.required' => 'The name of permission is required',
        ];
    }
}
