<?php

namespace App\Http\Requests\Profile;

use App\Helpers\Traits\Response;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;

class UpdateProfileRequest extends FormRequest
{
    use Response;

    public function authorize(): bool
    {
        return true;
    }

    protected function failedValidation(Validator $validator)
    {
        throw new HttpResponseException(
            $this->response(
                'Validation Failed',
                'error',
                422,
                [],
                $validator->errors()->toArray()
            )
        );
    }

    public function rules(): array
    {
        return [
            'organization_id' => 'nullable|integer|exists:organizations,id',
            'name' => 'required',
        ];
    }

    public function messages(): array
    {
        return [
            'organization_id.exists' => 'The selected organization does not exist.',
            'name.required' => 'The name of permission is required',
        ];
    }
}
