<?php

namespace App\Http\Requests\Profile;

use App\Helpers\Traits\Response;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;

class AddPermissionRequest extends FormRequest
{
    use Response;

    public function authorize(): bool
    {
        return true;
    }

    protected function failedValidation(Validator $validator)
    {
        throw new HttpResponseException(
            $this->response(
                'Validation Failed',
                'error',
                422,
                [],
                $validator->errors()->toArray()
            )
        );
    }

    public function rules(): array
    {
        return [
            'profile_id' => 'required|integer|exists:profiles,id',
            'permission_slug' => 'required|string|exists:permissions,slug',
        ];
    }

    public function messages(): array
    {
        return [
            'profile_id.required' => 'The profile ID is required',
            'profile_id.exists' => 'The selected profile does not exist',
            'permission_slug.required' => 'The permission slug is required',
            'permission_slug.exists' => 'The selected permission does not exist',
        ];
    }
}
