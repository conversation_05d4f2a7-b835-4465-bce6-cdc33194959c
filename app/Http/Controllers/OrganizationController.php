<?php

namespace App\Http\Controllers;

use App\Helpers\Traits\Response;
use App\Http\Requests\Organization\UpdateRequest;
use App\Services\ASAAS\Exceptions\AsaasException;
use App\UseCases\Organization\Get;
use App\UseCases\Organization\GetAll;
use App\UseCases\Organization\Update;
use App\UseCases\Organization\CreateAdminProfile;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class OrganizationController extends Controller
{
    use Response;

    public function index() : JsonResponse {
        try{
            /** @var GetAll $useCase */
            $useCase = app()->make(GetAll::class);
            $getAll = $useCase->perform();

            $data = $getAll['data'] ?? [];
            unset($getAll['data']);

            return $this->response(
                "Authorized",
                "success",
                200 ,
                $data ?? [],
                null,
                $getAll ?? []
            );
        } catch (\Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    public function store() : JsonResponse {
        try{
            return $this->response("endpoint unavailable");
        } catch (\Throwable $e){
            return $this->errorResponse($e->getMessage());
        }
    }

    public function show(int $id) : JsonResponse {
        try{
            $with_asaas = request()->input('with_asaas', false);

            /** @var Get $useCase */
            $useCase = app()->make(Get::class);
            $data = $useCase->perform($id, $with_asaas);

            return $this->response(
                "Authorized",
                "success",
                200 ,
                $data->toArray()
            );
        } catch (\Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    public function checkAsaasIntegration(int $id) : JsonResponse {
        try{
            $with_asaas = request()->input('with_asaas', true);

            /** @var Get $useCase */
            $useCase = app()->make(Get::class);
            $data = $useCase->perform($id, $with_asaas);

            return $this->response(
                "Authorized",
                "success",
                200 ,
                [
                    'has_asaas_integration' => $data->hasAsaasIntegration(),
                    'has_asaas_api_integration' => $data->hasAsaasAPIIntegration(),
                    'has_asaas_customer_integration' => $data->hasAsaasCustomerIntegration(),
                    'organization' => $data->toArray(),
                ]
            );
        } catch (\Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    public function getToASAASPayload(int $id) : JsonResponse {
        try{
            /** @var Get $useCase */
            $useCase = app()->make(Get::class);
            $organization = $useCase->perform($id);

            return $this->response(
                "Authorized",
                "success",
                200 ,
                $organization->toAsaasPayload()
            );
        } catch (\Throwable $e){
            return $this->errorResponse($e->getMessage());
        }
    }

    public function update(UpdateRequest $request, int $id) : JsonResponse {
        try{
            /** @var Update $useCase */
            $useCase = app()->make(Update::class);
            $organization = $useCase->perform($request, $id);

            return $this->response(
                "Organization updated successfully",
                "success",
                200,
                $organization->toArray()
            );
        } catch (\Throwable $e){
            return $this->errorResponse($e->getMessage());
        }
    }

    public function destroy() : JsonResponse {
        try{
            return $this->response("endpoint unavailable");
        } catch (\Throwable $e){
            return $this->errorResponse($e->getMessage());
        }
    }

    public function checkAccess(int $id): JsonResponse
    {
        try {
            /** @var Get $getOrganizationUseCase */
            $getOrganizationUseCase = app()->make(Get::class);
            $organization = $getOrganizationUseCase->perform($id);

            return $this->response(
                "Organization access checked",
                "success",
                200,
                [$organization->canAccessSystem()]
            );
        } catch (\Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    public function createAdminProfile(int $organizationId): JsonResponse
    {
        try {
            /** @var CreateAdminProfile $useCase */
            $useCase = app()->make(CreateAdminProfile::class);
            $adminProfile = $useCase->perform($organizationId);

            return $this->response(
                "Admin profile created successfully for organization",
                "success",
                200,
                $adminProfile->toArray()
            );
        } catch (\Throwable $e) {
            return $this->errorResponse($e->getMessage(), $e->getCode() ?: 500);
        }
    }
}
