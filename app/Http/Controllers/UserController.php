<?php

namespace App\Http\Controllers;

use App\Domains\Filters\OrderBy;
use App\Domains\Filters\UserFilters;
use App\Helpers\Traits\Response;
use App\Http\Requests\User\StoreRequest;
use App\Http\Requests\User\UpdateRequest;
use App\UseCases\Notification\GetAllNotifications;
use App\UseCases\Notification\GetUnreadNotifications;
use App\UseCases\Notification\ReadAllNotifications;
use App\UseCases\Notification\ReadNotification;
use App\UseCases\User\Delete;
use App\UseCases\User\Get;
use App\UseCases\User\GetAll;
use App\UseCases\User\Store;
use App\UseCases\User\Update;
use App\UseCases\User\FetchUserPermissions;
use App\UseCases\User\CheckSpecificUserPermission;
use Illuminate\Http\JsonResponse;

class UserController extends Controller
{
    use Response;

    public function index() : JsonResponse {
        try{
            /** @var GetAll $useCase */
            $useCase = app()->make(GetAll::class);
            $getAll = $useCase->perform(
                new UserFilters(request()->all()),
                new OrderBy(request()->only(OrderBy::ALLOWED_FIELDS))
            );

            $data = $getAll['data'] ?? [];
            unset($getAll['data']);

            return $this->response(
                "Authorized",
                "success",
                200 ,
                $data ?? [],
                null,
                $getAll ?? []
            );
        } catch (\Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    public function store(StoreRequest $request) : JsonResponse {
        try{
            /** @var Store $useCase */
            $useCase = app()->make(Store::class);
            $user = $useCase->perform($request);

            return $this->response(
                "User created successfully",
                "success",
                200 ,
                $user->toArray()
            );
        } catch (\Throwable $e){
            return $this->errorResponse($e->getMessage());
        }
    }

    public function show(int $id) : JsonResponse {
        try{
            /** @var Get $useCase */
            $useCase = app()->make(Get::class);
            $data = $useCase->perform($id);

            return $this->response(
                "Authorized",
                "success",
                200 ,
                $data->toArray()
            );
        } catch (\Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    public function update(UpdateRequest $request, int $id) : JsonResponse {
        try{
            /** @var Update $useCase */
            $useCase = app()->make(Update::class);
            $update = $useCase->perform($request, $id);

            return $this->response(
                "User updated successfully",
                "success",
                200 ,
                $update->toArray()
            );
        } catch (\Throwable $e){
            return $this->errorResponse($e->getMessage());
        }
    }

    public function destroy(int $id) : JsonResponse {
        try{
            /** @var Delete $useCase */
            $useCase = app()->make(Delete::class);
            $delete = $useCase->perform($id);

            return $this->response(
                "User deleted successfully",
                "success",
                200 ,
                [$delete]
            );
        } catch (\Throwable $e){
            return $this->errorResponse($e->getMessage());
        }
    }

    public function getAllNotifications() : JsonResponse {
        try{
            /** @var GetAllNotifications $useCase */
            $useCase = app()->make(GetAllNotifications::class);
            $notifications = $useCase->perform();

            return $this->response(
                "User notifications",
                "success",
                200 ,
                [$notifications->toArray()]
            );
        } catch (\Throwable $e){
            return $this->errorResponse($e->getMessage());
        }
    }

    public function getUnreadNotifications() : JsonResponse {
        try{
            /** @var GetUnreadNotifications $useCase */
            $useCase = app()->make(GetUnreadNotifications::class);
            $notifications = $useCase->perform();

            return $this->response(
                "User unread notifications",
                "success",
                200 ,
                [$notifications->toArray()]
            );
        } catch (\Throwable $e){
            return $this->errorResponse($e->getMessage());
        }
    }

    public function readAllNotifications() : JsonResponse {
        try{
            /** @var ReadAllNotifications $useCase */
            $useCase = app()->make(ReadAllNotifications::class);
            $read = $useCase->perform();

            return $this->response(
                "All notifications read successfully",
                "success",
                200 ,
                [$read]
            );
        } catch (\Throwable $e){
            return $this->errorResponse($e->getMessage());
        }
    }

    public function readNotification(int $id) : JsonResponse {
        try{
            /** @var ReadNotification $useCase */
            $useCase = app()->make(ReadNotification::class);
            $read = $useCase->perform($id);

            return $this->response(
                "Notification read successfully",
                "success",
                200 ,
                [$read]
            );
        } catch (\Throwable $e){
            return $this->errorResponse($e->getMessage());
        }
    }

    public function fetchUserPermissions() : JsonResponse {
        try{
            /** @var FetchUserPermissions $useCase */
            $useCase = app()->make(FetchUserPermissions::class);
            $data = $useCase->perform();

            return $this->response(
                "User permissions retrieved successfully",
                "success",
                200,
                $data
            );
        } catch (\Throwable $e){
            return $this->errorResponse($e->getMessage());
        }
    }

    public function checkSpecificUserPermission(string $slug) : JsonResponse {
        try{
            /** @var CheckSpecificUserPermission $useCase */
            $useCase = app()->make(CheckSpecificUserPermission::class);
            $data = $useCase->perform($slug);

            return $this->response(
                "Permission check completed",
                "success",
                200,
                $data
            );
        } catch (\Throwable $e){
            return $this->errorResponse($e->getMessage());
        }
    }
}
