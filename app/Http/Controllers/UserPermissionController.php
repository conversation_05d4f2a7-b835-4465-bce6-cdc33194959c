<?php

namespace App\Http\Controllers;

use App\Helpers\Traits\Response;
use Illuminate\Http\JsonResponse;
use App\UseCases\User\CheckPermission;
use App\UseCases\User\GetAllUserPermissions;

class UserPermissionController extends Controller
{
    use Response;

    // Only the two methods below are needed for user permission checking

    public function fetchUserPermissions() : JsonResponse {
        try{
            /** @var GetAllUserPermissions $useCase */
            $useCase = app()->make(GetAllUserPermissions::class);
            $permissions = $useCase->perform();

            return $this->response(
                "User permissions retrieved successfully",
                "success",
                200 ,
                $permissions
            );
        } catch (\Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    public function checkSpecificUserPermission(string $slug) : JsonResponse {
        try{
            /** @var CheckPermission $useCase */
            $useCase = app()->make(CheckPermission::class);
            $canAccess = $useCase->perform($slug);

            return $this->response(
                "Permission check completed",
                "success",
                200 ,
                ['can' => $canAccess, 'permission' => $slug]
            );
        } catch (\Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
    }
}
