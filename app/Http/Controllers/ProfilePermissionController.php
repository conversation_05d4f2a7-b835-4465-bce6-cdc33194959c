<?php

namespace App\Http\Controllers;

use App\Helpers\Traits\Response;
use Illuminate\Http\JsonResponse;
use App\Http\Requests\Profile\AddPermissionRequest;
use App\Http\Requests\Profile\RemovePermissionRequest;
use App\UseCases\Profile\AddPermission;
use App\UseCases\Profile\RemovePermission;

class ProfilePermissionController extends Controller
{
    use Response;

    /**
     * Add permission to a profile
     * @param AddPermissionRequest $request
     * @return JsonResponse
     */
    public function addPermission(AddPermissionRequest $request): JsonResponse
    {
        try {
            /** @var AddPermission $useCase */
            $useCase = app()->make(AddPermission::class);
            $result = $useCase->perform($request);

            return $this->response(
                $result['message'],
                "success",
                200,
                [
                    'profile_id' => $result['profile_id'],
                    'permission_slug' => $result['permission_slug'],
                    'success' => $result['success']
                ]
            );
        } catch (\Throwable $e) {
            return $this->errorResponse($e->getMessage(), $e->getCode() ?: 500);
        }
    }

    /**
     * Remove permission from a profile
     * @param RemovePermissionRequest $request
     * @return JsonResponse
     */
    public function removePermission(RemovePermissionRequest $request): JsonResponse
    {
        try {
            /** @var RemovePermission $useCase */
            $useCase = app()->make(RemovePermission::class);
            $result = $useCase->perform($request);

            return $this->response(
                $result['message'],
                "success",
                200,
                [
                    'profile_id' => $result['profile_id'],
                    'permission_slug' => $result['permission_slug'],
                    'success' => $result['success']
                ]
            );
        } catch (\Throwable $e) {
            return $this->errorResponse($e->getMessage(), $e->getCode() ?: 500);
        }
    }

    /**
     * Get all permissions for a specific profile
     * @param int $profileId
     * @return JsonResponse
     */
    public function getProfilePermissions(int $profileId): JsonResponse
    {
        try {
            $user = request()->user();
            if (!$user || !$user->profile || (!$user->profile->is_super_admin && !$user->profile->is_admin)) {
                return $this->errorResponse("You need admin or super admin privileges to view profile permissions.", 403);
            }

            $profileModel = \App\Models\Profile::with('permissions')->findOrFail($profileId);

            // Validate that profile belongs to user's organization (unless super admin)
            if (!$user->profile->is_super_admin && $profileModel->organization_id !== $user->organization_id) {
                return $this->errorResponse("You can only view permissions for profiles in your organization.", 403);
            }

            $permissions = $profileModel->permissions->map(function ($permission) {
                return [
                    'id' => $permission->id,
                    'name' => $permission->name,
                    'slug' => $permission->slug,
                    'description' => $permission->description,
                ];
            });

            return $this->response(
                "Profile permissions retrieved successfully",
                "success",
                200,
                [
                    'profile_id' => $profileId,
                    'profile_name' => $profileModel->name,
                    'permissions' => $permissions
                ]
            );
        } catch (\Throwable $e) {
            return $this->errorResponse($e->getMessage(), $e->getCode() ?: 500);
        }
    }
}
