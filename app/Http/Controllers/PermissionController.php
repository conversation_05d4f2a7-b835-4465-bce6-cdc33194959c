<?php

namespace App\Http\Controllers;

use App\Helpers\Traits\Response;
use App\Http\Requests\Permission\CreatePermissionRequest;
use App\Http\Requests\Permission\UpdatePermissionRequest;
use App\Models\Permission;
use App\UseCases\Permission\Get;
use App\UseCases\Permission\GetAll;
use App\UseCases\Permission\Store;
use App\UseCases\Permission\Update;
use App\UseCases\Permission\Delete;
use Illuminate\Http\JsonResponse;

class PermissionController extends Controller
{
    use Response;

    public function index() : JsonResponse {
        try{
            /** @var GetAll $useCase */
            $useCase = app()->make(GetAll::class);
            $data = $useCase->perform();

            return $this->response(
                "Authorized",
                "success",
                200 ,
                $data
            );
        } catch (\Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    public function store(CreatePermissionRequest $request) : JsonResponse {
        try{
            /** @var Store $useCase */
            $useCase = app()->make(Store::class);
            $data = $useCase->perform($request);

            return $this->response(
                "Permission created successfully",
                "success",
                201,
                $data->toArray()
            );
        } catch (\Throwable $e){
            return $this->errorResponse($e->getMessage());
        }
    }

    public function show(int $id) : JsonResponse {
        try{
            /** @var Get $useCase */
            $useCase = app()->make(Get::class);
            $data = $useCase->perform($id);

            return $this->response(
                "Authorized",
                "success",
                200 ,
                $data->toArray()
            );
        } catch (\Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    public function update(UpdatePermissionRequest $request, int $id) : JsonResponse {
        try{
            /** @var Update $useCase */
            $useCase = app()->make(Update::class);
            $data = $useCase->perform($request, $id);

            return $this->response(
                "Permission updated successfully",
                "success",
                200 ,
                $data->toArray()
            );
        } catch (\Throwable $e){
            return $this->errorResponse($e->getMessage());
        }
    }

    public function destroy(int $id) : JsonResponse {
        try{
            /** @var Delete $useCase */
            $useCase = app()->make(Delete::class);
            $delete = $useCase->perform($id);

            return $this->response(
                "Permission deleted successfully",
                "success",
                200 ,
                [$delete]
            );
        } catch (\Throwable $e){
            return $this->errorResponse($e->getMessage());
        }
    }
}
