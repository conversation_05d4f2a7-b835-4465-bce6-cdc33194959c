<?php

namespace App\Http\Controllers;

use App\Domains\Filters\OrderBy;
use App\Domains\Filters\ProfileFilters;
use App\Factories\ProfileFactory;
use App\UseCases\Profile\Get;
use App\UseCases\Profile\Store;
use App\UseCases\Profile\GetAll;
use App\UseCases\Profile\Update;
use App\UseCases\Profile\Delete;
use App\Helpers\Traits\Response;
use Illuminate\Http\JsonResponse;
use App\Http\Requests\Profile\CreateProfileRequest;
use App\Http\Requests\Profile\UpdateProfileRequest;

class ProfileController extends Controller
{
    use Response;

    public function index() : JsonResponse {
        try{
            /** @var GetAll $useCase */
            $useCase = app()->make(GetAll::class);
            $data = $useCase->perform(
                new ProfileFilters(request()->all()),
                new OrderBy(request()->only(OrderBy::ALLOWED_FIELDS))
            );

            return $this->response(
                "Authorized",
                "success",
                200 ,
                $data
            );
        } catch (\Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    public function store(CreateProfileRequest $request) : JsonResponse {
        try{
            /** @var Store $useCase */
            $useCase = app()->make(Store::class);
            $data = $useCase->perform($request);

            return $this->response(
                "Profile created successfully",
                "success",
                201,
                $data->toArray()
            );
        } catch (\Throwable $e){
            return $this->errorResponse($e->getMessage());
        }
    }

    public function show(int $id) : JsonResponse {
        try{
            /** @var Get $useCase */
            $useCase = app()->make(Get::class);
            $data = $useCase->perform($id);

            return $this->response(
                "Authorized",
                "success",
                200 ,
                $data->toArray()
            );
        } catch (\Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    public function update(UpdateProfileRequest $request, int $id) : JsonResponse {
        try{
            /** @var Update $useCase */
            $useCase = app()->make(Update::class);
            $data = $useCase->perform($request, $id);

            return $this->response(
                "Profile updated successfully",
                "success",
                200 ,
                $data->toArray()
            );
        } catch (\Throwable $e){
            return $this->errorResponse($e->getMessage());
        }
    }

    public function destroy(int $id) : JsonResponse {
        try{
            /** @var Delete $useCase */
            $useCase = app()->make(Delete::class);
            $delete = $useCase->perform($id);

            return $this->response(
                "Profile deleted successfully",
                "success",
                200 ,
                [$delete]
            );
        } catch (\Throwable $e){
            return $this->errorResponse($e->getMessage());
        }
    }

    public function fetchUserProfilePermissions() : JsonResponse {
        try{
            $user = request()->user();
            if(!$user || !$user->profile) {
                return $this->errorResponse("User profile not found", 404);
            }

            $permissions = $user->profile->permissions;

            return $this->response(
                "Authorized",
                "success",
                200 ,
                $permissions->toArray()
            );
        } catch (\Throwable $e){
            return $this->errorResponse($e->getMessage());
        }
    }

    public function userProfileCanPermission(string $slug) : JsonResponse {
        try{
            $user = request()->user();
            if(!$user || !$user->profile) {
                return $this->errorResponse("User profile not found", 404);
            }

            // Need to convert Model to Domain to use can() method
            $profileFactory = app()->make(ProfileFactory::class);
            $profileDomain = $profileFactory->buildFromModel($user->profile);

            $can = $profileDomain->can($slug);

            return $this->response(
                "Authorized",
                "success",
                200 ,
                ['can' => $can]
            );
        } catch (\Throwable $e){
            return $this->errorResponse($e->getMessage());
        }
    }
}
