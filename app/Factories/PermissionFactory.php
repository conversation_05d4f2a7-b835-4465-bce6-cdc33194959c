<?php

namespace App\Factories;

use App\Domains\Permission;
use App\Http\Requests\Permission\CreatePermissionRequest;
use App\Http\Requests\Permission\UpdatePermissionRequest;
use App\Models\Permission as PermissionModel;

class PermissionFactory
{
    public function buildFromStoreRequest(CreatePermissionRequest $request) : Permission {
        return new Permission(
            null,
            $request->slug ?? "",
            $request->name ?? "",
            $request->description ?? null,
        );
    }
    public function buildFromUpdateRequest(UpdatePermissionRequest $request) : Permission {
        return new Permission(
            null,
            $request->slug ?? "",
            $request->name ?? "",
            $request->description ?? null,
        );
    }

    public function buildFromModel(PermissionModel $permission) : Permission {
        return new Permission(
            $permission->id ?? null,
            $permission->slug ?? "",
            $permission->name ?? "",
            $permission->description ?? null,
            $permission->created_at ?? null,
            $permission->updated_at ?? null,
        );
    }

    // Helper methods for testing
    public function buildFromArray(array $data, ?int $id = null) : Permission {
        return new Permission(
            $id,
            $data['slug'] ?? "",
            $data['name'] ?? "",
            $data['description'] ?? null,
            $data['created_at'] ?? null,
            $data['updated_at'] ?? null,
        );
    }
}
