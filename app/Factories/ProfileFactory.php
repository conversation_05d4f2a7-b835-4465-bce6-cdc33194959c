<?php

namespace App\Factories;

use App\Domains\Profile;
use App\Http\Requests\Profile\CreateProfileRequest;
use App\Http\Requests\Profile\UpdateProfileRequest;
use App\Models\Profile as ProfileModel;
use App\Factories\PermissionFactory;

class ProfileFactory
{
    private PermissionFactory $permissionFactory;

    public function __construct(PermissionFactory $permissionFactory)
    {
        $this->permissionFactory = $permissionFactory;
    }
    public function buildFromStoreRequest(CreateProfileRequest $request) : Profile {
        return new Profile(
            null,
            $request->organization_id ?? null,
            $request->name ?? "",
            $request->slug ?? "",
            $request->description ?? null,
            $request->is_admin ?? false,
            $request->is_super_admin ?? false,
        );
    }
    public function buildFromUpdateRequest(UpdateProfileRequest $request) : Profile {
        return new Profile(
            null,
            null,
            $request->name ?? "",
            $request->slug ?? "",
            $request->description ?? null,
            $request->is_admin ?? false,
            $request->is_super_admin ?? false,
        );
    }

    public function buildFromModel(ProfileModel $profile) : Profile {
        // Convert permissions to Permission domains
        $permissions = [];
        if ($profile->permissions) {
            foreach ($profile->permissions as $permission) {
                $permissions[] = $this->permissionFactory->buildFromModel($permission);
            }
        }

        return new Profile(
            $profile->id ?? null,
            $profile->organization_id ?? null,
            $profile->name ?? "",
            $profile->slug ?? "",
            $profile->description ?? null,
            $profile->is_admin ?? false,
            $profile->is_super_admin ?? false,
            $profile->created_at ?? null,
            $profile->updated_at ?? null,
            $permissions,
        );
    }

    // Helper methods for testing
    public function buildFromArray(array $data, ?int $id = null) : Profile {
        return new Profile(
            $id,
            $data['organization_id'] ?? null,
            $data['name'] ?? "",
            $data['slug'] ?? "",
            $data['description'] ?? null,
            $data['is_admin'] ?? false,
            $data['is_super_admin'] ?? false,
            $data['created_at'] ?? null,
            $data['updated_at'] ?? null,
            $data['permissions'] ?? [],
        );
    }
}
