<?php

namespace App\Console\Commands\ChatBot;

use Illuminate\Console\Command;
use App\Services\Meta\WhatsApp\ChatBot\Services\ConversationTimeoutService;

class CleanupInactiveConversations extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'chatbot:cleanup-conversations 
                            {--dry-run : Run without making changes}
                            {--force : Force cleanup without confirmation}
                            {--older-than= : Cleanup conversations older than specified hours (default: 24)}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Cleanup inactive ChatBot conversations';

    protected ConversationTimeoutService $conversationTimeoutService;

    public function __construct(ConversationTimeoutService $conversationTimeoutService)
    {
        parent::__construct();
        $this->conversationTimeoutService = $conversationTimeoutService;
    }

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $dryRun = $this->option('dry-run');
        $force = $this->option('force');
        $olderThanHours = $this->option('older-than') ?? 24;

        $this->info('ChatBot Conversation Cleanup');
        $this->info('==================================');

        if ($dryRun) {
            $this->warn('DRY RUN MODE - No changes will be made');
        }

        // Check if cleanup is enabled
        if (!config('chatbot.cleanup.enabled', true)) {
            $this->error('Conversation cleanup is disabled in configuration');
            return Command::FAILURE;
        }

        // Show configuration
        $this->displayConfiguration($olderThanHours, $dryRun);

        // Confirm action unless forced
        if (!$force && !$dryRun) {
            if (!$this->confirm('Do you want to proceed with the cleanup?')) {
                $this->info('Cleanup cancelled');
                return Command::SUCCESS;
            }
        }

        // Perform cleanup
        $this->info('Starting cleanup process...');
        
        try {
            $result = $this->conversationTimeoutService->cleanupInactiveConversations($dryRun);
            $this->displayResults($result);
            
            return Command::SUCCESS;
        } catch (\Exception $e) {
            $this->error('Cleanup failed: ' . $e->getMessage());
            return Command::FAILURE;
        }
    }

    /**
     * Display current configuration
     *
     * @param int $olderThanHours
     * @param bool $dryRun
     */
    protected function displayConfiguration(int $olderThanHours, bool $dryRun): void
    {
        $config = config('chatbot.cleanup', []);
        
        $this->info('Configuration:');
        $this->line("  Cleanup conversations older than: {$olderThanHours} hours");
        $this->line("  Soft delete: " . ($config['soft_delete'] ?? true ? 'Yes' : 'No'));
        $this->line("  Dry run: " . ($dryRun ? 'Yes' : 'No'));
        $this->line('');
    }

    /**
     * Display cleanup results
     *
     * @param array $result
     */
    protected function displayResults(array $result): void
    {
        $this->info('Cleanup Results:');
        $this->line('================');
        
        $this->line("Total conversations found: {$result['total_found']}");
        $this->line("Successfully cleaned: {$result['cleaned']}");
        
        if ($result['errors'] > 0) {
            $this->error("Errors encountered: {$result['errors']}");
        }
        
        if ($result['dry_run']) {
            $this->warn('This was a dry run - no actual changes were made');
        }
        
        $this->line('');
        
        if ($result['cleaned'] > 0) {
            $this->info('✓ Cleanup completed successfully');
        } else {
            $this->info('No conversations needed cleanup');
        }
    }
}
