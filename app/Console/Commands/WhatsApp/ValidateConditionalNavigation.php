<?php

namespace App\Console\Commands\WhatsApp;

use Illuminate\Console\Command;
use App\Repositories\StepRepository;
use App\Services\Meta\WhatsApp\ChatBot\Services\ConditionalNavigationService;
use App\Factories\ChatBot\StepFactory;
use App\Models\Flow;

class ValidateConditionalNavigation extends Command
{
    protected $signature = 'whatsapp:validate-navigation {flow_id?}';
    protected $description = 'Validate conditional navigation in WhatsApp flows';

    public function handle()
    {
        $flowId = $this->argument('flow_id');

        if ($flowId) {
            $this->validateSingleFlow($flowId);
        } else {
            $this->validateAllFlows();
        }

        return 0;
    }

    private function validateSingleFlow(int $flowId)
    {
        $this->info("🔍 Validating Flow ID: {$flowId}");
        $this->info("=====================================");

        $flow = Flow::find($flowId);
        if (!$flow) {
            $this->error("❌ Flow not found: {$flowId}");
            return;
        }

        $this->info("Flow: {$flow->name}");
        $this->info("Description: {$flow->description}");
        $this->info("");

        $stepRepository = app()->make(StepRepository::class);
        $conditionalService = app()->make(ConditionalNavigationService::class);

        // Get all steps in flow
        $steps = $stepRepository->getStepsInFlow($flowId);
        $this->info("📋 Found " . count($steps) . " steps");

        // Validate each step
        $totalErrors = 0;
        foreach ($steps as $step) {
            $this->info("\n🔸 Step: {$step->step} (ID: {$step->id})");

            $errors = $conditionalService->validateStepConditionalNavigation($step);

            if (empty($errors)) {
                $this->info("  ✅ No validation errors");
            } else {
                $totalErrors += count($errors);
                foreach ($errors as $error) {
                    $this->error("  ❌ {$error}");
                }
            }

            // Show conditional paths
            $paths = $conditionalService->getConditionalPaths($step);
            if (!empty($paths)) {
                $this->info("  🔀 Conditional paths:");
                foreach ($paths as $path) {
                    $this->info("    • '{$path['button_text']}' → {$path['target_step']}");
                }
            } else {
                $this->info("  📍 No conditional navigation (uses default next_step)");
            }
        }

        // Overall validation
        $flowErrors = $stepRepository->validateConditionalNavigation($flowId);

        $this->info("\n📊 **Validation Summary:**");
        $this->info("Steps validated: " . count($steps));
        $this->info("Step-level errors: {$totalErrors}");
        $this->info("Flow-level errors: " . count($flowErrors));

        if (empty($flowErrors) && $totalErrors === 0) {
            $this->info("✅ Flow validation passed!");
        } else {
            $this->error("❌ Flow validation failed!");

            if (!empty($flowErrors)) {
                $this->info("\n🚨 Flow-level errors:");
                foreach ($flowErrors as $error) {
                    $this->error("  • {$error}");
                }
            }
        }
    }

    private function validateAllFlows()
    {
        $this->info("🔍 Validating All WhatsApp Flows");
        $this->info("=================================");

        $flows = Flow::all();
        $this->info("Found " . count($flows) . " flows\n");

        $totalFlows = 0;
        $validFlows = 0;
        $totalErrors = 0;

        foreach ($flows as $flow) {
            $totalFlows++;
            $this->info("🔸 Flow: {$flow->name} (ID: {$flow->id})");

            $stepRepository = app()->make(StepRepository::class);
            $errors = $stepRepository->validateConditionalNavigation($flow->id);

            if (empty($errors)) {
                $validFlows++;
                $this->info("  ✅ Valid");
            } else {
                $totalErrors += count($errors);
                $this->error("  ❌ {count($errors)} errors");

                // Show first few errors
                $displayErrors = array_slice($errors, 0, 3);
                foreach ($displayErrors as $error) {
                    $this->error("    • {$error}");
                }

                if (count($errors) > 3) {
                    $this->error("    • ... and " . (count($errors) - 3) . " more errors");
                }
            }
        }

        $this->info("\n📊 **Overall Summary:**");
        $this->info("Total flows: {$totalFlows}");
        $this->info("Valid flows: {$validFlows}");
        $this->info("Invalid flows: " . ($totalFlows - $validFlows));
        $this->info("Total errors: {$totalErrors}");

        if ($validFlows === $totalFlows) {
            $this->info("🎉 All flows are valid!");
        } else {
            $this->error("⚠️  Some flows have validation errors");
            $this->info("\nRun with specific flow ID to see detailed errors:");
            $this->info("php artisan whatsapp:validate-navigation <flow_id>");
        }
    }
}
