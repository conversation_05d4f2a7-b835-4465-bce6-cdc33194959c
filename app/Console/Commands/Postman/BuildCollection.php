<?php

namespace App\Console\Commands\Postman;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Storage;

class BuildCollection extends Command
{
    protected $signature = 'postman:build
                            {--output= : Output file path (default: storage/postman/ObvioAPI.postman_collection.json)}
                            {--source= : Source directory path (default: storage/postman/modular)}
                            {--dry-run : Show what would be built without creating the file}';

    protected $description = 'Build the complete Postman collection from modular JSON files';

    private string $sourcePath;
    private string $outputPath;
    private array $collection = [];

    public function handle(): int
    {
        $this->sourcePath = $this->option('source') ?? storage_path('docs/postman/modular');
        $this->outputPath = $this->option('output') ?? storage_path('docs/postman/ObvioAPI.postman_collection.json');
        $isDryRun = $this->option('dry-run');

        if ($isDryRun) {
            $this->info('🔍 DRY RUN MODE - No files will be created');
            $this->newLine();
        }

        $this->info('🚀 Building Postman collection from modular files...');
        $this->newLine();

        try {
            if (!File::exists($this->sourcePath)) {
                $this->error("Source directory not found: {$this->sourcePath}");
                return Command::FAILURE;
            }

            $this->loadMetadata();
            $this->buildCollection();

            if (!$isDryRun) {
                $this->saveCollection();
                $this->info("✅ Collection built successfully: {$this->outputPath}");
            } else {
                $this->info("✅ Collection structure validated successfully");
                $this->showCollectionSummary();
            }

            return Command::SUCCESS;

        } catch (\Exception $e) {
            $this->error("❌ Failed to build collection: " . $e->getMessage());
            return Command::FAILURE;
        }
    }

    private function loadMetadata(): void
    {
        $metaPath = $this->sourcePath . '/meta.json';

        if (!File::exists($metaPath)) {
            throw new \Exception("Metadata file not found: {$metaPath}");
        }

        $meta = json_decode(File::get($metaPath), true);

        if (json_last_error() !== JSON_ERROR_NONE) {
            throw new \Exception("Invalid JSON in metadata file: " . json_last_error_msg());
        }

        $this->collection = $meta;
        $this->collection['item'] = [];

        $this->info("📋 Loaded collection metadata: {$meta['info']['name']}");
    }

    private function buildCollection(): void
    {
        $foldersPath = $this->sourcePath . '/folders';
        $itemsPath = $this->sourcePath . '/items';

        $folderOrder = [
            'authentication',
            'user_management',
            'webhooks',
            'import',
            'subscription',
            'nfse',
            'inventory',
            'chatbot'
        ];

        foreach ($folderOrder as $folderKey) {
            $this->buildFolder($folderKey, $foldersPath, $itemsPath);
        }

        $this->info("📁 Built collection with " . count($this->collection['item']) . " main folders");
    }

    private function buildFolder(string $folderKey, string $foldersPath, string $itemsPath): void
    {
        $folderFile = $foldersPath . "/{$folderKey}.json";

        if (!File::exists($folderFile)) {
            $this->warn("⚠️  Folder definition not found: {$folderFile}");
            return;
        }

        $folderData = json_decode(File::get($folderFile), true);

        if (json_last_error() !== JSON_ERROR_NONE) {
            $this->warn("⚠️  Invalid JSON in folder file: {$folderFile}");
            return;
        }

        $folder = [
            'name' => $folderData['name'],
            'description' => $folderData['description'] ?? '',
            'item' => []
        ];

        $folderItemsPath = $itemsPath . '/' . $folderKey;

        if (File::exists($folderItemsPath)) {
            $this->loadFolderItems($folder, $folderItemsPath, $foldersPath);
        }

        $this->collection['item'][] = $folder;

        $itemCount = $this->countItems($folder);
        $this->line("  ✓ {$folderData['name']}: {$itemCount} items");
    }

    /**
     * Fully recursive loading of items & subfolders
     */
    private function loadFolderItems(array &$folder, string $folderPath, string $foldersPath): void
    {
        // Direct JSON request files
        foreach (File::files($folderPath) as $file) {
            if ($file->getExtension() === 'json') {
                $itemData = json_decode(File::get($file->getPathname()), true);
                if (json_last_error() === JSON_ERROR_NONE && isset($itemData['request'])) {
                    $folder['item'][] = $itemData;
                }
            }
        }

        // Nested directories (subfolders)
        foreach (File::directories($folderPath) as $directory) {
            $subfolderName = basename($directory);
            $subfolderDefFile = $foldersPath . '/' . basename($folderPath) . '_' . $subfolderName . '.json';

            $subfolder = [
                'name' => $this->getSubfolderName($subfolderName, $subfolderDefFile),
                'item' => []
            ];

            if (File::exists($subfolderDefFile)) {
                $subfolderData = json_decode(File::get($subfolderDefFile), true);
                if (json_last_error() === JSON_ERROR_NONE) {
                    $subfolder['name'] = $subfolderData['name'] ?? $subfolder['name'];
                    if (isset($subfolderData['description'])) {
                        $subfolder['description'] = $subfolderData['description'];
                    }
                }
            }

            // RECURSIVE CALL HERE
            $this->loadFolderItems($subfolder, $directory, $foldersPath);

            if (!empty($subfolder['item'])) {
                $folder['item'][] = $subfolder;
            }
        }
    }

    private function getSubfolderName(string $subfolderName, string $defFile): string
    {
        $displayNames = [
            'users' => '👤 Users',
            'organizations' => '🏢 Organizations',
            'profiles' => '👔 Profiles',
            'telegram' => '📱 Telegram',
            'whatsapp' => '💬 WhatsApp',
            'internal' => '🏢 Internal Subscription',
            'asaas' => '🔗 ASAAS',
            'account' => '🏦 Account',
            'customer' => '👥 Customer',
            'payment' => '💰 Payment',
            'subscription' => '🔄 Subscription',
            'brands' => '🏷️ Brands',
            'products' => '📦 Products',
            'clients' => '🏪 Clients',
            'projects' => '📋 Projects',
            'budgets' => '💰 Budgets',
            'stocks' => '📦 Stocks',
            'sales' => '💰 Sales',
            'items' => '📋 Items',
            'shops' => '🏪 Shops',
            'batches' => '📦 Batches',
            'flows' => '📊 Flows',
            'steps' => '🔗 Steps',
            'components' => '🧩 Components',
            'buttons' => '🔘 Buttons',
            'campaigns' => '📢 Campaigns',
            'messages' => '💬 Messages',
            'templates' => '📄 Templates',
            'parameters' => '⚙️ Parameters',
            'interactions' => '🔄 Interactions',
            'conversations' => '💭 Conversations',
            'exchanged_messages' => '💬 Exchanged Messages',
            'categories' => '📂 Categories',
            'tags' => '🏷️ Tags',
            'analytics' => '📊 Analytics',
            'whatsapp_sync' => '🔄 WhatsApp Sync'
        ];

        return $displayNames[$subfolderName] ?? ucfirst(str_replace('_', ' ', $subfolderName));
    }

    private function countItems(array $folder): int
    {
        $count = 0;
        foreach ($folder['item'] as $item) {
            if (isset($item['request'])) {
                $count++;
            } elseif (isset($item['item'])) {
                $count += $this->countItems($item);
            }
        }
        return $count;
    }

    private function saveCollection(): void
    {
        $json = json_encode($this->collection, JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES);
        if (json_last_error() !== JSON_ERROR_NONE) {
            throw new \Exception("Failed to encode collection as JSON: " . json_last_error_msg());
        }

        $outputDir = dirname($this->outputPath);
        if (!File::exists($outputDir)) {
            File::makeDirectory($outputDir, 0755, true);
        }

        File::put($this->outputPath, $json);
        $fileSize = File::size($this->outputPath);
        $this->info("💾 Collection saved: " . $this->formatBytes($fileSize));
    }

    private function showCollectionSummary(): void
    {
        $this->newLine();
        $this->info("📊 Collection Summary:");
        $this->line("  • Collection Name: {$this->collection['info']['name']}");
        $this->line("  • Main Folders: " . count($this->collection['item']));

        $totalRequests = 0;
        foreach ($this->collection['item'] as $folder) {
            $totalRequests += $this->countItems($folder);
        }

        $this->line("  • Total Requests: {$totalRequests}");
        $this->line("  • Output Path: {$this->outputPath}");
    }

    private function formatBytes(int $bytes): string
    {
        $units = ['B', 'KB', 'MB', 'GB'];
        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);
        $bytes /= pow(1024, $pow);
        return round($bytes, 2) . ' ' . $units[$pow];
    }
}
