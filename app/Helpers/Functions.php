<?php

namespace App\Helpers;

use App\Models\Client;
use App\Models\Organization;
use App\Models\Product;
use App\Models\Sale;
use App\Models\Shop;
use App\Models\Stock;
use App\Models\User;
use App\Repositories\ClientRepository;
use App\Repositories\OrganizationRepository;
use App\Repositories\ProductRepository;
use App\Repositories\SaleRepository;
use App\Repositories\ShopRepository;
use App\Repositories\StockRepository;
use App\Repositories\UserRepository;

class Functions
{
    public const array MODELS = [
        'user' => User::class,
        'client' => Client::class,
        'organization' => Organization::class,
        'sale' => Sale::class,
        'shop' => Shop::class,
        'product' => Product::class,
        'stock' => Stock::class,
    ];

    public const array REPOSITORIES = [
        'user' => UserRepository::class,
        'client' => ClientRepository::class,
        'organization' => OrganizationRepository::class,
        'sale' => SaleRepository::class,
        'shop' => ShopRepository::class,
        'product' => ProductRepository::class,
        'stock' => StockRepository::class,
    ];
}
