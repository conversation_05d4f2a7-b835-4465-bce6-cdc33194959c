<?php

namespace App\UseCases\User;

use Exception;
use App\Domains\User;
use App\Repositories\UserRepository;
use App\Repositories\ProfileRepository;
use Illuminate\Support\Facades\DB;

class AssignAdminProfile
{
    private UserRepository $userRepository;
    private ProfileRepository $profileRepository;

    public function __construct(
        UserRepository $userRepository,
        ProfileRepository $profileRepository
    ) {
        $this->userRepository = $userRepository;
        $this->profileRepository = $profileRepository;
    }

    /**
     * Assign admin profile to a user
     * @param int $userId
     * @param int $organizationId
     * @return User
     * @throws Exception
     */
    public function perform(int $userId, int $organizationId): User
    {
        DB::beginTransaction();

        try {
            // Get the user
            $user = $this->userRepository->fetchById($userId);
            if (!$user) {
                throw new Exception("User not found", 404);
            }

            // Get the admin profile for this organization
            $adminProfile = $this->profileRepository->fetchAdminIntoOrganization($organizationId);
            if (!$adminProfile) {
                throw new Exception("Admin profile not found for this organization", 404);
            }

            // Update user's profile_id
            $user->profile_id = $adminProfile->id;
            $updatedUser = $this->userRepository->update($user);

            DB::commit();

            return $updatedUser;

        } catch (Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }
}
