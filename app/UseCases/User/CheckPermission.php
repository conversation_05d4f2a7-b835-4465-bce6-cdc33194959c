<?php

namespace App\UseCases\User;

use App\Domains\User;
use App\Repositories\UserRepository;

class CheckPermission
{
    private UserRepository $userRepository;

    public function __construct(UserRepository $userRepository) {
        $this->userRepository = $userRepository;
    }

    /**
     * Verifica se o usuário possui determinada permissão
     * @return bool
     */
    public function perform($slug) : bool {
        return $this->userRepository->checkPermission($slug);
    }
}
