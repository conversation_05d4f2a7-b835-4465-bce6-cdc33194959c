<?php

namespace App\UseCases\User;

use App\Repositories\UserRepository;
use App\Repositories\ProfileRepository;
use App\Repositories\PermissionRepository;
use App\Domains\User;

class CheckSpecificUserPermission
{
    private UserRepository $userRepository;
    private ProfileRepository $profileRepository;
    private PermissionRepository $permissionRepository;

    public function __construct(
        UserRepository $userRepository,
        ProfileRepository $profileRepository,
        PermissionRepository $permissionRepository
    ) {
        $this->userRepository = $userRepository;
        $this->profileRepository = $profileRepository;
        $this->permissionRepository = $permissionRepository;
    }

    public function perform(string $permissionSlug): array
    {
        // Get current authenticated user
        $currentUser = auth()->user();

        if (!$currentUser) {
            throw new \Exception('User not authenticated');
        }

        // Fetch user domain
        $userDomain = $this->userRepository->fetchById($currentUser->id);

        if (!$userDomain) {
            throw new \Exception('User not found');
        }

        // Check if permission exists
        $permissionDomain = $this->permissionRepository->fetchBySlug($permissionSlug);

        if (!$permissionDomain) {
            return [
                'user' => [
                    'id' => $userDomain->id,
                    'name' => $userDomain->name,
                    'email' => $userDomain->email
                ],
                'permission' => [
                    'slug' => $permissionSlug,
                    'exists' => false
                ],
                'has_permission' => false,
                'reason' => 'Permission does not exist'
            ];
        }



        // If user has no profile, they have no permissions
        if (!$userDomain->profile_id) {
            return [
                'user' => [
                    'id' => $userDomain->id,
                    'name' => $userDomain->name,
                    'email' => $userDomain->email
                ],
                'permission' => [
                    'id' => $permissionDomain->id,
                    'slug' => $permissionDomain->slug,
                    'name' => $permissionDomain->name,
                    'description' => $permissionDomain->description,
                    'exists' => true
                ],
                'has_permission' => false,
                'reason' => 'User has no profile assigned'
            ];
        }

        // Fetch profile with permissions
        $profileDomain = $this->profileRepository->fetchById($userDomain->profile_id);

        if (!$profileDomain) {
            return [
                'user' => [
                    'id' => $userDomain->id,
                    'name' => $userDomain->name,
                    'email' => $userDomain->email
                ],
                'permission' => [
                    'id' => $permissionDomain->id,
                    'slug' => $permissionDomain->slug,
                    'name' => $permissionDomain->name,
                    'description' => $permissionDomain->description,
                    'exists' => true
                ],
                'has_permission' => false,
                'reason' => 'User profile not found'
            ];
        }

        // Check if profile has the permission using domain logic
        $hasPermission = $profileDomain->can($permissionSlug);
        $reason = '';

        if ($profileDomain->is_super_admin) {
            $hasPermission = true;
            $reason = 'Profile is super admin';
        } elseif ($profileDomain->is_admin) {
            $hasPermission = true;
            $reason = 'Profile is admin';
        } elseif ($hasPermission) {
            $reason = 'Permission explicitly granted to profile';
        } else {
            $reason = 'Permission not granted to profile';
        }

        return [
            'user' => [
                'id' => $userDomain->id,
                'name' => $userDomain->name,
                'email' => $userDomain->email
            ],
            'profile' => [
                'id' => $profileDomain->id,
                'name' => $profileDomain->name,
                'slug' => $profileDomain->slug,
                'is_admin' => $profileDomain->is_admin,
                'is_super_admin' => $profileDomain->is_super_admin
            ],
            'permission' => [
                'id' => $permissionDomain->id,
                'slug' => $permissionDomain->slug,
                'name' => $permissionDomain->name,
                'description' => $permissionDomain->description,
                'exists' => true
            ],
            'has_permission' => $hasPermission,
            'reason' => $reason
        ];
    }
}
