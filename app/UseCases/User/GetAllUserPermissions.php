<?php

namespace App\UseCases\User;

use App\Domains\User;
use App\Repositories\UserRepository;

class GetAllUserPermissions
{
    private UserRepository $userRepository;

    public function __construct(UserRepository $userRepository) {
        $this->userRepository = $userRepository;
    }

    /**
     * Verifica se o usuário possui determinada permissão
     * @return array
     */
    public function perform() : array {
        return $this->userRepository->getAllUserPermissions();
    }
}
