<?php

namespace App\UseCases\User;

use App\Repositories\UserRepository;
use App\Repositories\ProfileRepository;
use App\Domains\User;

class FetchUserPermissions
{
    private UserRepository $userRepository;
    private ProfileRepository $profileRepository;

    public function __construct(
        UserRepository $userRepository,
        ProfileRepository $profileRepository
    ) {
        $this->userRepository = $userRepository;
        $this->profileRepository = $profileRepository;
    }

    public function perform(): array
    {
        // Get current authenticated user
        $currentUser = auth()->user();

        if (!$currentUser) {
            throw new \Exception('User not authenticated');
        }

        // Fetch user domain with profile and permissions
        $userDomain = $this->userRepository->fetchById($currentUser->id);

        if (!$userDomain) {
            throw new \Exception('User not found');
        }

        // If user has no profile, return empty permissions
        if (!$userDomain->profile_id) {
            return [
                'user' => [
                    'id' => $userDomain->id,
                    'name' => $userDomain->name,
                    'email' => $userDomain->email,
                    'organization_id' => $userDomain->organization_id
                ],
                'profile' => null,
                'permissions' => [],
                'can_access_all' => false
            ];
        }

        // Fetch profile with permissions
        $profileDomain = $this->profileRepository->fetchById($userDomain->profile_id);

        if (!$profileDomain) {
            throw new \Exception('User profile not found');
        }

        // Prepare permissions array
        $permissions = [];
        if ($profileDomain->permissions) {
            foreach ($profileDomain->permissions as $permission) {
                $permissions[] = [
                    'id' => $permission->id,
                    'slug' => $permission->slug,
                    'name' => $permission->name,
                    'description' => $permission->description
                ];
            }
        }

        return [
            'user' => [
                'id' => $userDomain->id,
                'name' => $userDomain->name,
                'email' => $userDomain->email,
                'organization_id' => $userDomain->organization_id
            ],
            'profile' => [
                'id' => $profileDomain->id,
                'name' => $profileDomain->name,
                'slug' => $profileDomain->slug,
                'description' => $profileDomain->description,
                'is_admin' => $profileDomain->is_admin,
                'is_super_admin' => $profileDomain->is_super_admin
            ],
            'permissions' => $permissions,
            'can_access_all' => $profileDomain->is_super_admin || $profileDomain->is_admin
        ];
    }
}
