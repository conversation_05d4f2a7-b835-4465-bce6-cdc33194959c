<?php

namespace App\UseCases\Profile;

use App\Repositories\ProfileRepository;

class FetchByOrganization
{
    private ProfileRepository $profileRepository;
    private int $organization_id;

    public function __construct(ProfileRepository $profileRepository, int $organization_id) {
        $this->profileRepository = $profileRepository;
        $this->organization_id = $organization_id;
    }

    /**
     * @return array
     */
    public function perform() : array {
        return $this->profileRepository->fetchByOrganization($this->organization_id);
    }
}
