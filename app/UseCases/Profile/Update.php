<?php

namespace App\UseCases\Profile;

use Exception;
use App\Domains\Profile;
use Illuminate\Support\Facades\DB;
use App\Factories\ProfileFactory;
use App\Repositories\ProfileRepository;
use App\Http\Requests\Profile\UpdateProfileRequest;

class Update
{
    private ProfileRepository $profileRepository;
    private ProfileFactory $profileFactory;

    public function __construct(ProfileRepository $profileRepository, ProfileFactory $profileFactory) {
        $this->profileRepository = $profileRepository;
        $this->profileFactory = $profileFactory;
    }

    /**
     * @param UpdateProfileRequest $request
     * @param int $id
     * @return Profile
     */
    public function perform(UpdateProfileRequest $request, int $id) : Profile {
        $user = request()->user();
        if(!$user || !$user->profile || !$user->profile->is_super_admin){
            throw new Exception(
                "You need super admin privileges to update this Profile.",
                403
            );
        }

        DB::beginTransaction();

        $domain = $this->profileFactory->buildFromUpdateRequest($request);
        $domain->id = $id;

        $profile = $this->profileRepository->update(
            $domain
        );

        DB::commit();

        return $profile;
    }
}
