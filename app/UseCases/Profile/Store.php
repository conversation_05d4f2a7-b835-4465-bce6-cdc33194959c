<?php

namespace App\UseCases\Profile;

use Exception;
use App\Domains\Profile;
use Illuminate\Support\Facades\DB;
use App\Factories\ProfileFactory;
use App\Repositories\ProfileRepository;
use App\Http\Requests\Profile\CreateProfileRequest;

class Store
{
    private ProfileRepository $profileRepository;
    private ProfileFactory $profileFactory;

    public function __construct(ProfileRepository $profileRepository, ProfileFactory $profileFactory) {
        $this->profileRepository = $profileRepository;
        $this->profileFactory = $profileFactory;
    }

    /**
     * @param CreateProfileRequest $request
     * @return Profile
     */
    public function perform(CreateProfileRequest $request) : Profile {
        $user = request()->user();
        if(!$user || !$user->profile || !$user->profile->is_super_admin){
            throw new Exception(
                "You need super admin privileges to create Profiles.",
                403
            );
        }

        DB::beginTransaction();

        $domain = $this->profileFactory->buildFromStoreRequest($request);
        $domain->organization_id = auth()->user()->organization_id; // Ensure the profile is linked to the user's organization
        if($domain->is_super_admin || $domain->is_admin){
            throw new Exception(
                "You cannot create a admin profile.",
                403
            );
        }

        $profile = $this->profileRepository->store($domain);

        DB::commit();

        return $profile;
    }
}
