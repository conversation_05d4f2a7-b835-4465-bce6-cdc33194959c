<?php

namespace App\UseCases\Profile;

use Exception;
use App\Domains\Profile;
use App\Repositories\ProfileRepository;

class CreateDefaultAdmin
{
    private ProfileRepository $profileRepository;

    public function __construct(ProfileRepository $profileRepository) {
        $this->profileRepository = $profileRepository;
    }

    /**
     * Cria o perfil de admin padrão para uma organização
     * @param int $organizationId
     * @return Profile
     */
    public function perform(int $organizationId): Profile {
        if($this->profileRepository->fetchAdminIntoOrganization($organizationId)){
			throw new Exception(
				"This organization already has a admin profile.",
				400
			);
		}
		
		$profile = new Profile(
            null,
			$organizationId,
            'Administrador',
            'admin',
            'Perfil administrativo padrão',
            true,
            false,
            now(),
            now(),
            ['*'] // Permissão total
        );
        // Aqui você pode associar o profile à organização se necessário
        return $this->profileRepository->store($profile);
    }
}
