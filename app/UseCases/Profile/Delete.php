<?php

namespace App\UseCases\Profile;

use App\Repositories\ProfileRepository;
use Exception;

class Delete
{
    private ProfileRepository $profileRepository;

    public function __construct(ProfileRepository $profileRepository) {
        $this->profileRepository = $profileRepository;
    }

    /**
     * @param int $id
     * @return bool
     * @throws Exception
     */
    public function perform(int $id) : bool {
        $organization_id = request()->user()->organization_id;

        $profile = $this->profileRepository->fetchById($id);

        if($profile->organization_id !== $organization_id || $profile->is_super_admin === true || $profile->is_admin === true){
            throw new Exception(
                "You cannot delete this profile.",
                403
            );
        }

        return $this->profileRepository->delete($profile);
    }
}
