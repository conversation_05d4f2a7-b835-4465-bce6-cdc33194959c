<?php

namespace App\UseCases\Profile;

use App\Domains\Filters\OrderBy;
use App\Domains\Filters\ProfileFilters;
use App\Domains\Profile;
use App\Repositories\ProfileRepository;

class GetAll
{
    private ProfileRepository $profileRepository;

    public function __construct(ProfileRepository $profileRepository) {
        $this->profileRepository = $profileRepository;
    }

    /**
     * @return Profile[]
     */
    public function perform(ProfileFilters $filters, OrderBy $orderBy) : array {
        return $this->profileRepository->fetchFromOrganization(
            request()->user()->organization_id,
            $filters,
            $orderBy
        );
    }
}
