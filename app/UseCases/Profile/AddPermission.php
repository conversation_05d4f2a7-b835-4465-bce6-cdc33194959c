<?php

namespace App\UseCases\Profile;

use Exception;
use Illuminate\Support\Facades\DB;
use App\Http\Requests\Profile\AddPermissionRequest;
use App\Repositories\ProfileRepository;
use App\Repositories\PermissionRepository;
use App\Models\Profile as ProfileModel;
use App\Models\Permission as PermissionModel;

class AddPermission
{
    private ProfileRepository $profileRepository;
    private PermissionRepository $permissionRepository;

    public function __construct(
        ProfileRepository $profileRepository,
        PermissionRepository $permissionRepository
    ) {
        $this->profileRepository = $profileRepository;
        $this->permissionRepository = $permissionRepository;
    }

    /**
     * Add permission to a profile
     * @param AddPermissionRequest $request
     * @return array
     * @throws Exception
     */
    public function perform(AddPermissionRequest $request): array
    {
        $user = request()->user();
        if (!$user || !$user->profile || (!$user->profile->is_super_admin && !$user->profile->is_admin)) {
            throw new Exception(
                "You need admin or super admin privileges to manage profile permissions.",
                403
            );
        }

        DB::beginTransaction();

        try {
            // Get profile and permission models for the relationship
            $profileModel = ProfileModel::findOrFail($request->profile_id);
            $permissionModel = PermissionModel::where('slug', $request->permission_slug)->firstOrFail();

            // Validate that profile belongs to user's organization (unless super admin)
            if (!$user->profile->is_super_admin && $profileModel->organization_id !== $user->organization_id) {
                throw new Exception(
                    "You can only manage permissions for profiles in your organization.",
                    403
                );
            }

            // Check if permission is already attached
            if ($profileModel->permissions()->where('permission_id', $permissionModel->id)->exists()) {
                throw new Exception(
                    "Permission '{$request->permission_slug}' is already assigned to this profile.",
                    400
                );
            }

            // Attach permission to profile
            $profileModel->permissions()->attach($permissionModel->id, [
                'created_at' => now(),
                'updated_at' => now()
            ]);

            DB::commit();

            return [
                'success' => true,
                'message' => "Permission '{$request->permission_slug}' added to profile successfully.",
                'profile_id' => $request->profile_id,
                'permission_slug' => $request->permission_slug
            ];

        } catch (Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }
}
