<?php

namespace App\UseCases\Organization;

use Exception;
use App\Domains\Profile;
use App\UseCases\Profile\CreateDefaultAdmin;
use App\Repositories\OrganizationRepository;
use Illuminate\Support\Facades\DB;

class CreateAdminProfile
{
    private OrganizationRepository $organizationRepository;

    public function __construct(OrganizationRepository $organizationRepository) {
        $this->organizationRepository = $organizationRepository;
    }

    /**
     * Create admin profile for an existing organization
     * @param int $organizationId
     * @return Profile
     * @throws Exception
     */
    public function perform(int $organizationId): Profile {
        $user = request()->user();
        if (!$user || !$user->profile || !$user->profile->is_super_admin) {
            throw new Exception(
                "You need super admin privileges to create admin profiles for organizations.",
                403
            );
        }

        DB::beginTransaction();

        try {
            // Verify organization exists
            $organization = $this->organizationRepository->fetchById($organizationId);
            if (!$organization) {
                throw new Exception("Organization not found", 404);
            }

            // Create default admin profile
            /** @var CreateDefaultAdmin $createAdminUseCase */
            $createAdminUseCase = app()->make(CreateDefaultAdmin::class);
            $adminProfile = $createAdminUseCase->perform($organizationId);

            DB::commit();

            return $adminProfile;

        } catch (Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }
}
