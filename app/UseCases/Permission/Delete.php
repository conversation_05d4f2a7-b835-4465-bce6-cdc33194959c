<?php

namespace App\UseCases\Permission;

use Exception;
use App\Repositories\PermissionRepository;

class Delete
{
    private PermissionRepository $permissionRepository;

    public function __construct(PermissionRepository $permissionRepository) {
        $this->permissionRepository = $permissionRepository;
    }

    /**
     * @param int $id
     * @return bool
     * @throws Exception
     */
    public function perform(int $id) : bool {
        $permission = $this->permissionRepository->fetchById($id);

        $user = request()->user();
        if(!$user || !$user->profile || !$user->profile->is_super_admin){
            throw new Exception(
                "You need super admin privileges to delete this permission." ,
                403
            );
        }

        return $this->permissionRepository->delete($permission);
    }
}
