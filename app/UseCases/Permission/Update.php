<?php

namespace App\UseCases\Permission;

use Exception;
use App\Domains\Permission;
use Illuminate\Support\Facades\DB;
use App\Factories\PermissionFactory;
use App\Repositories\PermissionRepository;
use App\Http\Requests\Permission\UpdatePermissionRequest;

class Update
{
    private PermissionRepository $permissionRepository;
    private PermissionFactory $permissionFactory;

    public function __construct(PermissionRepository $permissionRepository, PermissionFactory $permissionFactory) {
        $this->permissionRepository = $permissionRepository;
        $this->permissionFactory = $permissionFactory;
    }

    /**
     * @param UpdatePermissionRequest $request
     * @param int $id
     * @return Permission
     */
    public function perform(UpdatePermissionRequest $request, int $id) : Permission {
        $user = request()->user();
        if(!$user || !$user->profile || !$user->profile->is_super_admin){
            throw new Exception(
                "You need super admin privileges to update this permission.",
                403
            );
        }

        DB::beginTransaction();

        $domain = $this->permissionFactory->buildFromUpdateRequest($request);
        $domain->id = $id;

        $permission = $this->permissionRepository->update(
            $domain
        );

        DB::commit();

        return $permission;
    }
}
