<?php

namespace App\UseCases\Permission;

use Exception;
use App\Domains\Permission;
use Illuminate\Support\Facades\DB;
use App\Factories\PermissionFactory;
use App\Repositories\PermissionRepository;
use App\Http\Requests\Permission\CreatePermissionRequest;

class Store
{
    private PermissionRepository $permissionRepository;
    private PermissionFactory $permissionFactory;

    public function __construct(PermissionRepository $permissionRepository, PermissionFactory $permissionFactory) {
        $this->permissionRepository = $permissionRepository;
        $this->permissionFactory = $permissionFactory;
    }

    /**
     * @param CreatePermissionRequest $request
     * @return Permission
     */
    public function perform(CreatePermissionRequest $request) : Permission {
        $user = request()->user();
        if(!$user || !$user->profile || !$user->profile->is_super_admin){
            throw new Exception(
                "You need super admin privileges to create permissions.",
                403
            );
        }

        DB::beginTransaction();

        $domain = $this->permissionFactory->buildFromStoreRequest($request);

        $permission = $this->permissionRepository->store($domain);

        DB::commit();

        return $permission;
    }
}
