<?php

namespace App\UseCases\Permission;

use Exception;
use App\Domains\Permission;
use App\Repositories\PermissionRepository;

class Get
{
    private PermissionRepository $permissionRepository;

    public function __construct(PermissionRepository $permissionRepository) {
        $this->permissionRepository = $permissionRepository;
    }

    /**
     * @param int $id
     * @return Permission
     * @throws Exception
     */
    public function perform(int $id) : Permission {
        $permission = $this->permissionRepository->fetchById($id);

        return $permission;
    }
}
