<?php

namespace App\UseCases\Permission;

use App\Repositories\PermissionRepository;

class GetAll
{
    private PermissionRepository $permissionRepository;

    public function __construct(PermissionRepository $permissionRepository) {
        $this->permissionRepository = $permissionRepository;
    }

    /**
     * @return array
     */
    public function perform() : array {
        return $this->permissionRepository->fetchAll();
    }
}
