<?php

namespace App\UseCases\Auth;

use App\Domains\User;
use App\Factories\UserFactory;
use App\Http\Requests\Auth\RegisterRequest;
use App\Repositories\UserRepository;
use App\UseCases\Organization\Store;
use App\UseCases\Profile\CreateDefaultAdmin;
use App\UseCases\User\AssignAdminProfile;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class Register
{
    public UserFactory $userFactory;
    public UserRepository $userRepository;

    public function __construct(
        UserFactory $userFactory,
        UserRepository $userRepository
    ){
        $this->userFactory = $userFactory;
        $this->userRepository = $userRepository;
    }

    public function perform(RegisterRequest $request) : ?User {
        DB::beginTransaction();

        $userDomain = $this->userFactory->buildFromRegisterRequest($request);
        if($userDomain->organization){
            /** @var Store $registerUseCase */
            $organizationUseCase = app()->make(Store::class);
            $organizationUseCase->perform($userDomain->organization);
            $userDomain->organization_id = $userDomain->organization->id;

            // Create default admin profile for the new organization
            try {
                /** @var CreateDefaultAdmin $createAdminUseCase */
                $createAdminUseCase = app()->make(CreateDefaultAdmin::class);
                $adminProfile = $createAdminUseCase->perform($userDomain->organization->id);

                // Set the user's profile to the newly created admin profile
                $userDomain->profile_id = $adminProfile->id;
            } catch (\Exception $e) {
                // Log the error but don't fail the registration
                \Log::warning('Failed to create default admin profile during registration', [
                    'organization_id' => $userDomain->organization->id,
                    'error' => $e->getMessage()
                ]);
            }
        }
        $user = $this->userRepository->store(
            $userDomain
        );

        Auth::loginUsingId($user->id);

        $user->token = $request->user()->createToken("token")->plainTextToken;

        DB::commit();

        return $user;
    }
}
