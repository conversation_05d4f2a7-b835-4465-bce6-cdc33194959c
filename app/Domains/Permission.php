<?php

namespace App\Domains;

use Carbon\Carbon;

class Permission
{
    public ?int $id;
    public string $slug;
    public string $name;
    public ?string $description;
    public ?Carbon $created_at;
    public ?Carbon $updated_at;

    public function __construct(
        ?int $id,
        string $slug,
        string $name,
        ?string $description,
        ?Carbon $created_at = null,
        ?Carbon $updated_at = null,
    ){
        $this->id = $id;
        $this->slug = $slug;
        $this->name = $name;
        $this->description = $description;
        $this->created_at = $created_at;
        $this->updated_at = $updated_at;
    }

    public function toArray(): array
    {
        return [
            "id" => $this->id,
            "name" => $this->name,
            "slug" => $this->slug,
            "description" => $this->description,
            "created_at" => $this->created_at?->format("Y-m-d H:i:s"),
            "updated_at" => $this->updated_at?->format("Y-m-d H:i:s")
        ];
    }

    public function toStoreArray(): array
    {
        return [
            "name" => $this->name,
            "slug" => $this->slug,
            "description" => $this->description,
        ];
    }

    public function toUpdateArray(): array
    {
        return [
            "name" => $this->name,
            "slug" => $this->slug,
            "description" => $this->description,
        ];
    }
}
