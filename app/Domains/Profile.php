<?php

namespace App\Domains;

use Carbon\Carbon;

class Profile
{
    public ?int $id;
    public ?int $organization_id;
    public string $name;
    public string $slug;
    public ?string $description;
    public ?bool $is_admin;
    public ?bool $is_super_admin;
    public ?Carbon $created_at;
    public ?Carbon $updated_at;
    /** @var Permission[] $permissions */
    public ?array $permissions;

    public function __construct(
        ?int $id,
        ?int $organization_id,
        string $name,
        string $slug,
        ?string $description,
        ?bool $is_admin,
        ?bool $is_super_admin,
        ?Carbon $created_at = null,
        ?Carbon $updated_at = null,
        ?array $permissions = [],
    ){
        $this->id = $id;
        $this->organization_id = $organization_id;
        $this->name = $name;
        $this->slug = $slug;
        $this->description = $description;
        $this->is_admin = $is_admin;
        $this->is_super_admin = $is_super_admin;
        $this->created_at = $created_at;
        $this->updated_at = $updated_at;
        $this->permissions = $permissions;
    }

    public function toArray(): array
    {
        return [
            "id" => $this->id,
            "organization_id" => $this->organization_id,
            "name" => $this->name,
            "slug" => $this->slug,
            "description" => $this->description,
            "is_admin" => $this->is_admin,
            "is_super_admin" => $this->is_super_admin,
            "created_at" => $this->created_at?->format("Y-m-d H:i:s"),
            "updated_at" => $this->updated_at?->format("Y-m-d H:i:s"),
            "permissions" => $this->permissions,
        ];
    }

    public function toStoreArray(): array
    {
        return [
            "organization_id" => $this->organization_id,
            "name" => $this->name,
            "slug" => $this->slug,
            "description" => $this->description,
            "is_admin" => $this->is_admin,
            "is_super_admin" => $this->is_super_admin,
        ];
    }

    public function toUpdateArray(): array
    {
        return [
            "name" => $this->name,
            "slug" => $this->slug,
            "description" => $this->description,
            "is_admin" => $this->is_admin,
            "is_super_admin" => $this->is_super_admin,
        ];
    }

    public function isAdmin(): bool
    {
        return $this->is_admin;
    }

    public function isSuperAdmin(): bool
    {
        return $this->is_super_admin;
    }

    public function can($slug): bool
    {
        // Super admin always has all permissions
        if ($this->isSuperAdmin()) {
            return true;
        }

        // Admin has all permissions within their organization
        if ($this->isAdmin()) {
            return true;
        }

        // Check if permission exists in the permissions array
        if (!$this->permissions || !is_array($this->permissions)) {
            return false;
        }

        // Check if slug exists in permissions array (permissions is array of Permission domains)
        foreach ($this->permissions as $permission) {
            if (is_object($permission) && isset($permission->slug) && $permission->slug === $slug) {
                return true;
            }
            // Fallback for array format (backward compatibility)
            if (is_string($permission) && $permission === $slug) {
                return true;
            }
        }

        return false;
    }

    public function hasPermission($slug): bool
    {
    // Alias for can($slug)
    return $this->can($slug);
    }
}
