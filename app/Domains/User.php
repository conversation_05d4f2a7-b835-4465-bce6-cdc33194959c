<?php

namespace App\Domains;

use Illuminate\Support\Facades\Hash;

class User
{
    public ?int $id;
    public ?int $profile_id;
    public ?int $organization_id;
    public string $first_name;
    public string $last_name;
    public ?string $name;
    public string $username;
    public string $email;
    public ?string $password;
    public ?string $cpf;
    public ?string $phone;
    public ?string $token;
    public ?Organization $organization;
    /**
     * @var Profile|null
     */
    public ?Profile $profile = null;

    public function __construct(
        ?int $id,
        ?int $profile_id,
        ?int $organization_id,
        string $first_name,
        string $last_name,
        string $username,
        string $email,
        ?string $password,
        ?string $cpf,
        ?string $phone,
        ?string $token = null,
        ?Organization $organization = null,
    ){
        $this->id = $id;
        $this->profile_id = $profile_id;
        $this->organization_id = $organization_id;
        $this->first_name = $first_name;
        $this->last_name = $last_name;
        $this->username = $username;
        $this->email = $email;
        $this->password = $password;
        $this->cpf = $cpf;
        $this->phone = $phone;
        $this->token = $token;
        $this->organization = $organization;
        $this->name = $first_name . ' ' . $last_name;
        // Optionally, you may want to load the Profile object here if available
    }
    /**
     * Checks if the user has authority for a given permission slug.
     */
    public function can($slug): bool
    {
        if ($this->profile instanceof Profile) {
            return $this->profile->can($slug);
        }
        return false;
    }

    public function toArray(): array
    {
        return [
            "id" => $this->id,
            "profile_id" => $this->profile_id,
            "organization_id" => $this->organization_id,
            "name" => $this->name,
            "first_name" => $this->first_name,
            "last_name" => $this->last_name,
            "username" => $this->username,
            "email" => $this->email,
            "cpf" => $this->cpf,
            "phone" => $this->phone,
            "organization" => $this->organization,
        ];
    }

    public function getName(): string {
        return $this->first_name . ' ' . $this->last_name;
    }

    public function setName(): string {
        return $this->name = $this->first_name . ' ' . $this->last_name;
    }

    public function toArrayWithToken(): array
    {
        return array_merge(["token" => $this->token] , $this->toArray());
    }

    public function toStoreArray(): array
    {
        return [
            "profile_id" => $this->profile_id,
            "organization_id" => $this->organization_id,
            "first_name" => $this->first_name,
            "last_name" => $this->last_name,
            "username" => $this->username,
            "email" => $this->email,
            "password" => Hash::make($this->password),
            "cpf" => $this->cpf,
            "phone" => $this->phone,
        ];
    }

    public function toUpdateArray(): array
    {
        $array = [
            "profile_id" => $this->profile_id,
            "first_name" => $this->first_name,
            "last_name" => $this->last_name,
            "username" => $this->username,
            "email" => $this->email,
            "cpf" => $this->cpf,
            "phone" => $this->phone,
        ];

        if($this->password){
            $array["password"] = Hash::make($this->password);
        }

        return $array;
    }
}
