<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Profile extends Model
{
    use HasFactory;

    protected $table = 'profiles';

    protected $fillable = [
        'organization_id',
        'name',
        'slug',
        'description',
        'is_admin',
        'is_super_admin',
    ];

    public function organization(){
        return $this->belongsTo(Organization::class);
    }

    public function users(){
        return $this->hasMany(User::class);
    }

    public function permissions(){
        return $this->belongsToMany(Permission::class, 'profile_permissions');
    }
}
