<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Permission extends Model
{
    use HasFactory;

    protected $table = 'permissions';

    protected $fillable = [
        'organization_id',  //precisa desse organization_id para verificar se o usuário está na mesma organização, ou considera a organização do perfil associado?
        'slug',
        'name',
        'description'
    ];

    public function profile(){
        return $this->belongsToMany(Profile::class, 'profile_permissions');
    }
}
