# Plano Detalhado de Correção do Serviço ASAAS v2

## 📋 Análise dos Problemas Identificados

### 🔍 Problemas Críticos Encontrados

#### 1. **AsaasOrganization Domain vs Model Inconsistência**
- **Problema**: Domain e Model têm campos completamente diferentes
- **Domain atual**: Usa campos como `subscription_status`, `subscription_expires_at`, `courtesy_expires_at`
- **Documentação ASAAS**: SubAccount retorna `id`, `apiKey`, `walletId`, `object`, `dateCreated`
- **Impacto**: Incompatibilidade total com API real do ASAAS

#### 2. **Mapeamento Incorreto de Entidades**
- **AsaasSale** → Cobrança no ASAAS (payments)
- **AsaasSubscription** → Assinatura no ASAAS (subscriptions) 
- **AsaasClient** → Customer no ASAAS (customers)
- **AsaasOrganization** → SubAccount no ASAAS (accounts)

#### 3. **Use Cases com Erros Críticos**

##### IsAllowedToUseSystem
- **Linha 73**: `$asaasOrganization = $organization->asaas;` - Relacionamento não existe
- **Problema**: Usa sistema de cortesia antigo que não existe mais
- **Erro**: Acessa propriedades inexistentes como `subscription_status`, `courtesy_expires_at`

##### CreateCustomer  
- **Linha 289**: Passa QueryBuilder em vez de Domain
- **Linha 104**: `$asaasClient->markSyncError` - Método não existe
- **Problema**: Inconsistência entre Domain e Model

##### CheckSubscriptionStatus
- **Linha 30**: `$organization->asaas_account_id` - Campo não existe no Domain
- **Linha 73**: Usa `Organization` em vez de `OrganizationDomain`
- **Problema**: Mistura Model com Domain

##### CreateSubaccount
- **Linha 138**: `Organization $organization` - Classe indefinida (deveria ser OrganizationDomain)
- **Problema**: Passa parâmetros incorretos para API

##### SyncPaymentStatus
- **Linha 45**: `$sale->organization` - Relacionamento não existe no Domain
- **Problema**: Domain de Sale não tem organization, apenas organization_id

##### CreatePayment
- **Linha 59**: Usa parâmetros incorretos para `asaasService->get()`
- **Problema**: Ordem dos parâmetros está errada

## 🎯 Plano de Correção Detalhado

### **Fase 1: Correção dos Domains**

#### 1.1 AsaasOrganization Domain
```php
// Campos corretos baseados na documentação ASAAS
class AsaasOrganization
{
    public function __construct(
        public readonly ?int $id,
        public readonly int $organization_id,
        public readonly Organization $organization,
        public readonly ?string $asaas_account_id,     // ID da subconta
        public readonly ?string $asaas_api_key,        // API Key da subconta
        public readonly ?string $asaas_wallet_id,      // Wallet ID para splits
        public readonly AsaasEnvironment $asaas_environment,
        public readonly bool $is_active,
        public readonly ?Carbon $last_sync_at,
        public readonly ?array $sync_errors,
        public readonly ?Carbon $created_at,
        public readonly ?Carbon $updated_at,
    ) {}
}
```

#### 1.2 AsaasClient Domain  
```php
// Campos corretos baseados na documentação ASAAS customers
class AsaasClient
{
    public function __construct(
        public readonly ?int $id,
        public readonly int $organization_id,
        public readonly int $client_id,
        public readonly ClientDomain $client,
        public readonly ?string $asaas_customer_id,    // ID do customer no ASAAS
        public readonly ?Carbon $asaas_synced_at,
        public readonly ?array $asaas_sync_errors,
        public readonly ?Carbon $created_at,
        public readonly ?Carbon $updated_at,
    ) {}
}
```

#### 1.3 AsaasSale Domain
```php
// Campos corretos baseados na documentação ASAAS payments
class AsaasSale  
{
    public function __construct(
        public readonly ?int $id,
        public readonly int $organization_id,
        public readonly int $client_id,
        public readonly int $sale_id,
        public readonly SaleDomain $sale,
        public readonly ?string $asaas_payment_id,     // ID do payment no ASAAS
        public readonly ?string $asaas_customer_id,    // Customer que fez o pagamento
        public readonly ?PaymentStatus $payment_status,
        public readonly ?string $billing_type,         // BOLETO, CREDIT_CARD, PIX
        public readonly ?Carbon $due_date,
        public readonly ?Carbon $payment_date,
        public readonly ?float $value,
        public readonly ?float $net_value,
        public readonly ?string $invoice_url,
        public readonly ?string $bank_slip_url,
        public readonly ?string $pix_qr_code,
        public readonly ?Carbon $asaas_synced_at,
        public readonly ?array $asaas_sync_errors,
        public readonly ?array $asaas_webhook_data,
        public readonly ?Carbon $created_at,
        public readonly ?Carbon $updated_at,
    ) {}
}
```

#### 1.4 AsaasSubscription Domain
```php
// Campos corretos baseados na documentação ASAAS subscriptions
class AsaasSubscription
{
    public function __construct(
        public readonly ?int $id,
        public readonly int $subscription_id,
        public readonly Subscription $subscription,
        public readonly ?string $asaas_subscription_id, // ID da subscription no ASAAS
        public readonly ?string $asaas_customer_id,     // Customer da subscription
        public readonly ?SubscriptionStatus $status,    // ACTIVE, EXPIRED, etc
        public readonly ?string $billing_type,
        public readonly ?float $value,
        public readonly ?Carbon $next_due_date,
        public readonly ?string $cycle,                 // MONTHLY, YEARLY
        public readonly ?Carbon $asaas_synced_at,
        public readonly ?array $asaas_sync_errors,
        public readonly ?array $asaas_webhook_data,
        public readonly ?Carbon $created_at,
        public readonly ?Carbon $updated_at,
    ) {}
}
```

### **Fase 2: Correção dos Models**

#### 2.1 AsaasOrganization Model
```php
// Tabela: asaas_organizations
Schema::create('asaas_organizations', function (Blueprint $table) {
    $table->id();
    $table->foreignId('organization_id')->constrained()->onDelete('cascade');
    $table->string('asaas_account_id')->nullable();
    $table->text('asaas_api_key')->nullable();
    $table->string('asaas_wallet_id')->nullable();
    $table->enum('asaas_environment', ['SANDBOX', 'PRODUCTION'])->default('SANDBOX');
    $table->boolean('is_active')->default(true);
    $table->timestamp('last_sync_at')->nullable();
    $table->json('sync_errors')->nullable();
    $table->timestamps();
    
    $table->unique('organization_id');
    $table->index('asaas_account_id');
});
```

#### 2.2 AsaasClient Model
```php
// Tabela: asaas_clients  
Schema::create('asaas_clients', function (Blueprint $table) {
    $table->id();
    $table->foreignId('organization_id')->constrained()->onDelete('cascade');
    $table->foreignId('client_id')->constrained()->onDelete('cascade');
    $table->string('asaas_customer_id')->nullable();
    $table->timestamp('asaas_synced_at')->nullable();
    $table->json('asaas_sync_errors')->nullable();
    $table->timestamps();
    
    $table->unique(['organization_id', 'client_id']);
    $table->index('asaas_customer_id');
});
```

#### 2.3 AsaasSale Model
```php
// Tabela: asaas_sales
Schema::create('asaas_sales', function (Blueprint $table) {
    $table->id();
    $table->foreignId('organization_id')->constrained()->onDelete('cascade');
    $table->foreignId('client_id')->constrained()->onDelete('cascade');
    $table->foreignId('sale_id')->constrained()->onDelete('cascade');
    $table->string('asaas_payment_id')->nullable();
    $table->string('asaas_customer_id')->nullable();
    $table->enum('payment_status', ['PENDING', 'CONFIRMED', 'RECEIVED', 'OVERDUE', 'REFUNDED'])->nullable();
    $table->enum('billing_type', ['BOLETO', 'CREDIT_CARD', 'PIX', 'UNDEFINED'])->nullable();
    $table->date('due_date')->nullable();
    $table->date('payment_date')->nullable();
    $table->decimal('value', 10, 2)->nullable();
    $table->decimal('net_value', 10, 2)->nullable();
    $table->text('invoice_url')->nullable();
    $table->text('bank_slip_url')->nullable();
    $table->text('pix_qr_code')->nullable();
    $table->timestamp('asaas_synced_at')->nullable();
    $table->json('asaas_sync_errors')->nullable();
    $table->json('asaas_webhook_data')->nullable();
    $table->timestamps();
    
    $table->unique(['organization_id', 'sale_id']);
    $table->index('asaas_payment_id');
});
```

#### 2.4 AsaasSubscription Model
```php
// Tabela: asaas_subscriptions
Schema::create('asaas_subscriptions', function (Blueprint $table) {
    $table->id();
    $table->foreignId('subscription_id')->constrained()->onDelete('cascade');
    $table->string('asaas_subscription_id')->nullable();
    $table->string('asaas_customer_id')->nullable();
    $table->enum('status', ['ACTIVE', 'EXPIRED', 'INACTIVE'])->nullable();
    $table->enum('billing_type', ['BOLETO', 'CREDIT_CARD', 'PIX'])->nullable();
    $table->decimal('value', 10, 2)->nullable();
    $table->date('next_due_date')->nullable();
    $table->enum('cycle', ['MONTHLY', 'YEARLY'])->nullable();
    $table->timestamp('asaas_synced_at')->nullable();
    $table->json('asaas_sync_errors')->nullable();
    $table->json('asaas_webhook_data')->nullable();
    $table->timestamps();
    
    $table->unique('subscription_id');
    $table->index('asaas_subscription_id');
});
```

### **Fase 3: Correção dos Use Cases**

#### 3.1 IsAllowedToUseSystem
**Problemas a corrigir:**
- Remover sistema de cortesia antigo
- Usar AsaasOrganizationRepository para buscar dados
- Verificar apenas integração ASAAS e subscription ativa

```php
public function perform(OrganizationDomain $organization): array
{
    // Buscar AsaasOrganization via repository
    $asaasOrg = $this->asaasOrgRepository->findByOrganizationId($organization->id);

    if (!$asaasOrg || !$asaasOrg->hasAsaasIntegration()) {
        return ['allowed' => false, 'reason' => 'no_asaas_integration'];
    }

    // Verificar subscription via AsaasSubscription
    $subscription = $this->asaasSubscriptionRepository->findByOrganizationId($organization->id);

    if (!$subscription || !$subscription->isActive()) {
        return ['allowed' => false, 'reason' => 'no_active_subscription'];
    }

    return ['allowed' => true, 'reason' => 'subscription_active'];
}
```

#### 3.2 CreateCustomer
**Problemas a corrigir:**
- Linha 289: Passar Domain em vez de QueryBuilder
- Linha 104: Implementar método `markSyncError` no Domain
- Usar AsaasClientRepository corretamente

```php
public function perform(ClientDomain $client, ?AsaasEnvironment $environment = null): array
{
    // Buscar organização via repository
    $organization = $this->organizationRepository->findById($client->organization_id);
    $asaasOrg = $this->asaasOrgRepository->findByOrganizationId($organization->id);

    if (!$asaasOrg || !$asaasOrg->hasAsaasIntegration()) {
        throw new AsaasException('Organization has no ASAAS integration');
    }

    // Preparar dados do customer
    $customerData = [
        'name' => $client->name,
        'email' => $client->email,
        'phone' => $client->phone,
        'cpfCnpj' => $client->document,
        // outros campos...
    ];

    // Criar customer no ASAAS
    $response = $this->asaasService->post(
        '/v3/customers',
        $customerData,
        $asaasOrg->asaas_api_key,
        $environment ?: AsaasEnvironment::SANDBOX
    );

    // Criar AsaasClient domain
    $asaasClient = new AsaasClient(
        id: null,
        organization_id: $client->organization_id,
        client_id: $client->id,
        client: $client,
        asaas_customer_id: $response['id'],
        asaas_synced_at: now(),
        asaas_sync_errors: null,
        created_at: now(),
        updated_at: now()
    );

    // Salvar via repository
    $savedAsaasClient = $this->asaasClientRepository->create($asaasClient);

    return [
        'success' => true,
        'asaas_client' => $savedAsaasClient,
        'asaas_response' => $response
    ];
}
```

#### 3.3 CheckSubscriptionStatus
**Problemas a corrigir:**
- Linha 30: Usar AsaasOrganization em vez de Organization
- Linha 73: Corrigir tipo de parâmetro
- Usar repositories corretos

```php
public function perform(OrganizationDomain $organization): array
{
    // Buscar AsaasOrganization
    $asaasOrg = $this->asaasOrgRepository->findByOrganizationId($organization->id);

    if (!$asaasOrg || !$asaasOrg->asaas_account_id) {
        return [
            'success' => false,
            'message' => 'Organization has no ASAAS integration',
            'status' => 'no_integration'
        ];
    }

    // Buscar AsaasSubscription
    $asaasSubscription = $this->asaasSubscriptionRepository->findByOrganizationId($organization->id);

    if (!$asaasSubscription || !$asaasSubscription->asaas_subscription_id) {
        return $this->checkPaymentsFromAsaas($asaasOrg);
    }

    return $this->checkSubscriptionFromAsaas($asaasOrg, $asaasSubscription);
}
```

#### 3.4 CreateSubaccount
**Problemas a corrigir:**
- Linha 138: Usar OrganizationDomain em vez de Organization
- Corrigir parâmetros da API
- Usar campos corretos da documentação ASAAS

```php
public function perform(OrganizationDomain $organization, ?AsaasEnvironment $environment = null): array
{
    // Verificar se já tem integração
    $existingAsaasOrg = $this->asaasOrgRepository->findByOrganizationId($organization->id);
    if ($existingAsaasOrg && $existingAsaasOrg->hasAsaasIntegration()) {
        throw new AsaasException("Organization already has ASAAS account");
    }

    // Preparar dados da subconta conforme documentação ASAAS
    $subaccountData = [
        'name' => $organization->name,
        'email' => $organization->email ?? $organization->name . '@example.com',
        'cpfCnpj' => $organization->document ?? '**************',
        'postalCode' => $organization->postal_code ?? '********',
        'phone' => $organization->phone ?? '***********',
        'incomeValue' => 5000.00,
    ];

    // Criar subconta no ASAAS
    $response = $this->asaasService->post(
        '/v3/accounts',
        $subaccountData,
        null, // Usar token principal
        $environment ?: AsaasEnvironment::SANDBOX
    );

    // Criar AsaasOrganization domain
    $asaasOrg = new AsaasOrganization(
        id: null,
        organization_id: $organization->id,
        organization: $organization,
        asaas_account_id: $response['id'],
        asaas_api_key: $response['apiKey'],
        asaas_wallet_id: $response['walletId'] ?? null,
        asaas_environment: $environment ?: AsaasEnvironment::SANDBOX,
        is_active: true,
        last_sync_at: now(),
        sync_errors: null,
        created_at: now(),
        updated_at: now()
    );

    // Salvar via repository
    $savedAsaasOrg = $this->asaasOrgRepository->create($asaasOrg);

    return [
        'success' => true,
        'asaas_organization' => $savedAsaasOrg,
        'asaas_response' => $response
    ];
}
```

#### 3.5 SyncPaymentStatus
**Problemas a corrigir:**
- Linha 45: Sale domain não tem organization, usar organization_id
- Buscar organization via repository
- Corrigir parâmetros do asaasService

```php
public function perform(SaleDomain $sale, ?AsaasEnvironment $environment = null): array
{
    // Buscar AsaasSale
    $asaasSale = $this->asaasSaleRepository->findBySaleId($sale->id);
    if (!$asaasSale || !$asaasSale->asaas_payment_id) {
        return [
            'success' => false,
            'message' => 'Sale does not have ASAAS payment ID'
        ];
    }

    // Buscar organization e AsaasOrganization
    $organization = $this->organizationRepository->findById($sale->organization_id);
    $asaasOrg = $this->asaasOrgRepository->findByOrganizationId($organization->id);

    if (!$asaasOrg || !$asaasOrg->hasAsaasIntegration()) {
        return [
            'success' => false,
            'message' => 'Organization does not have ASAAS integration'
        ];
    }

    // Buscar payment no ASAAS
    $response = $this->asaasService->get(
        "/v3/payments/{$asaasSale->asaas_payment_id}",
        [],
        $asaasOrg->asaas_api_key,
        $environment ?: AsaasEnvironment::SANDBOX
    );

    // Atualizar AsaasSale com dados do ASAAS
    $updatedAsaasSale = $this->updateAsaasSaleFromResponse($asaasSale, $response);

    return [
        'success' => true,
        'asaas_sale' => $updatedAsaasSale,
        'asaas_response' => $response
    ];
}
```

#### 3.6 CreatePayment
**Problemas a corrigir:**
- Linha 59: Corrigir ordem dos parâmetros do asaasService
- Usar repositories corretos
- Implementar relacionamentos corretos

```php
public function perform(SaleDomain $sale, array $paymentData, ?AsaasEnvironment $environment = null): array
{
    // Buscar organization e client
    $organization = $this->organizationRepository->findById($sale->organization_id);
    $client = $this->clientRepository->findById($sale->client_id);

    // Buscar AsaasOrganization e AsaasClient
    $asaasOrg = $this->asaasOrgRepository->findByOrganizationId($organization->id);
    $asaasClient = $this->asaasClientRepository->findByClientId($client->id);

    if (!$asaasOrg || !$asaasOrg->hasAsaasIntegration()) {
        throw new AsaasException('Organization has no ASAAS integration');
    }

    if (!$asaasClient || !$asaasClient->asaas_customer_id) {
        throw new AsaasException('Client has no ASAAS customer ID');
    }

    // Preparar dados do payment
    $paymentData = [
        'customer' => $asaasClient->asaas_customer_id,
        'billingType' => $paymentData['billing_type'] ?? 'BOLETO',
        'value' => $sale->total_value,
        'dueDate' => $paymentData['due_date'] ?? now()->addDays(7)->format('Y-m-d'),
        'description' => $paymentData['description'] ?? "Venda #{$sale->id}",
        'externalReference' => "sale_{$sale->id}",
    ];

    // Criar payment no ASAAS
    $response = $this->asaasService->post(
        '/v3/payments',
        $paymentData,
        $asaasOrg->asaas_api_key,
        $environment ?: AsaasEnvironment::SANDBOX
    );

    // Criar AsaasSale domain
    $asaasSale = new AsaasSale(
        id: null,
        organization_id: $sale->organization_id,
        client_id: $sale->client_id,
        sale_id: $sale->id,
        sale: $sale,
        asaas_payment_id: $response['id'],
        asaas_customer_id: $asaasClient->asaas_customer_id,
        payment_status: PaymentStatus::from($response['status']),
        billing_type: $response['billingType'],
        due_date: Carbon::parse($response['dueDate']),
        payment_date: $response['paymentDate'] ? Carbon::parse($response['paymentDate']) : null,
        value: $response['value'],
        net_value: $response['netValue'] ?? null,
        invoice_url: $response['invoiceUrl'] ?? null,
        bank_slip_url: $response['bankSlipUrl'] ?? null,
        pix_qr_code: $response['qrCode']['payload'] ?? null,
        asaas_synced_at: now(),
        asaas_sync_errors: null,
        asaas_webhook_data: null,
        created_at: now(),
        updated_at: now()
    );

    // Salvar via repository
    $savedAsaasSale = $this->asaasSaleRepository->create($asaasSale);

    return [
        'success' => true,
        'asaas_sale' => $savedAsaasSale,
        'asaas_response' => $response
    ];
}
```

### **Fase 4: Correção dos Repositories**

#### 4.1 AsaasOrganizationRepository
```php
class AsaasOrganizationRepository
{
    public function findByOrganizationId(int $organizationId): ?AsaasOrganization
    {
        $model = AsaasOrganizationModel::where('organization_id', $organizationId)->first();

        if (!$model) {
            return null;
        }

        return $this->asaasOrganizationFactory->fromModel($model);
    }

    public function create(AsaasOrganization $asaasOrganization): AsaasOrganization
    {
        $model = AsaasOrganizationModel::create($asaasOrganization->toStoreArray());

        return $this->asaasOrganizationFactory->fromModel($model);
    }

    public function update(AsaasOrganization $asaasOrganization): AsaasOrganization
    {
        $model = AsaasOrganizationModel::findOrFail($asaasOrganization->id);
        $model->update($asaasOrganization->toUpdateArray());

        return $this->asaasOrganizationFactory->fromModel($model->fresh());
    }
}
```

#### 4.2 AsaasClientRepository
```php
class AsaasClientRepository
{
    public function findByClientId(int $clientId): ?AsaasClient
    {
        $model = AsaasClientModel::where('client_id', $clientId)->first();

        if (!$model) {
            return null;
        }

        return $this->asaasClientFactory->fromModel($model);
    }

    public function findByOrganizationId(int $organizationId): Collection
    {
        $models = AsaasClientModel::where('organization_id', $organizationId)->get();

        return $models->map(fn($model) => $this->asaasClientFactory->fromModel($model));
    }

    public function create(AsaasClient $asaasClient): AsaasClient
    {
        $model = AsaasClientModel::create($asaasClient->toStoreArray());

        return $this->asaasClientFactory->fromModel($model);
    }
}
```

### **Fase 5: Correção das Factories**

#### 5.1 AsaasOrganizationFactory
```php
class AsaasOrganizationFactory
{
    public function fromModel(AsaasOrganizationModel $model): AsaasOrganization
    {
        return new AsaasOrganization(
            id: $model->id,
            organization_id: $model->organization_id,
            organization: $this->organizationFactory->fromModel($model->organization),
            asaas_account_id: $model->asaas_account_id,
            asaas_api_key: $model->asaas_api_key,
            asaas_wallet_id: $model->asaas_wallet_id,
            asaas_environment: AsaasEnvironment::from($model->asaas_environment),
            is_active: $model->is_active,
            last_sync_at: $model->last_sync_at,
            sync_errors: $model->sync_errors,
            created_at: $model->created_at,
            updated_at: $model->updated_at,
        );
    }

    public function fromArray(array $data): AsaasOrganization
    {
        return new AsaasOrganization(
            id: $data['id'] ?? null,
            organization_id: $data['organization_id'],
            organization: $data['organization'],
            asaas_account_id: $data['asaas_account_id'] ?? null,
            asaas_api_key: $data['asaas_api_key'] ?? null,
            asaas_wallet_id: $data['asaas_wallet_id'] ?? null,
            asaas_environment: $data['asaas_environment'] ?? AsaasEnvironment::SANDBOX,
            is_active: $data['is_active'] ?? true,
            last_sync_at: $data['last_sync_at'] ?? null,
            sync_errors: $data['sync_errors'] ?? null,
            created_at: $data['created_at'] ?? null,
            updated_at: $data['updated_at'] ?? null,
        );
    }
}
```

### **Fase 6: Implementação de Métodos Auxiliares**

#### 6.1 Métodos para AsaasOrganization Domain
```php
// Adicionar ao AsaasOrganization Domain
public function hasAsaasIntegration(): bool
{
    return !empty($this->asaas_account_id) && !empty($this->asaas_api_key);
}

public function toStoreArray(): array
{
    return [
        'organization_id' => $this->organization_id,
        'asaas_account_id' => $this->asaas_account_id,
        'asaas_api_key' => $this->asaas_api_key,
        'asaas_wallet_id' => $this->asaas_wallet_id,
        'asaas_environment' => $this->asaas_environment->value,
        'is_active' => $this->is_active,
        'last_sync_at' => $this->last_sync_at,
        'sync_errors' => $this->sync_errors,
    ];
}

public function toUpdateArray(): array
{
    return [
        'asaas_account_id' => $this->asaas_account_id,
        'asaas_api_key' => $this->asaas_api_key,
        'asaas_wallet_id' => $this->asaas_wallet_id,
        'asaas_environment' => $this->asaas_environment->value,
        'is_active' => $this->is_active,
        'last_sync_at' => $this->last_sync_at,
        'sync_errors' => $this->sync_errors,
    ];
}

public function markSyncError(array $error): void
{
    $this->sync_errors = array_merge($this->sync_errors ?? [], [
        'timestamp' => now()->toISOString(),
        'error' => $error
    ]);
    $this->last_sync_at = now();
}
```

#### 6.2 Métodos para AsaasClient Domain
```php
// Adicionar ao AsaasClient Domain
public function hasAsaasIntegration(): bool
{
    return !empty($this->asaas_customer_id);
}

public function toStoreArray(): array
{
    return [
        'organization_id' => $this->organization_id,
        'client_id' => $this->client_id,
        'asaas_customer_id' => $this->asaas_customer_id,
        'asaas_synced_at' => $this->asaas_synced_at,
        'asaas_sync_errors' => $this->asaas_sync_errors,
    ];
}

public function markSyncError(array $error): void
{
    $this->asaas_sync_errors = array_merge($this->asaas_sync_errors ?? [], [
        'timestamp' => now()->toISOString(),
        'error' => $error
    ]);
}
```

### **Fase 7: Testes Unitários**

#### 7.1 Teste para AsaasOrganizationFactory
```php
class AsaasOrganizationFactoryTest extends TestCase
{
    public function test_creates_domain_from_model()
    {
        $model = AsaasOrganizationModel::factory()->create([
            'asaas_account_id' => 'acc_123',
            'asaas_api_key' => 'key_123',
            'asaas_environment' => 'SANDBOX'
        ]);

        $domain = $this->factory->fromModel($model);

        $this->assertInstanceOf(AsaasOrganization::class, $domain);
        $this->assertEquals('acc_123', $domain->asaas_account_id);
        $this->assertEquals('key_123', $domain->asaas_api_key);
        $this->assertEquals(AsaasEnvironment::SANDBOX, $domain->asaas_environment);
    }

    public function test_has_asaas_integration()
    {
        $domain = new AsaasOrganization(
            id: 1,
            organization_id: 1,
            organization: $this->createMock(Organization::class),
            asaas_account_id: 'acc_123',
            asaas_api_key: 'key_123',
            asaas_wallet_id: null,
            asaas_environment: AsaasEnvironment::SANDBOX,
            is_active: true,
            last_sync_at: null,
            sync_errors: null,
            created_at: now(),
            updated_at: now()
        );

        $this->assertTrue($domain->hasAsaasIntegration());
    }
}
```

### **Resumo das Correções**

1. **Domains**: Alinhados com documentação real do ASAAS
2. **Models**: Campos corretos e relacionamentos adequados
3. **Use Cases**: Corrigidos todos os erros identificados
4. **Repositories**: Implementação consistente com padrões
5. **Factories**: Conversão correta entre Model e Domain
6. **Testes**: Cobertura adequada para validar correções

**Próximos Passos:**
1. Implementar as correções em ordem (Domains → Models → Use Cases)
2. Executar testes para validar cada correção
3. Testar integração real com API do ASAAS
4. Documentar mudanças e criar guia de migração
